{"name": "chat-mis", "private": true, "version": "0.0.0", "type": "module", "packageManager": "pnpm@9.7.0", "scripts": {"dev": "vite", "package": "vite --force", "build": "vite build", "build:test": "vite build", "build:online": "vite build", "report": "vue-tsc --noEmit && export NODE_OPTIONS=--max-old-space-size=8192 && vite build --mode report", "preview": "vite preview", "prepare": "husky install", "tsc": "vue-tsc --noEmit", "lintStaged": "lint-staged", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix", "format": "prettier --write \"./**/*.{html,vue,ts,js,json,md}\"", "preinstall": "npx only-allow pnpm", "commit": "cz", "test": "vitest", "test:run": "vitest run", "coverage": "vitest run --coverage", "webp": "webp"}, "dependencies": {"@element-plus/icons-vue": "^2.0.9", "@traptitech/markdown-it-katex": "^3.6.0", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^10.2.1", "@vueuse/integrations": "^13.1.0", "@vueuse/router": "^11.1.0", "@znzt-fe/axios": "^2.2.0", "@znzt-fe/components": "^2.0.5", "@znzt-fe/hooks": "^6.1.5", "@znzt-fe/utils": "^2.9.1", "@znzt-fe/webp": "^2.5.3", "@zyb-data/stats-pc": "^0.0.11", "copy-to-clipboard": "^3.3.3", "cos-js-sdk-v5": "^1.8.0", "custom-protocol-check": "^1.4.0", "dayjs": "^1.11.8", "decimal.js": "^10.4.3", "diff": "^5.1.0", "diff-match-patch": "^1.0.5", "echarts": "^5.4.2", "element-plus": "^2.8.7", "highlight.js": "^11.8.0", "idb-keyval": "^6", "immer": "^10.0.2", "json-editor-vue": "^0.10.6", "lodash-es": "^4.17.21", "markdown-it": "^13.0.1", "pinia": "^2.0.23", "pinia-plugin-persistedstate": "^3.2.1", "qs": "^6.11.0", "uuid": "^9.0.0", "vue": "^3.4.21", "vue-draggable-plus": "^0.2.6", "vue-router": "^4.1.5", "vue-virtual-scroller": "2.0.0-beta.8", "vuedraggable": "^4.1.0", "wavesurfer.js": "^7.9.4"}, "engines": {"node": ">=16.0.0", "pnpm": "^9.0.0"}, "devDependencies": {"@commitlint/cli": "^17.3.0", "@commitlint/config-conventional": "^17.3.0", "@types/lodash": "^4.14.185", "@types/lodash-es": "^4.17.10", "@types/markdown-it": "^12.2.3", "@types/node": "^18.7.23", "@types/qs": "^6.9.7", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vitest/coverage-istanbul": "^0.34.1", "@vue/test-utils": "^2.4.1", "@znzt-fe/declare": "^2.0.0", "@znzt-fe/lint-config": "^1.1.5", "@zyb/apm-plugin": "2.1.0-beta.6", "autoprefixer": "^10.4.19", "classnames": "^2.3.2", "commitizen": "^4.2.6", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^7.0.0", "eslint": "8.29.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-vue": "9.23.0", "husky": "^8.0.2", "imagemin": "^8.0.1", "imagemin-webp": "^8.0.0", "jsdom": "^22.1.0", "less": "^4.2.0", "lint-staged": "^13.1.0", "postcss": "^8.4.35", "prettier": "^2.8.1", "rollup-plugin-visualizer": "^5.9.2", "tailwindcss": "^3.4.3", "ts-node": "^10.9.2", "typescript": "^5.3.3", "unplugin-auto-import": "^0.16.4", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.25.1", "vite": "^4.3.9", "vite-plugin-mkcert": "^1.17.6", "vitest": "^0.34.1", "vue-tsc": "^2.0.6"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}, "cz-customizable": {"config": "./.cz-config.cjs"}}, "lint-staged": {"*.{js,vue,ts,jsx,tsx}": ["eslint --fix"], "*.{html,css,less,scss,md,ts,vue,js,json,md}": ["prettier --write"]}}