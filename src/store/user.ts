import { getBusSummary } from '@/api/business'
import { GetBusSummaryRet } from '@/api/business/type'
import { getUserInfo } from '@/api/user'
import { BusinessListItem, BusinessUserStatus, RoleId } from '@/api/user/type'
import zs from '@/plugin/zs'
let pendingResolve: ((value: unknown) => void) | undefined = undefined
interface Current {
  status: number
  businessId: number
  businessCode: string
  businessName: string
  businessNeedAduit: number
  businessStatus: number
  isOffline: boolean
  isReal: boolean
  roleName: string
  roleStatus: number
  roleId: RoleId | -1
}

export const pendingLogin = () => new Promise((resolve) => (pendingResolve = resolve))
export const useUserStore = defineStore('user', {
  state: () => {
    return {
      name: '',
      zhName: '',
      roleName: '',
      isGuest: false,
      businessList: [] as BusinessListItem[],
      userCount: 0,
      current: {
        roleId: -1,
        status: -1,
        businessId: -1,
        businessCode: '',
        businessName: '',
        businessNeedAduit: -1,
        businessStatus: -1,
        isOffline: false,
        isReal: false,
        roleName: '',
        roleStatus: -1
      } as Current, // 如果为空则表示无权限 如果有内容则根据内容中status判断
      summary: undefined as GetBusSummaryRet | undefined,
      login: false,
      adminList: [] as string[],
      isRefreshBusiness: false /** 是否更新keepalive */,
      isSystemAdmin: false
    }
  },
  persist: {
    key: 'user',
    storage: localStorage,
    paths: ['businessList', 'current']
  },
  getters: {
    isBusAdmin: (state) =>
      state.current.roleId === RoleId.BusAdmin || state.current.roleId === RoleId.BusSuperAdmin,
    isSuperAdmin: (state) => state.current.roleId === RoleId.SuperAdmin,
    isBusSuperAdmin: (state) => state.current.roleId === RoleId.BusSuperAdmin
  },
  actions: {
    async getUserInfo() {
      const data = await getUserInfo({
        withBusNameList: true
      })
      if (!data) return
      const {
        uname = '',
        businessList = [],
        zhName,
        isGuest,
        nameList = [],
        userCount = 0,
        isSystemAdmin
      } = data
      zs.login(uname)
      this.name = uname
      this.isSystemAdmin = isSystemAdmin
      this.zhName = zhName
      this.isGuest = isGuest
      this.businessList = businessList
      const index = businessList.findIndex((item) => +item.businessId === this.current.businessId)
      if (~index) {
        this.setCurrent(businessList[index])
      } else {
        this.setCurrent(businessList[0])
      }
      this.adminList = nameList
      this.userCount = userCount
      this.login = true
      if (pendingResolve) {
        pendingResolve(true)
      }
    },
    updateCurrent(data: Current) {
      this.isRefreshBusiness = true
      this.setCurrent(data)
    },
    setCurrent(data: Current) {
      this.current = {
        ...data
      }
      if (data?.status !== BusinessUserStatus.Approved) {
        this.summary = undefined
        return
      }
      this.updateBusSummary()
    },
    resetRefreshBusiness() {
      this.isRefreshBusiness = false
    },
    async updateBusSummary() {
      // 超级管理员
      if (!this.current.businessId) {
        this.summary = undefined
        return
      }
      try {
        const data = await getBusSummary({
          businessId: this.current.businessId,
          withUserUsage: true
        })
        this.summary = {
          ...data
        }
      } catch {
        this.summary = undefined
      }
    }
  }
})
export default useUserStore
