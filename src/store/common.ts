import { HandSrvRet, HandsrvParams } from '@/api/model/type'
import { getBusinessOptional } from '@/api/business'
import { BusinessOptionalRet, ModelItem } from '@/api/business/type'
import { getOptionList } from '@/api/sk'
import { OptionListRet } from '@/api/sk/type'
import { getModelHandsrv } from '@/api/model'

export type Config = BusinessOptionalRet & OptionListRet

interface CommonStoreState {
  handsrvList: HandSrvRet
  config: Config
}

export const useCommonStore = defineStore('config', {
  state: (): CommonStoreState => {
    return {
      config: {
        accountList: [],
        businessList: [],
        modelList: [],
        companyList: [],
        channelList: []
      },
      handsrvList: {}
    }
  },
  getters: {},
  actions: {
    async getConfig() {
      const data = await getBusinessOptional()
      const { channelList } = await getOptionList()
      Object.assign(this.config, data || {}, {
        channelList
      })
      this.config.modelList = data.modelList
        .map((model: ModelItem) => {
          return {
            ...model,
            disabled: model.status === 2
          }
        })
        .sort((a, b) => a.status - b.status)
    },
    async getModelHandsrv(params: HandsrvParams) {
      const data = await getModelHandsrv(params)
      this.handsrvList = data || {}
    }
  }
})
export default useCommonStore
