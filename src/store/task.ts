import router from '@/router'
import {
  GetModelParamsParams,
  ModelParamsItemStatus,
  type ModelParamsItem
} from '@/api/business/type'
import { getModelParams } from '@/api/business'
import {
  batchRunTask,
  createTask,
  delDetail,
  deleteTask,
  getBatchDetail,
  getTaskDetail,
  getTaskFolder,
  getTaskListByFolder,
  getTaskStatus,
  runTask,
  searchDetail,
  updateTask,
  updateTaskStatus
} from '@/api/task'
import { CompanyList } from '@/hooks/useModelTree'
import {
  BatchDetailParams,
  BatchRunTaskParams,
  CreateTaskParams,
  DelDetailParams,
  Folder,
  ListFolderItem,
  RunTaskParams,
  SearchDetailListItem,
  TaskDetailRet,
  TaskListByFolderParams,
  TaskStatusParams,
  UpdateTaskParams,
  UpdateTaskStatus
} from '@/api/task/type'
export interface FolderMapValue extends ListFolderItem, TaskDetailRet {
  edit: boolean
  folderId: number
}
interface IState {
  list: Folder[]
  folderMap: Map<number, FolderMapValue[]>
  taskResultMap: Map<number, SearchDetailListItem[]>
  active?: number
  models: ModelParamsItem[]
}
export const useTaskStore = defineStore('task', {
  state: (): IState => ({
    list: [],
    folderMap: new Map(),
    taskResultMap: new Map(),
    active: void 0,
    models: []
  }),
  getters: {
    companyList: (state) =>
      state.models
        .reduce<CompanyList>((pre, now) => {
          let group = pre.find((item) => item.value === now.modelCategory)
          if (!group) {
            group = { value: now.modelCategory, label: now.modelCategory, modelList: [] }
            pre.push(group)
          }
          group.modelList!.push({
            value: now.model,
            label: now.model,
            name: now.model,
            disabled: now.status !== ModelParamsItemStatus['Effective']
          })
          return pre
        }, [])
        .map((item) => ({ ...item, disabled: !item.modelList?.length })),
    companyListId: (state) => {
      return state.models
        .reduce<CompanyList>((pre, now) => {
          let group = pre.find((item) => item.value === now.modelCategory)
          if (!group) {
            group = { value: now.modelCategory, label: now.modelCategory, modelList: [] }
            pre.push(group)
          }
          group.modelList!.push({
            value: now.id,
            label: now.model,
            name: now.model,
            disabled: now.status !== ModelParamsItemStatus['Effective'],
            status: now.status
          })
          group.modelList = (group.modelList || [])
            .sort((a: any, b: any) => a.label - b.label)
            .sort((a: any, b: any) => a.status - b.status)
          return pre
        }, [])
        .map((item) => ({ ...item, disabled: !item.modelList?.length }))
    }
  },
  actions: {
    async getModelParams(params: GetModelParamsParams) {
      const data = await getModelParams(params)
      const models = data.list || []
      this.models = models
    },
    async getTaskFolder() {
      const data = await getTaskFolder()
      const { list = [] } = data
      this.list = list
      this.list.forEach((item) => {
        const { id } = item
        this.folderMap.set(id, [])
      })
    },
    async getTaskListByFolderId(params: TaskListByFolderParams) {
      const data = await getTaskListByFolder(params)
      const { list = [] } = data
      const newList = list.map((item) => ({
        ...item,
        folderId: params.folderId
      })) as unknown as FolderMapValue[]
      this.folderMap.set(params.folderId, newList)
    },
    async createTask(params: CreateTaskParams) {
      return await createTask(params)
    },
    async reloadRoute(id?: number) {
      await router.replace({ name: 'Task', params: { id } })
    },
    async updateTitle(params: UpdateTaskParams) {
      await updateTask(params)
    },
    async update(params: UpdateTaskParams) {
      await updateTask(params)
    },
    getTaskById(id?: number) {
      const defaultValue = {} as FolderMapValue
      if (!id) return defaultValue
      let target: FolderMapValue | undefined
      this.folderMap.forEach((value) => {
        if (target) {
          return
        }
        target = value.find((item) => item.taskId === id)
      })
      return target || defaultValue
    },
    async deleteTask(taskId: number) {
      await deleteTask({
        taskId
      })
    },
    async getTaskDetail(taskId?: number) {
      if (!taskId) return
      const data = await getTaskDetail({
        taskId,
        withSearchParam: true
      })
      const target = this.getTaskById(taskId)
      Object.assign(target, data)
    },
    async runTask(params: RunTaskParams) {
      await runTask({
        ...params
      })
    },
    async batchRunTask(params: BatchRunTaskParams) {
      await batchRunTask({
        ...params
      })
    },
    async getTaskResult() {
      const data = await searchDetail({
        taskId: this.active!
      })
      this.taskResultMap.set(this.active!, data.list || [])
    },
    getResultListByTaskId(id?: number) {
      const defaultValue = [] as SearchDetailListItem[]
      if (!id) return defaultValue
      const list = this.taskResultMap.get(id) || defaultValue
      return list
    },
    async delDetail(params: DelDetailParams) {
      await delDetail(params)
    },
    async exportDetail(ids: number[]) {
      const link = document.createElement('a')
      link.href = `/openmis/task/exportdetail?ids=${ids.join(',')}`
      link.click()
    },
    async getBatchDetail(params: BatchDetailParams) {
      return getBatchDetail(params)
    },
    async getTaskStatus(params: TaskStatusParams) {
      return getTaskStatus(params)
    },
    async updateTaskStatus(params: UpdateTaskStatus) {
      return updateTaskStatus(params)
    }
  }
})
export default useTaskStore
