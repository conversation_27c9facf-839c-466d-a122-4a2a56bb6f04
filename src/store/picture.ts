import { useGetImageGuide, useImageGenList } from '@/api/imagelab'
import { GetGuideRet, ImageGenListItem } from '@/api/imagelab/type'
import { suffixWebp } from '@/utils'
import { cloneDeep } from 'lodash-es'

export const usePictureStore = defineStore('picture', () => {
  const { mutate: getImageGenList } = useImageGenList({
    onSuccess: (res) => {
      res.list.forEach((item) => (item.webpImageUrl = item.originImageUrl + suffixWebp))
      imageGenList.value = cloneDeep(res.list || [])
      if (!res.list.length) showImageGuide.value = true
    }
  })
  const { mutate: getImageGuide } = useGetImageGuide({
    onSuccess(res) {
      res.imageList.forEach((item) => (item.webpImageUrl = item.originImageUrl + suffixWebp))
      imageGuide.value = res
    }
  })
  const imageGuide = ref<GetGuideRet | undefined>()
  const init = () => {
    getImageGenList({})
    getImageGuide({})
  }
  const imageGenList = ref<ImageGenListItem[]>([])
  const showImageGuide = ref(false)
  // 是否全部缩略图加载完毕
  const [pending, setPending] = useToggle(true)
  return {
    imageGenList,
    getImageGuide,
    imageGuide,
    showImageGuide,
    pending,
    setPending,
    getImageGenList,
    init
  }
})
