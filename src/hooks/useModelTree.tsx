import useUserStore from '@/store/user'
import { useGetModelParams } from '@/api/business'
import useTaskStore from '@/store/task'
import ModelTreeSelect from '@/components/model-tree-select'

export type CompanyList = CompanyListItem[]
export interface CompanyListItem {
  label: string
  value: string | number
  name?: string
  modelList?: CompanyListItem[]
  disabled?: boolean
  status?: number
}

export const useModelTree = (onChange: () => void) => {
  const model = ref<string[]>([])
  const { current } = useUserStore()
  const taskStore = useTaskStore()
  invoke(async () => {
    await until(current).toMatch((value) => !!value.businessId)
    taskStore.getModelParams({ business: current.businessCode })
  })

  const { data: modelMap, isLoading: modelMapIsLoading } = useGetModelParams(
    { business: current.businessCode },
    { enabled: !!current.businessCode }
  )
  const TreeSelect = () => (
    <ModelTreeSelect
      data={taskStore.companyList}
      modelValue={model.value}
      multiple
      onChange={(e: string[]) => {
        model.value = e
        onChange()
      }}
    />
  )
  return {
    modelMapIsLoading,
    modelMap,
    companyList: taskStore.companyList,
    TreeSelect,
    model
  }
}
