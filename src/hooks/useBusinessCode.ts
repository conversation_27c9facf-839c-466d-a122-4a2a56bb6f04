import useUserStore from '@/store/user'

export const useBusinessCode = () => {
  const { current } = storeToRefs(useUserStore())
  return current.value.businessCode
}

export const useBusinessId = () => {
  const { current } = storeToRefs(useUserStore())
  return current.value.businessId
}

export function useOpenUrl() {
  const businessCode = useBusinessCode()
  const openUrl = (url: string, option?: Record<string, any>) => {
    const config = {
      businessCode,
      ...option
    }
    const query = qs.stringify(config)
    window.open(url + '?' + query)
  }
  return {
    openUrl
  }
}
