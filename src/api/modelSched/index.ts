import axios from '@/plugin/axios'
import type {
  GetModelSchedListParams,
  GetModelSchedListRet,
  UpdateModelSchedParams,
  BaseModelSchedParams,
  ModelSchedListItem,
  GetModelSchedModelsRet,
  GetModelSchedModelsParams,
  GetModelSchedHandsrvsParams,
  GetModelSchedHandsrvsRet,
  ModelSchedBaseParams
} from './type'
import { MutationFn } from '@znzt-fe/axios'
const { mutationGet, mutationPost } = axios('model/schedpackage')
/**
 * 获取套餐列表
 */
export const useGetModelSchedList: MutationFn<GetModelSchedListParams, GetModelSchedListRet> = (
  options
) => mutationGet('list', options)
/**
 * 获取套餐详情
 */
export const useGetModelSchedDetail: MutationFn<BaseModelSchedParams, ModelSchedBaseParams> = (
  options
) => mutationGet('detail', options)
/**
 * 删除套餐
 */
export const useDelModelSched: MutationFn<BaseModelSchedParams, {}> = (options) =>
  mutationPost('del', options)
/**
 * 设置默认套餐
 */
export const useSetDefaultModelSched: MutationFn<BaseModelSchedParams, {}> = (options) =>
  mutationPost('setdefault', options)
/**
 * 新增套餐
 */
export const useAddModelSched: MutationFn<ModelSchedListItem, {}> = (options) =>
  mutationPost('add', options)

/**
 * 更新套餐
 */
export const useUpdateModelSched: MutationFn<UpdateModelSchedParams, {}> = (options) =>
  mutationPost('update', options)

/**
 * 获取业务线下或者模型组下可配置的模型组
 */
export const useGetModelSchedModels: MutationFn<
  GetModelSchedModelsParams,
  GetModelSchedModelsRet
> = (options) => mutationGet('models', options)

/**
 * 超管/业务线-获取模型的调度方以及资源池列表
 */
export const useGetModelSchedPools: MutationFn<
  GetModelSchedHandsrvsParams,
  GetModelSchedHandsrvsRet
> = (options) => mutationGet('pools', options)
