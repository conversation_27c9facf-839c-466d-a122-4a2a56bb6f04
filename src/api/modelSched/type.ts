export interface GetModelSchedListParams {
  type?: number // 类型  0 获取全部类型   1  只获取base套餐  2 获取系统套餐
}
export interface GetModelSchedListRet {
  total: number
  list: ModelSchedListItem[]
}
export interface ModelSchedListItem {
  id: number //id
  name: string //套餐名称
  type: number //是否是基础套餐
  createTime: number // 创建时间
  updateTIme: number // 更新时间
  opUname: string // 更新人
  isDefault: number // 是否是默认套餐
}
export interface BaseModelSchedParams {
  id?: number // 套餐id
  businessId?: number // 业务线id
}

export interface changeModelConfigItem {
  pre: string
  after: string
}
export interface HandSrvItem {
  handSrv: string
  privatePoolCode: string
  rate: number
  changeModelId?: number
  changeModelConfig?: changeModelConfigItem[]
}

export interface ModelSchedConfigItem {
  modelId: number
  isCustom: boolean
  useModelSchedPackageId?: string
  handSrvs?: HandSrvItem[]
  modelName?: string
}
export interface ModelSchedBaseParams {
  name: string //套餐名称
  desc?: string // 描述
  type: number //是否是基础套餐
  useModelSchedPackageId?: string // 默认套餐类型
  configs: ModelSchedConfigItem[]
}
export interface UpdateModelSchedParams extends ModelSchedBaseParams, BaseModelSchedParams {}

export interface GetModelSchedModelsParams {
  businessId?: number
}
export interface ModelItem {
  id: number
  name: string
  scene: string
  status: number
}
export interface HandSrvItemBase {
  name: string
  canPrivate: boolean
  key: string
}
export interface ModelGroupItem {
  id: number
  name: string
  scene: string
  models: ModelItem[]
  handSrvs: HandSrvItemBase[]
}
export interface GetModelSchedModelsRet {
  list: ModelGroupItem[]
}
export interface GetModelSchedHandsrvsParams extends GetModelSchedModelsParams {
  modelId: number
}

export interface PrivatePoolItemBase {
  name: string
  isDefault: boolean
  code: string
}
export interface GetModelSchedHandsrvsRet {
  pools: Record<string, PrivatePoolItemBase[]>
}
