import axios from '@/plugin/axios'
import type {
  AdminModelListParams,
  AdminModelListRet,
  DelAdminModelParams,
  GetAdminModelOptionsRet,
  AddAdminModelParams,
  UpdateAdminModelParams,
  ModelControlParams
} from './type'
import { MutationFn, QueryFn } from '@znzt-fe/axios'
const { get, queryGet, mutationPost } = axios('admin/model')

/** 模型列表 */
export const getAdminModelList = (data: AdminModelListParams) =>
  get<AdminModelListRet>('list', data)

/** 选项 */
export const useGetAdminModelOptions: QueryFn<{}, GetAdminModelOptionsRet> = (options) =>
  queryGet('options', options)

/** 删除模型 */
export const useDelAdminModel: MutationFn<DelAdminModelParams> = (options) =>
  mutationPost('del', options)

/** 新增模型 */
export const useAddAdminModel: MutationFn<AddAdminModelParams> = (options) =>
  mutationPost('add', options)

/** 编辑模型 */
export const useUpdateAdminModel: MutationFn<UpdateAdminModelParams> = (options) =>
  mutationPost('update', options)

export const getHandConfigList = (data: any) => get<AdminModelListRet>('handconfig', data)

export const useUpdateAdminModelStrategy = () => mutationPost('handconfig')

export const useModelControl: MutationFn<ModelControlParams> = (options) =>
  mutationPost('control', options)
