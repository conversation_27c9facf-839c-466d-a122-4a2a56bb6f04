import axios from '@/plugin/axios'
import { MutationFn } from '@znzt-fe/axios'
import {
  GetTaskTplRet,
  TaskFolderRet,
  DeleteTaskParams,
  TaskListByFolderParams,
  TaskListByFolderRet,
  CreateTaskParams,
  UpdateTaskParams,
  CreateTaskRet,
  UpdateTaskRet,
  RunTaskParams,
  RunTaskRet,
  DelDetailParams,
  BatchDetailParams,
  BatchDetailRet,
  TaskDetailParams,
  TaskDetailRet,
  TaskStatusParams,
  TaskStatusRet,
  UpdateTaskStatus,
  SearchDetailParams,
  SearchDetailRet,
  RetryTaskParams,
  RetryTaskRet,
  BatchRunTaskParams,
  BatchRunTaskRet,
  RetryApiBatchParams,
  RetryApiBatchRet,
  CancelApiBatchParams,
  CancelApiBatchRet,
  CancelBatchTaskParams,
  CancelBatchTaskRet
} from './type'
const { post, mutationGet, mutationPost } = axios('task')

/**  任务列表文件夹 */
export const getTaskFolder = () => post<TaskFolderRet>('folder')

/**  任务列表 */
export const getTaskListByFolder = (data: TaskListByFolderParams) =>
  post<TaskListByFolderRet>('list', data)

/**  新建任务 */
export const createTask = (data: CreateTaskParams) => post<CreateTaskRet>('create', data)

/**  更新任务 */
export const updateTask = (data: UpdateTaskParams) => post<UpdateTaskRet>('update', data)

/**  删除任务 */
export const deleteTask = (data: DeleteTaskParams) => post('del', data)

/**  任务批次明细 */
export const getTaskDetail = (data: TaskDetailParams) => post<TaskDetailRet>('detail', data)

/**  执行任务 */
export const runTask = (data: RunTaskParams) => post<RunTaskRet>('run', data)

/**  apiBatch执行 */
export const batchRunTask = (data: BatchRunTaskParams) =>
  post<BatchRunTaskRet>('apibatch/run', data)

/**  任务执行批次检索 */
export const searchDetail = (data: SearchDetailParams) =>
  post<SearchDetailRet>('searchdetail', data)

/**  删除任务批次明细 */
export const delDetail = (data: DelDetailParams) => post('deldetail', data)

/**  任务批次详情 */
export const getBatchDetail = (data: BatchDetailParams) => post<BatchDetailRet>('batchdetail', data)

/**  任务批次明细状态 */
export const getTaskStatus = (data: TaskStatusParams) => post<TaskStatusRet>('detailstatus', data)

/**  修改任务批次信息 */
export const updateTaskStatus = (data: UpdateTaskStatus) => post('batchupdate', data)

/** 跑批任务重试 */
export const retryTask = (data: RetryTaskParams) =>
  post<RetryTaskRet>('promptbatch/failretry', data)

/** 跑批任务取消 */
export const cancelBatchTask = (data: CancelBatchTaskParams) =>
  post<CancelBatchTaskRet>('promptbatch/cancel', data)

/** api跑批任务重试 */
export const retryApiBatch = (data: RetryApiBatchParams) =>
  post<RetryApiBatchRet>('apibatch/retry', data)

/** 任务模板 */
export const useGetTaskTpl: MutationFn<{}, GetTaskTplRet> = (options) => mutationGet('tpl', options)

/** 取消apiBatch */
export const useCancelApiBatch: MutationFn<CancelApiBatchParams, CancelApiBatchRet> = (options) =>
  mutationPost('apibatch/cancel', options)

/** 获取模型排队情况 */
export const useGetModelQueue = () => mutationGet<any>('apibatch/queue/model')
