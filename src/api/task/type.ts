import { UniversalListResult } from '@znzt-fe/declare'

export interface GetTaskTplRet {
  newApiBatch: TplApiBatch
  newBatch: TplBatch
}
export interface TplBatch {
  xlsxUrl: string
  jsonUrl: string
}

export interface TplApiBatch {
  jsonlUrl: string
}

export type TaskFolderRet = UniversalListResult<Folder>
export interface Folder {
  id: number
  name: string
  sort: number
  startTime: number
  endTime: number
  cnt: number
}

export interface DeleteTaskParams {
  taskId: number
}

export interface TaskListByFolderParams {
  folderId: number
}

export type TaskListByFolderRet = UniversalListResult<ListFolderItem>
export interface ListFolderItem {
  taskId: number
  name: string
  taskType: TaskTypeEnum
  promptBatch: PromptBatch
}
export interface PromptBatch {
  batchId: string
  batchStatus: number
  failReason: string
  success: number
  fail: number
  total: number
  modelProcess: Array<{
    modelName: string
    total: number
    success: number
    fail: number
  }>
}

export interface CreateTaskParams {
  name: string
  taskType: TaskTypeEnum
}
export interface CreateTaskRet {
  taskId: number
}
export const enum TaskTypeEnum {
  Task = 1,
  Batch,
  ApiBatch
}
export interface UpdateTaskParams {
  name?: string
  taskId: number
  content?: TaskContent
}
export interface UpdateTaskRet {
  taskId: number
}
export interface TaskContent {
  msg: string
  repeatTimes: number
  vars: TaskVar[]
  models: Model[]
  modelParams: ModelParam
  uploadId: number
}

export interface Model {
  id: number
  name: string
  desc: string
}
export interface TaskVar {
  name: string
  content: string
  split: string
}
export type ModelParam = Record<string | number, ModelParamAll>
export interface ModelParamAll {
  temperature?: number
  max_tokens?: number
  top_p?: number
  frequency_penalty?: number
  presence_penalty?: number
  penalty_score?: number
  adapter_name?: string
  candidateCount?: number
  top_k?: number
  max_new_tokens?: number
}
export interface RunTaskParams {
  taskId: number
  content: TaskContent
  fileName?: string
}
export interface BatchRunTaskParams {
  batchId: string
  uploadId: string
  fileName?: string
}
export interface BatchRunTaskRet {
  batchId: string
}
export interface RunTaskRet {
  batchId: number
}

export interface DelDetailParams {
  taskId: number
  ids: number[]
}

export interface BatchDetailParams {
  taskId: number
  batchId: string
}

export interface BatchDetailRet {
  batchInfo: BatchInfo
}
export interface BatchInfo {
  id: number
  taskId: number
  taskType: number
  batchId: string
  name: string
  vars: BatchVars
  times: number[]
  models: number[]
  status: number
  failReason: string
  beenRead: number
  runTime: number
  businessCode: string
  ownerUname: string
  content: TaskContent
  deleted: number
  createTime: number
  updateTime: string
}
export type BatchVars = BatchVar[]
export interface BatchVar {
  name: string
  values: string[]
}

export interface TaskDetailParams {
  taskId: number
  withSearchParam: boolean
}
export interface TaskDetailRet {
  taskInfo: TaskInfo
  searchParam: SearchParam
  promptBatch: PromptBatch
  apiBatch: ApiBatch
}
export interface ApiBatch {
  batchId: string
  batchStatus: number
  in_progress_at: string // 开始执行时间
  completed_at: string // 完成时间
  canceling_at: string // 取消时间
  canceled_at: string // 实际取消时间
  failed_at: string // 失败时间
  expired_at: string // 过期时间
  inputFile: string
  outputFile: string
  failReason: string
  total: number // 请求总数
  completed: number // 请求成功数
  failed: number // 请求失败数
  total_tokens: number // 总消耗token
  completion_tokens: number // 输出消耗token
  prompt_tokens: number // 输入消耗token
  subBatch?: Array<Record<string, string | number>>
  actualStatus: number
  actualStatusDesc: string
  estimateExecTime: string
}
export interface TaskInfo {
  id: number
  taskId: number
  taskType: number
  name: string
  businessCode: string
  ownerUname: string
  content: TaskContent
  deleted: number
  createTime: number
  updateTime: string
}
export interface SearchParam {
  list: SearchParamItem[]
}
export interface SearchParamItem {
  batchId: number
  name: string
  vars: BatchVars
  times: number[]
  models: number[]
}
export interface TaskStatusParams {
  taskId: number
}
export interface TaskStatusRet {
  taskId: number
  detailStatus: number
  batchId: number
  msg: string
  initNum: number
  sucNum: number
  failNum: number
  allNum: number
}

export interface UpdateTaskStatus {
  taskId: number
  batchId: number
  setRead: boolean
}

export interface SearchDetailParams {
  taskId: number
  batchIds?: number[]
  times?: number[]
  models?: number[]
  vars?: BatchVars
}
export interface SearchDetailRet {
  cnt: number
  list: SearchDetailListItem[]
}
export interface SearchDetailListItem extends TaskBatchDetail {
  modelName: string
  batchName: string
  updateTimestamp: number
}

export interface TaskBatchDetail {
  id: number
  taskId: number
  taskType: number
  batchId: number
  param: BatchDetailParam
  time: number
  modelId: number
  apiParam: string
  apiResult: string
  status: number
  excelRowId: number
  businessCode: string
  ownerUname: string
  deleted: number
  createTime: number
  updateTime: string
}
export interface BatchDetailParam {
  modelParam: ModelParam
  vars: Record<string, string>
}
export interface RetryTaskParams {
  taskId: number
  batchId: string
}
export interface RetryTaskRet {
  resetNum: number
}

export interface RetryApiBatchParams {
  taskId: number
  batchId: string
}
export interface RetryApiBatchRet {
  resetNum: number
}

export interface CancelApiBatchParams {
  taskId: number
  batchId: string
}
export interface CancelApiBatchRet {
  batchId: string
}

export interface CancelBatchTaskParams {
  taskId: number
  batchId: string
}
export interface CancelBatchTaskRet {
  batchId: string
}
