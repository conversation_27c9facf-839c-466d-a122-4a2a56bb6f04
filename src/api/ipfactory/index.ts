import axios from '@/plugin/axios'
const { get, post } = axios('ipfactory')

/**  ip厂商列表 */
export const getIpFactoryList = (data: any) => get<any>('list', data)

/**  新增ip厂商 */
export const addFactory = (data: any) => post<any>('create', data)

/**  调控ip厂商 */
export const factoryControl = (data: any) => post<any>('control', data)

/**  修改ip厂商 */
export const updateFactory = (data: any) => post<any>('update', data)
