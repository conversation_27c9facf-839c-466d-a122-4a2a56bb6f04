import axios from '@/plugin/axios'
const { get,  } = axios('admin/user')

interface AdminUserListParams {
  pageNum: number
  pageSize: number,
  uname: string
}

interface AdminUserListRet {
  total: number,
  list: Array<AdminUserItem>
}
interface AdminUserItem{
  businessName: string,
  roleName: string,
  uname: string,
  zhName: string
}
/** 管理员用户列表 */
export const getAdminUserList = (data: AdminUserListParams) =>
  get<AdminUserListRet>('list', data)