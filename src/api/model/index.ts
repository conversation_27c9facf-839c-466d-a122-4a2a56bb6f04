import axios from '@/plugin/axios'
import type {
  HandSrvRet,
  GetPrivatePoolParams,
  GetPrivatePoolRet,
  HandsrvParams,
  GetModelPriceRet
} from './type'
import { MutationFn } from '@znzt-fe/axios'
const { get, post, mutationGet } = axios('model')
export const getModelList = (data: any) => get<any>('list', data)
export const removeModelById = (data: any) => post<any>('del', data)
export const addModel = (data: any) => post<any>('add', data)
export const updateModel = (data: any) => post<any>('update', data)
export const testModel = (data: any) => post<any>('test', data)
export const getModelHandsrv = (data: HandsrvParams) => get<HandSrvRet>('handsrv', data)
export const controlModelStatus = (data: any) => post<any>('control', data)
export const useGetPrivatePool: MutationFn<GetPrivatePoolParams, GetPrivatePoolRet> = (options) =>
  mutationGet('privatepool', options)

/** 获取模型价格 */
export const useGetModelPrice: MutationFn<{}, GetModelPriceRet> = (options) =>
  mutationGet('price', options)
