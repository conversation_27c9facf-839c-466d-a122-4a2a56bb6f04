import { UpdateModelStrategyConfigHandSrv } from '../business/type'

export type HandSrvRet = Record<
  string,
  {
    handSrvs: HandSrv[]
    modelVersions: {
      id: number
      name: string
    }[]
    scene: string
    defaultHandConfig: UpdateModelStrategyConfigHandSrv[]
  }
>

export interface HandSrv {
  key: string
  value: string
  canPrivate: boolean // 是否可以私有
  prex: string
  accoType: number
  skTypes: number[]
  modelVersions: string
}

export interface GetPrivatePoolParams {
  businessId?: number // 业务线id
  model: string // 模型名: web-gpt-4 or web-gpt-3.5-turbo
}
export type GetPrivatePoolRet = Record<string, PrivatePoolItem[]>
export interface PrivatePoolItem {
  name: string //业务线名称
  code: string //业务线code
  isDefault: boolean //是否是默认私有资源池
}

export interface HandsrvParams {
  businessId: number
}

export interface GetModelPriceRet {
  list: PriceItem[]
}

export interface PriceItem {
  id: number
  name: string
  modelCompanyName: string
  logoUrl: string
  isFree: number
  promptPrice: number
  completionPrice: number
  isCn: boolean
  rate: number
  cnPromptPrice: number // 接口不返回
  cnCompletionPrice: number // 接口不返回
}
