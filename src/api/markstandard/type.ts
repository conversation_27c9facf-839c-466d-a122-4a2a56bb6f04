import { UniversalListResult } from '@znzt-fe/declare'

export type MarkStandardListRet = UniversalListResult<MarkStandardListItem>
export interface MarkStandardListItem {
  id: string
  name: string
  content: string
  createTime: number
}

export interface CreateMarkStandardParams {
  name: string
  content: string
}

export interface EditMarkStandardParams {
  id: string
  name: string
  content: string
}

export interface DelMarkStandardParams {
  id: string
}

export interface MarkStandardDetailParams {
  id: string
}
