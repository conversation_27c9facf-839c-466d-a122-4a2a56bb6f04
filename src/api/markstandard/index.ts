import axios from '@/plugin/axios'
import type {
  CreateMarkStandardParams,
  DelMarkStandardParams,
  EditMarkStandardParams,
  MarkStandardDetailParams,
  MarkStandardListItem,
  MarkStandardListRet
} from './type'
import { MutationFn, QueryFn } from '@znzt-fe/axios'
const { mutationPost, post, queryPost } = axios('markstandard')

/** 标注标注列表 */
export const getMarkStandardList = () => post<MarkStandardListRet>('list')

export const useGetMarkStandardList: QueryFn<{}, MarkStandardListRet> = () => queryPost('list')

/** 标注标注详情 */
export const useGetMarkStandardDetail: MutationFn<
  MarkStandardDetailParams,
  MarkStandardListItem
> = (options) => mutationPost('detail', options)

/** 添加标注标注 */
export const useCreateMarkStandard: MutationFn<CreateMarkStandardParams> = (options) =>
  mutationPost('create', options)

/** 编辑标注标注 */
export const useEditMarkStandard: MutationFn<EditMarkStandardParams> = (options) =>
  mutationPost('edit', options)

/** 删除标注标注 */
export const useDelMarkStandard: MutationFn<DelMarkStandardParams> = (options) =>
  mutationPost('del', options)
