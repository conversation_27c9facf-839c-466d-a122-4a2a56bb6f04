import axios from '@/plugin/axios'
import { MutationFn } from '@znzt-fe/axios'
import type {
  GetGuideRet,
  ImageGenParams,
  ImageGenRet,
  ImageGenListRet,
  ImageGenDelParams
} from './type'
const { mutationGet, mutationPost } = axios('imagelab')

// 引导语
export const useGetImageGuide: MutationFn<{}, GetGuideRet> = (options) =>
  mutationGet('guide', options)

// 图片生成
export const useImageGen: MutationFn<ImageGenParams, ImageGenRet> = (options) =>
  mutationPost('imagegen', options)

// 文生图，历史记录
export const useImageGenList: MutationFn<{}, ImageGenListRet> = (options) =>
  mutationGet('imagegenlist', options)

// 删除文生图
export const useImageGenDel: MutationFn<ImageGenDelParams> = (options) =>
  mutationPost('del', options)
