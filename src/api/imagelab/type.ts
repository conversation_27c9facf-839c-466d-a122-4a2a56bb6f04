import { UniversalListResult, Empty } from '@znzt-fe/declare'

export interface GetGuideRet {
  imageList: ImageListItem[]
  guidText: GuidText
}
export interface ImageListItem {
  thumbnailImageUrl: string
  originImageUrl: string
  webpImageUrl: string
}

export interface GuidText {
  tipTitle: string
  tipContent: string
  exampleTitle: string
  exampleContent: string
}

export interface ImageGenParams {
  prompt: string
  style: Style // 图像样式
  quality: Quality // 图像质量
}

export type Style = Empty<'vivid' | 'nature'>
export type Quality = Empty<'standard' | 'hd'>
export interface ImageGenRet {
  url: string
  id: number
}

export type ImageGenListRet = UniversalListResult<ImageGenListItem>
export interface ImageGenListItem {
  thumbnailImageUrl: string
  originImageUrl: string
  webpImageUrl: string
  prompt: string
  id: number
}
export interface ImageGenDelParams {
  id: number
}
