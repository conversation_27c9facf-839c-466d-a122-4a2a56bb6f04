import axios from '@/plugin/axios'
const { get, post } = axios('apisubbatch')

interface GlobalResponse {
  failedCount: number
  runningCount: number
  successCount: number
}

export interface BusinessItem {
  key: string
  value: string
}

interface BusinessResponse {
  list: BusinessItem[]
}

interface UserItem {
  value: string
  label: string
}

interface UserResponse {
  list: UserItem[]
}

export interface TaskItem {
  id: number
  businessCode: string
  businessName: string
  modelName: string
  uname: string
  status: number
  execStatus: number
  total: number
  completed: number
  failed: number
  createAt: number
  completedAt: number
  finishAt?: number
  inputUrl: string
  outputUrl: string
  promptTokens: number
  completionTokens: number
  totalTokens: number
  priority: number
}

interface ModelItem {
  value: string
  key: string
}

interface TaskListResponse {
  list: TaskItem[]
  total: number
}

interface ModelListResponse {
  list: ModelItem[]
}

export interface BusinessRequest {
  model?: string
  aggregationStatus?: number
}

export interface UserRequest {
  businessCode?: string
  model?: string
  aggregationStatus?: number
}

export const getGlobal = () => get<GlobalResponse>('global')
export const getBusiness = (params?: BusinessRequest): Promise<BusinessResponse> => {
  return get<BusinessResponse>('business', params)
}
export const getUser = (params?: UserRequest): Promise<UserResponse> => {
  return get<UserResponse>('user', params)
}
export const getUserTaskList = (params: {
  businessCode?: string
  uname?: string
  aggregationStatus?: number
  model?: string
  pageNum: number
  pageSize: number
}) => get<TaskListResponse>('list', params)
export const getModelList = (params?: { aggregationStatus?: number }) =>
  get<ModelListResponse>('model', params)

export interface UpdateTaskStatusResponse {
  result: number
}

export enum OpEnum {
  stop = 1,
  restart = 2
}
export const updateTaskStatus = (params: { subBatchId: number; op: OpEnum }) =>
  post<UpdateTaskStatusResponse>('exec', params)

export const updatePriority = (params: { subBatchId: number; priority: number }) =>
  post<UpdateTaskStatusResponse>('priority', params)
