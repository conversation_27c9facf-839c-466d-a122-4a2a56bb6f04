import axios from '@/plugin/axios'
import type {
  GetMarkObserverDetailParams,
  GetMarkObserverDetailRet,
  UpdateObserverParams,
  GetTaskRoleParams,
  GetTaskRoleRet,
  UpdateTaskRole
} from './type'
import { MutationFn } from '@znzt-fe/axios'
const { mutationGet, mutationPost } = axios('markrole')

/** 标注观察员列表 */
export const useGetMarkObserverDetail: MutationFn<
  GetMarkObserverDetailParams,
  GetMarkObserverDetailRet
> = (data) => mutationGet('observerdetail', data)

/** 修改标注观察员 */
export const useUpdateObserver: MutationFn<UpdateObserverParams> = (data) =>
  mutationPost('observerupdate', data)

/** 获取标注任务角色信息 */
export const useGetTaskRole: MutationFn<GetTaskRoleParams, GetTaskRoleRet> = (data) =>
  mutationGet('getTaskRole', data)

/** 修改标注任务角色信息 */
export const useUpdateTaskRole: MutationFn<UpdateTaskRole> = (data) =>
  mutationPost('updateTaskRole', data)
