import { UniversalListResult } from '@znzt-fe/declare'

export interface GetMarkObserverDetailParams {
  markTaskId: number
}
export type GetMarkObserverDetailRet = UniversalListResult<string>

export interface UpdateObserverParams {
  observeUnames: string[]
  markTaskId: number
}

export const enum RoleId {
  Admin = 1, // 管理员
  Owner, // 所有者
  Observer, // 观察员
  QI // 质检员
}
export interface GetTaskRoleParams {
  markTaskId: number
  roleId: RoleId
}
export type GetTaskRoleRet = UniversalListResult<TaskRoleItem>
export interface TaskRoleItem {
  uname: string
  roleId: RoleId
}
export interface UpdateTaskRole {
  markTaskId: number
  roleId: RoleId
  unames: string[]
}
