import axios from '@/plugin/axios'
import type {
  CreateMarkTaskParams,
  CreateMarkTaskRet,
  DelMarkTaskParams,
  GetMarkTaskListParams,
  GetMarkTaskListRet,
  ImportMarkTaskParams,
  MarkResultListDetailParams,
  MarkResultListDetailRet,
  MarkResultListParams,
  MarkResultListRet,
  MarkTaskOptionsParams,
  MarkTaskOptionsRet,
  MarktaskTemplateRet,
  MarktaskdetailParams,
  MarktaskdetailRet,
  ResultUpdateParams,
  MarktaskUpdateParams,
  InitUnameListItem,
  MarktaskChartParams,
  MarktaskOverlapParams,
  MarktaskOverlapRet,
  DatasetsOptionsRet,
  UpdateGsbParams,
  MarkEditHistoryParams,
  MarkEditHistoryRet,
  ChartsOptionsRet,
  ChartsSelfOptionsRet,
  MarkSelfChartRet,
  MarktaskChartRet,
  GetAnnotatorStatusParams,
  GetAnnotatorStatusRet,
  AddAnnotatorParams,
  RedistributeParams
} from './type'
import type { MutationFn, QueryFn } from '@znzt-fe/axios'
import { produce } from 'immer'
const { post, mutationPost, get, queryPost, mutationGet } = axios('marktask')

/** 标注任务列表 */
export const getMarkList = (data: GetMarkTaskListParams) => post<GetMarkTaskListRet>('list', data)

/** 新建标注任务选项 */
export const getMarkTaskOptions = (data: MarkTaskOptionsParams) =>
  post<MarkTaskOptionsRet<InitUnameListItem>>('option', data).then(
    (res) =>
      produce(res, (draft) => {
        draft.unameList.forEach((item) => {
          Reflect.set(item, 'name', item.id)
        })
      }) as MarkTaskOptionsRet
  )

/** 新建标注任务 */
export const useCreateMarkTask: MutationFn<CreateMarkTaskParams, CreateMarkTaskRet> = (options) =>
  mutationPost('create', options)

/** 删除标注任务 */
export const useDelMarkTask: MutationFn<DelMarkTaskParams> = (options) =>
  mutationPost('del', options)

/** 重新执行标注任务 */
export const useRetryMarkTask: MutationFn<DelMarkTaskParams> = (options) =>
  mutationPost('retry', options)

/** 标注任务导入xlsx */
export const importMarkTask = (data: ImportMarkTaskParams) => post('import', data)

/** 标注任务明细列表 */
export const useMarktaskdetailList: MutationFn<MarktaskdetailParams, MarktaskdetailRet> = (
  options
) => mutationPost('detaillist', options)

/** 修改任务详情 */
export const useResultUpdate: MutationFn<ResultUpdateParams> = (options) =>
  mutationPost('resultupdate', options)

/** 标注任务结果列表 */
export const useGetMarkResultList: MutationFn<MarkResultListParams, MarkResultListRet> = (
  options
) => mutationPost('resultlist', options)

/** 获取单个任务，用于刷新 */
export const useGetMarkTaskDetail: MutationFn<
  MarkResultListDetailParams,
  MarkResultListDetailRet
> = (options) => mutationPost('detail', options)

/** 标注模版 */
export const getMarktaskTemplate = () => post<MarktaskTemplateRet>('template')

export const useGetMarktaskTemplate: QueryFn<{}, MarktaskTemplateRet> = () => queryPost('template')

/** 修改标注任务 */
export const useMarktaskUpdate: MutationFn<MarktaskUpdateParams> = (options) =>
  mutationPost('update', options)

/** 获取图数据 */
export const useGetMarktaskChart: MutationFn<MarktaskChartParams, MarktaskChartRet> = (options) =>
  mutationPost('charts', options)

/** 获取筛选项数据 */
export const useGetChartsOptions: MutationFn<MarktaskChartParams, ChartsOptionsRet> = (data) =>
  mutationPost('chartsOptions', data)

/** 获取数据集选项 */
export const useGetMarkSearchOptions: MutationFn<{}, DatasetsOptionsRet> = (data) =>
  mutationPost('options', data)

/** 任务overlap列表 */
export const useGetMarktaskOverlap: MutationFn<MarktaskOverlapParams, MarktaskOverlapRet> = (
  data
) => mutationPost('overlap', data)

/** GSB打分 */
export const useUpdateGsb: MutationFn<UpdateGsbParams> = (data) => mutationPost('gsb', data)

/** 标注任务编辑历史 */
export const getMarkEditHistory = (data: MarkEditHistoryParams) =>
  get<MarkEditHistoryRet>(`markedithistory`, data)

/** 获取自建图数据 */
export const useGetMarkSelfChart: MutationFn<MarktaskChartParams, MarkSelfChartRet> = (options) =>
  mutationPost('selfcharts', options)

/** 获取自建筛选项数据 */
export const useGetChartsSelfOptions: MutationFn<MarktaskChartParams, ChartsSelfOptionsRet> = (
  data
) => mutationPost('chartsSelftOptions', data)

/** 任务标注状态 */
export const useGetAnnotatorStatus: MutationFn<GetAnnotatorStatusParams, GetAnnotatorStatusRet> = (
  data
) => mutationGet('annotatorstatus', data)

/** 新增标注员 */
export const useAddAnnotator: MutationFn<AddAnnotatorParams> = (data) =>
  mutationPost('addannotator', data)

/** 重新分配 */
export const useRedistribute: MutationFn<RedistributeParams> = (data) =>
  mutationPost('redistribute', data)
