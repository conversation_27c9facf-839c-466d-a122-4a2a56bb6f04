import type { ScoreType } from '@/views/mark/util'
import type { Empty, UniversalListParams, UniversalListResult } from '@znzt-fe/declare'

export type GetMarkTaskListParams = UniversalListParams & GetMarkTaskListParamQuery
export interface GetMarkTaskListParamQuery {
  ownerUname?: string // 创建人员
  annotator?: string // 参与者
  dataSetId?: Empty // 数据集id
  markTypeFilter: MarkTypeFilter // 标注类型筛选
  taskName: string // 任务名称
  categoryId?: Empty // 任务类别
  createStartTime?: Empty // 创建开始时间
  createEndTime?: Empty // 创建结束时间
  labelStyle?: Empty // 标签类型
  fullTaskStatus?: Empty<MarkTaskStatus.inMark | MarkTaskStatus.markDone> // 整体任务状态
}

export const enum LabelType {
  Create = 1,
  Mark,
  Observer
}
export const enum MarkTypeFilter {
  Automatic = 1,
  Manual
}

export const enum MarkTaskStatus {
  execute = 1,
  error,
  success,
  // WaitForMark,
  inMark = 5,
  markDone
}

export type GetMarkTaskListRet = UniversalListResult<MarkTaskListItem>
export const enum LockType {
  lock = 1,
  unLock
}
export const enum PublishResult {
  public = 1,
  private = 2
}
export type MarkTaskListItem = {
  id: number
  ownerUname: string
  markCategory: string
  models: ModelsItem[]
  status: MarkTaskStatus // 状态 1初始化 2执行失败 3执行成功
  deleted: number
  failReason: string
  createTime: number
  halfScore: boolean // 是否支持半星
  updateTime: string
  repetitionRatio: number // 重复发放比例
  markTaskType: string // 任务类型名称
  markScoreType: string // 任务分数类型名称
  markLanguageType: string // 任务语言类型名称
  detailCnt: number // 文本数量
  currentProgress: string // 当前进度
  isMark: boolean // 是否可标注
  isOwner: boolean // 是否所有者
  lock: LockType // 是否锁定
  publishResult: PublishResult // 是否公开结果
  note: string // 标注标准
  desc: string
  businessCode: string
  name: string
  scoreType: ScoreType // 打分类型
  languageType: number // 语言类型
  type: MarkType // 标注任务类型
  uploadId: string
  deadLine: string // 截止时间 时间戳
  isMarkTag: boolean // 是否标注标签
  startTime: string // 任务开始时间
  stopTime: string // 任务结束时间
  annotators: string[] // 标注员
  isObserver: boolean // 是否是观察员
  isQi: boolean // 是否质检员
  questionDisplayMode: QuestionDisplayMode
}
export const enum MarkType {
  NlpSingle = 1,
  Common,
  DownStream,
  Self
}

interface ModelsItem extends CreateModelsItem {
  name: string
}
export interface ModelsItemParams {
  temperature: number
  max_tokens: number
  top_p: number
  frequency_penalty: number
  presence_penalty: number
  corpusId: number
  variables: null
  useQALib: boolean
}

export interface MarkTaskOptionsParams {
  businessId: number
}

export interface MarkTaskOptionsRet<T = UnameListItem> {
  unameList: T[] // 业务线用户列表
  taskTypeList: TaskTypeListItem[] // 任务类型列表
  scoreTypeList: ScoreTypeListItem[] // 评分类型列表
  languageTypeList: LanguageTypeListItem[] // 语言类型列表
  categoryList: CategoryListItem[] // 标注类别标签列表
  questionDisplayModeList: QuestionDisplayModeListItem[] // 题目展示模式
  annotationRoleList: AnnotationRoleListItem[] // 标注角色列表
  annotationGranularityList: AnnotationGranularityListItem[] // 标注粒度
  allocateGranularityList: AllocateGranularityListItem[] // 分配规则
}
export type ScoreTypeListItem = TaskTypeListItem
export type LanguageTypeListItem = TaskTypeListItem
export type CategoryListItem = TaskTypeListItem
export type UnameListItem = InitUnameListItem & TaskTypeListItem
export type QuestionDisplayModeListItem = TaskTypeListItem
export type AnnotationRoleListItem = TaskTypeListItem
export type AnnotationGranularityListItem = TaskTypeListItem
export type AllocateGranularityListItem = TaskTypeListItem
export interface InitUnameListItem {
  id: string
  check: boolean
}
export interface TaskTypeListItem {
  id: number
  name: string
}

export interface CreateMarkTaskParams {
  scoreType: Empty<ScoreType> // 打分类型
  allocateGranularity: Empty
  deadLine: string // 截止时间 时间戳
  type: Empty<MarkType> // 标注任务类型
  uploadId: string
  desc: string
  languageType: Empty // 语言类型
  halfScore: boolean
  name: string
  businessCode: string
  isMarkTag: boolean // 是否标注标签
  repetitionRatio: number
  models: CreateModelsItem[]
  unames: string[] // 标注员
  observers: string[] // 观察员
  qiList: string[] // 质检员
  questionSetting: QuestionSetting[]
  standardId: string // 标注标准id
  isShowSource: boolean // 是否展示生成源
  config: {
    score: number // 评分值
  }
  categoryId: Empty
  questionDisplayMode: QuestionDisplayMode // 排列方式
}
export const enum QuestionDisplayMode {
  Horizontal = 1,
  Vertical
}

export const enum QuestionType {
  single = 1,
  multiple,
  number,
  subjective,
  multipleChoice = 6,
  captions
}

export const enum AnnotationGranularity {
  Assistant = 1,
  Session,
  Captions
}
export interface QuestionSetting {
  disabled: boolean
  name: string
  type:
    | QuestionType.multiple
    | QuestionType.single
    | QuestionType.number
    | QuestionType.multipleChoice
  options: string[]
  numberConfig: NumberConfig
  mustAnswer: boolean
  annotationRole: AnnotationRole
  annotationGranularity: AnnotationGranularity
}
export const enum NumberConfigType {
  select = 1,
  input
}
export interface NumberConfig {
  type: NumberConfigType
  max: number
  min: number
  percision: number
}
export interface CreateModelsItem {
  id: number
  source: string // 自定义名称
  params: Partial<ModelsItemParams>
}

export interface DelMarkTaskParams {
  id: number
}

export const enum TaskFileType {
  create = 1,
  import
}
export interface ImportMarkTaskParams {
  file: File
  type: TaskFileType
}

export interface ImportMarkTaskResult {
  dwUrl: string
  id: string
  pid: string
  needAnnotator: boolean
}

export interface MarktaskdetailParams {
  markTaskId: number
  ownerUname?: string // 标注人名称(进入指定人标注页面)
}
export interface GsbListItem {
  detailId: number
  anotherDetailId: number
  gsb: number // 1-g 2-s 3-b
  markTaskId: number
}

export type UpdateGsbParams = GsbListItem
export interface Questions {
  questions: Question[]
}
export interface MarktaskdetailRet extends Questions {
  list: Array<{
    session: string
    promptList: PromptListItem[]
  }>
  isGsb: boolean
  hasSession: boolean
  isOwner: boolean
  isNil: boolean
  score: number
  isSort: boolean
  isWritingEval: boolean
  isCaptions: boolean
  extDispTitles: string[]
}
export interface PromptListItem {
  minRowId: number
  detailList: MarktaskdetailItem[]
  gsbList: GsbListItem[]
}
export interface Question {
  id: number
  type: QuestionType
  name: string
  options: TaskTypeListItem[]
  numberConfig: NumberConfig
  mustAnswer: boolean
  annotationRole: AnnotationRole
  annotationGranularity: AnnotationGranularity
}
export const enum AnnotationRole {
  Annotationer = 1,
  PolyQualityInspector
}
export const enum MarkTaskDetailType {
  single = 1,
  multiple
}
export interface MultiChoice {
  index: number
  score: string | undefined
}
export interface Caption {
  confirm: boolean
  layers: CaptionLayer[]
}
export interface CaptionLayer {
  name: string
  lines: CaptionLine[]
  canDel: boolean
}
export interface UserManipulateLog {
  user: string
  datetime: string
  type: 'add' | 'edit' | 'deleted' | 'merge'
}
export interface CaptionLine {
  start: number
  end: number
  content: string
  userManipulateLog: UserManipulateLog[]
  deleted: boolean
  questionResult: ResultType
}
export type ResultType = Record<string, number[] | string | MultiChoice[] | Caption>
export interface MarktaskdetailItem {
  id: number
  markTaskId: number
  param: ModelsItemParams
  time: number
  modelId: number
  apiUser: string // user信息
  apiResult: string // 结果
  score: number // 已打分数
  markTaskType: string // 标注任务类型 1-4分制
  markScoreType: ScoreType // 评分类型
  status: MarkTaskStatus // 明细状态
  businessCode: string
  ownerUname: string
  deleted: number
  createTime: number
  updateTime: string
  remark: string // 备注
  totalCount?: number // 用于合并单元格(apiUser)
  preSessionId?: number // 合并单元格找之前的题
  totalPromptCount?: number // 用于合并单元格(session)
  type: MarkTaskDetailType // 1-单次消息 2-带上下文
  result: ResultType
  isMarkTag: boolean // 是否标注标签
  commentLabelContent: string // 批注内容
  source: string // 模型名称
  image: string // 图片链接
  inputImage: string // 图片链接
  sid: string // sid
  writingEvalQuery: string // 写作评估query信息
  session: string
  ext: {
    extList: {
      t: string
      v: string
      et: number
    }[]
  }
}

export interface ResultUpdateParams {
  markTaskDetailId: number
  score?: number
  remark?: string
  question?: ResultType
  commentLabelContent?: string
  ownerUname?: string
}

export interface MarkResultListParams {
  markTaskId: number
}

export interface MarkResultListRet {
  isOwner: boolean
  list: MarkResultListItem[]
  models: string[]
  createTime: number
  name: string
}
export interface MarkResultListItem {
  ownerUname: string // 用户名
  finishRate: number // 完成率 4000 => 40%
  modelAvgMap: Record<keyof MarkResultListRet['models'], string>
}

export interface MarkResultListDetailParams {
  id: number
}
export type MarkResultListDetailRet = MarkTaskListItem & {
  isShowSource: boolean // 是否展示数据源
}

export interface CreateMarkTaskRet {
  id: number
  modelNum: number
  promptNum: number
}

export interface MarktaskTemplateRet {
  xlsxUrl: string
  jsonUrl: string
}

type PickMarkTaskListItem<T extends keyof MarkTaskListItem> = Pick<MarkTaskListItem, T>
export type MarktaskUpdateParams = Partial<PickMarkTaskListItem<'publishResult' | 'lock'>> &
  PickMarkTaskListItem<'id'>

export interface MarktaskChartParams {
  markTaskIds: number[]
  startHitRate?: number
  endHitRate?: number
  mode?: MarkTaskChartMode
  models?: string[]
  dataSets?: string[]
  startCorrectRate?: number
  endCorrectRate?: number
}

export const enum MarkTaskChartMode {
  DataSetId = 3,
  DataSetGroup
}

export interface MarktaskOverlapParams {
  markTaskId: number
  option: number // 1-不同项 2-相反项
}

export interface MarktaskOverlapRet {
  list: MarktaskOverlapRetListItem[]
  sameRate: string // 一致率
  same: number // 一致数
  cnt: number // 总数
  headers: TaskTypeListItem[]
}
export interface MarktaskOverlapRetListItem {
  markTaskId: number
  markTaskDetailId: number
  model: string
  prompt: string
  output: string
  user1: string
  score1: number
  user2: string
  score2: number
}

export interface DatasetsOptionsRet {
  dataSets: TaskTypeListItem[]
  labelStyles: Array<TaskTypeListItem & { num: number }>
}

export interface MarkEditHistoryParams {
  markTaskId: number
}
export type MarkEditHistoryRet = UniversalListResult<MarkEditHistoryItem> & {
  config: MarkEditHistoryRetConfig
}

export interface MarkEditHistoryRetConfig extends Questions {
  isGsb: boolean
  isSort: boolean
  score: number
  isNil: boolean
  scoreType: ScoreType //评分类型
  apiResultMap: Record<string, string>
}
export interface MarkEditHistoryItem {
  id: number
  markTaskDetailId: number
  editUname: string
  updateTime: number
  oldValue: any
  newValue: any
  field: HistoryField
}
export const enum HistoryField {
  Score = 'score',
  Remark = 'remark',
  CommentLabelContent = 'commentLabelContent',
  Question = 'question',
  GSB = 'gsb'
}

export interface ChartsOptionsRet {
  dataSets: TaskTypeListItem[]
  models: string[]
  modes: TaskTypeListItem[]
}

export interface ChartsSelfOptionsRet {
  dataSets: TaskTypeListItem[]
  modes: TaskTypeListItem[]
}

export interface MarktaskChartRet {
  category: string[]
  cnt: number
  list: MarkSelfChartListItem[]
  series: SeriesItem[]
}
export interface MarktaskRadarChartRet {
  category: string[]
  series: SeriesItem[]
}
export type MarkSelfChartRet = [MarktaskChartRet, MarktaskRadarChartRet]
export interface MarkSelfChartListItem {
  allNum: number
  avgHitScore: string
  avgScore: string
  dataSetId: number
  dataSetName: string
  hitDeviation: string
  hitNum: number
  hitRate: string
  markTaskId: number
  markTaskName: string
  model: string
  scoreList: number[]
  totalScore: number
}

export interface SeriesItem {
  data: string[]
  name: string
}

export interface GetAnnotatorStatusParams {
  taskId: number
}
export type GetAnnotatorStatusRet = UniversalListResult<AnnotatorStatusItem>
export interface AnnotatorStatusItem {
  uname: string
  marked: boolean
}

export interface AddAnnotatorParams {
  newAnnotators: string[]
  oldAnnotator: string
  taskId: number
  type: AddAnnotatorType
}
export const enum AddAnnotatorType {
  Copy = 1,
  Split
}

export interface RedistributeParams {
  taskId: number
}
