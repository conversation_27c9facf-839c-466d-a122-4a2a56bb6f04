import axios from '@/plugin/axios'
import type {
  CreateMarkTagParams,
  DelMarkTagParams,
  EditMarkTagParams,
  MarkTagDetailParams,
  MarkTagListItem,
  MarkTagListRet
} from './type'
import { MutationFn } from '@znzt-fe/axios'
const { mutationPost, post } = axios('marktag')

/** 标注标签列表 */
export const getMarkTagList = () => post<MarkTagListRet>('list')

/** 添加标注标签 */
export const useCreateMarkTag: MutationFn<CreateMarkTagParams> = (options) =>
  mutationPost('create', options)

/** 编辑标注标签 */
export const useEditMarkTag: MutationFn<EditMarkTagParams> = (options) =>
  mutationPost('edit', options)

/** 删除标注标签 */
export const useDelMarkTag: MutationFn<DelMarkTagParams> = (options) => mutationPost('del', options)

/** 标注标签详情 */
export const useGetMarkTagDetail: MutationFn<MarkTagDetailParams, MarkTagListItem> = (options) =>
  mutationPost('detail', options)
