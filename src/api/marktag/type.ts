import { UniversalListResult } from '@znzt-fe/declare'

export type MarkTagListRet = UniversalListResult<MarkTagListItem>
export interface MarkTagListItem {
  id: string
  color: string
  name: string
  desc: string
  createTime: number
}

export interface CreateMarkTagParams {
  color: string
  name: string
  desc: string
}

export interface EditMarkTagParams {
  id: string
  color: string
  name: string
  desc: string
}

export interface DelMarkTagParams {
  id: string
}

export interface MarkTagDetailParams {
  id: string
}
