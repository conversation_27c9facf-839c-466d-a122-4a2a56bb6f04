import { UniversalListResult } from '@znzt-fe/declare'

export type MarkCategoryListRet = UniversalListResult<MarkCategoryListItem>
export interface MarkCategoryListItem {
  id: string
  name: string
  createTime: number
}

export interface CreateMarkCategoryParams {
  name: string
}

export interface EditMarkCategoryParams {
  id: string
  name: string
}

export interface DelMarkCategoryParams {
  id: string
}

export interface MarkCategoryDetailParams {
  id: string
}
