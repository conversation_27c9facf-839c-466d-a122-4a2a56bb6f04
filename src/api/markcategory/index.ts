import axios from '@/plugin/axios'
import type {
  CreateMarkCategoryParams,
  DelMarkCategoryParams,
  EditMarkCategoryParams,
  MarkCategoryDetailParams,
  MarkCategoryListItem,
  MarkCategoryListRet
} from './type'
import { MutationFn } from '@znzt-fe/axios'
const { mutationPost, post } = axios('markcategory')

/** 标注标注类别列表 */
export const getMarkCategoryList = () => post<MarkCategoryListRet>('list')

/** 标注标注类别详情 */
export const useGetMarkCategoryDetail: MutationFn<
  MarkCategoryDetailParams,
  MarkCategoryListItem
> = (options) => mutationPost('detail', options)

/** 添加标注类别 */
export const useCreateMarkCategory: MutationFn<CreateMarkCategoryParams> = (options) =>
  mutationPost('create', options)

/** 编辑标注类别 */
export const useEditMarkCategory: MutationFn<EditMarkCategoryParams> = (options) =>
  mutationPost('edit', options)

/** 删除标注类别 */
export const useDelMarkCategory: MutationFn<DelMarkCategoryParams> = (options) =>
  mutationPost('del', options)
