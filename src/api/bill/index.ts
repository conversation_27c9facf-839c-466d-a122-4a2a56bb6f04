import axios from '@/plugin/axios'
import { BillBusinessDetailRet, BillDetailRet, BillOptionsRet } from './type'
const { get, post } = axios('bill')
export const getOptions = () => get<BillOptionsRet>('option')
export const uploadBill = (data: any) => post<any>('upload', data)
export const getDetail = (data: any) => get<BillDetailRet>('viewdetail', data)
export const getBusinessDetail = () => get<BillBusinessDetailRet>('viewbuss')
export const fixBill = (data: any) => post<any>('fix', data)
export const billReady = () => get<any>('ready')

export const fixBusiness = (data: any) => post<any>('fixbusiness', data)
