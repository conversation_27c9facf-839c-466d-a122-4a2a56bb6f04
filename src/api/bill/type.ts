export interface BillBusinessDetailRet {
  bussList: BillBussListItem[]
}
export interface BillBussListItem {
  batchName: string
  budgetName: string
  bussCode: string
  bussName: string
  price: string
}

export interface BillDetailRet {
  detailList: BillDetailListItem[]
  total: number
}
export interface BillDetailListItem {
  batchName: string
  budgetName: string
  bussName: string
  correctFromId: number
  handSrv: string
  id: number
  isCorrect: string
  modelName: string
  notes: string
  ori: string
  price: number
  skType: string
  taxRate: 1
  type: string
}

export interface BillOptionsRet {
  list: OptionRow[]
  useRate: 0 | 1 // 0不能上传 1可以上传
  status: 1 | 2 // 1开始导入 2导入完成
  batchName: string
}

export interface OptionRow {
  label: string
  value: number
  fileName: string
  fileUrl: string
}
