import { ModelsItemParams } from '@/api/marktask/type'
import type { Empty, Options, UniversalListResult } from '@znzt-fe/declare'
export interface GetBusSummaryParams {
  businessId: number
  withUserUsage: boolean
}

export interface GetBusSummaryRet {
  businessId: number | string
  name: string
  code: string
  qpsLimit: string
  dailyTokenLimit: number
  usedTokenCnt: number
  unUsedTokenCnt: number
  status: string
  memberLimit: number
  userApiCntNow: number
  userApiCntLimit: number
  userUsePromptTokens: number
  userUseCompletionTokens: number
  userUseAllTokens: number
}

export interface GetSKListParams {
  businessId: number | string
  name: string
  code: string
  status: number
  withSk: number
  withIp: number
  withMemberCnt: number
  pageNum: number
  pageSize: number
}

export type GetSKListRet = UniversalListResult<GetSKListRetListItem>
interface GetSKListRetListItem {
  id: number
  businessId: number | string
  businessCode: string
  sk: string
  model: string
  cost: number
  status: number
  rpmLimit: number
  tpmLimit: number
  importContent: ImportContent
  delType: string
  delCode: string
  delMsg: string
  opUname: string
  deleted: number
  createTime: number
  updateTime: string
  account: string
}
interface ImportContent {
  date: string
  businessName: string
  account: string
  accountNumber: string
  openAiPassword: string
  emailPassword: string
  sk: string
  rpm: number
  tpm: number
  organization: string
  balance: string
  channel: string
  payment: string
  price: string
}

export type GetBusListParams = GetSKListParams

export type GetBusListRet = UniversalListResult<GetBusListRetListItem>

export interface GetBusListRetListItem {
  businessId: number
  name: string
  code: string
  useScenario: number
  dailyTokenLimit: number
  memberLimit: number
  status: string
  nowMemberCnt: number
  skCnt: number
  applyUname: string
  createTime: number
}

export interface GetBusinessDetailParams {
  businessId: number
}

export interface GetBusinessDetailRet {
  businessId: number
  name: string
  code: string
  useScenario: number
  dailyTokenLimit: number
  memberLimit: number
  applyUname: string
  status: string
  config: GetBusinessDetailRetConfig[]
  budgetName: string
}
export interface GetBusinessDetailRetConfig {
  businessId: number // 所属业务线id
  modelName: string // 模型名称
  rpm: number // 每分钟请求数
  tpm: number // 每分钟请求token数
  ipm: number // 每分钟生成图片数量
  typeString: string // 模型类型
  handSrvs: HandSrv[] // 调度策略
  customHandSrv: number // 是否可以指定调度策略
  modelCategory: string // 模型分类名称
}
export interface HandSrvModelVersionDetail {
  id: number
  name: string
  parentId?: number
  status?: number
  type?: number
  handSrvs?: HandSrv[] // 调度策略
}
export interface HandSrv {
  handSrv: string // 调用方标识
  rate: number // 流量比例
  usePrivate: boolean // 是否私有
  privatePoolCode: string // 私有池pool - 为空时 默认本业务线code
  changeModelCategory: string
  changeModelConfig?: any[]
}
export interface GetIpListParams {
  businessId: number
  name: string
  code: string
  status: number
  withSk: number
  withIp: number
  withMemberCnt: number
  pageNum: number
  pageSize: number
}
export type GetIpListRet = UniversalListResult<GetIpListRetListItem>

interface GetIpListRetListItem {
  id: number
  ipFactoryId: number
  ipFactoryName: string
  ip: string
}

export interface CreateBusinessParams {
  name: string
  code: string
  budgetName: string
  useScenario: number
  dailyTokenLimit: number
  memberLimit: number
  modelLimitStatus: number
  needAlarm: number
  desc: string
  config: ConfigItem[]
  retryInterval: number
  devUname: string
  applyUname: string
}

interface ConfigItem {
  modelName: string
  rpm: number
  tpm: number
  ipm: number
  qps: number
  handSrvs: HandSrv[]
}

export const enum Scenario {
  Unset,
  Online,
  Offline,
  Inside
}
export interface UpdateBusinessParams {
  businessId: number | string
  name: string
  code: string
  budgetName: string
  useScenario: number
  dailyTokenLimit: number
  memberLimit: number
  modelLimitStatus: number
  needAlarm: number
  desc: string
  config: ConfigItem[]
  retryInterval: number
  devUname: string
  applyUname: string
}

export interface SkControlParams {
  ids: number[]
  controlAction: number
}

export interface SkAddParams {
  businessId: number | string
  qpsLimit: number
  skListStr: string
  skType: number
  lockDuration: number
  cost: number
}

export interface BusControlParams {
  businessId: number | string
  controlAction: ControlAction
  reason?: string
}
export const enum ControlAction {
  Pass = 1,
  Deactivate = 3,
  Enable,
  Dismiss,
  RejectEdit
}
export interface IpControlParams {
  ipIds: number[]
  controlAction: ControlAction
}

export interface AddIpParams {
  ipFactoryId: number
  ipListStr: string
}

export interface GetModelParamsParams {
  business: string
}

export interface GetModelParamsRet {
  list: ModelParamsItem[]
}

export interface ModelParamsItem {
  id: number
  model: string
  desc: string
  status: ModelParamsItemStatus
  advancedParameters: AdvancedParameter[]
  modelCategory: string
}
export const enum ModelParamsItemStatus {
  Effective = 1,
  Invalid,
  offline
}
export interface AdvancedParameter {
  component: 'slider' | 'input' | 'switch' | 'numberinput' | 'json'
  defaultValue: number
  name: keyof ModelsItemParams
  type: string
  params: AdvancedParameterParams
}

interface AdvancedParameterParams {
  max: number
  min: number
  step: number
}

export interface GetBusinessBudgetRet {
  list: {
    budgetName: string
  }[]
}

export interface BatchSetRpmTpmParams {
  arrId: number[]
  tpm: number
  rpm: number
}

export interface GetSkFileDemoRet {
  url: string
}

export interface BusinessOptionalRet {
  accountList: Options[]
  businessList: Array<Options & { status: number; code: string }>
  companyList: companyListItem[]
  modelList: ModelList
}

export type ModelList = ModelItem[]
export interface ModelItem extends Options {
  status: number
  disabled?: boolean
  modelList?: ModelList
}

export interface companyListItem extends Options {
  modelList: ModelList
}

export interface SkUpdateParams {
  id: number
  date: Empty
  businessName: string
  account: string
  accountNumber: string
  openAiPassword: string
  emailPassword: string
  sk: string
  rpm: number
  tpm: number
  trainTpm: number
  ipm: number
  organization: string
  balance: string
  channel: string
  payment: string
  price: string
  expirationTime: Empty
  priority: number
  rowId: number
  location: string
  apiSecret: string
}

export interface UpdateModelStrategyParams {
  businessId: number
  config: UpdateModelStrategyConfig
}
export type UpdateModelStrategyConfig = UpdateModelStrategyConfigItem[]
export interface UpdateModelStrategyConfigItem {
  modelName: string
  rpm: number
  tpm: number
  ipm: number
  handSrvs: UpdateModelStrategyConfigHandSrv[]
  modelCategory: string
}
export interface UpdateModelStrategyConfigHandSrv {
  handSrv: string
  rate: number
  privatePoolCode: string
  changeModelCategory?: string
  changeModelConfig?: ModelChange[]
}
export interface ModelChange {
  pre: string
  after: string
}

export interface UpdateModelStrategyRet {
  result: number
}
