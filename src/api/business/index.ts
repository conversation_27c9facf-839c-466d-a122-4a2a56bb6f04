import axios from '@/plugin/axios'
import type {
  AddIpParams,
  BusControlParams,
  CreateBusinessParams,
  GetBusListParams,
  GetBusListRet,
  GetBusinessBudgetRet,
  GetBusinessDetailParams,
  GetBusinessDetailRet,
  GetSKListParams,
  GetSKListRet,
  GetBusSummaryParams,
  GetBusSummaryRet,
  GetIpListParams,
  GetIpListRet,
  GetModelParamsParams,
  GetModelParamsRet,
  IpControlParams,
  SkAddParams,
  SkControlParams,
  UpdateBusinessParams,
  BatchSetRpmTpmParams,
  GetSkFileDemoRet,
  BusinessOptionalRet,
  SkUpdateParams,
  UpdateModelStrategyParams,
  UpdateModelStrategyRet
} from './type'
import { MutationFn, QueryFn } from '@znzt-fe/axios'

const { get, post, mutationGet, mutationPost, queryGet } = axios('business')

/**  业务线 / 用户概览 */
export const getBusSummary = (data: GetBusSummaryParams) => get<GetBusSummaryRet>('summary', data)

/**  sk列表 */
export const getSKList = (data: GetSKListParams) => get<GetSKListRet>('sklist', data)

/**  业务线列表 + query */
export const useGetBusList = () => mutationGet<GetBusListParams, GetBusListRet>('list')

/**  业务线列表 */
export const getBusList = (data: GetBusListParams) => get<GetBusListRet>('list', data)

/**  业务线详情 */
export const getBusinessDetail = (data: GetBusinessDetailParams) =>
  get<GetBusinessDetailRet>('detail', data)

/**  业务线详情 */
export const useGetBusinessDetail: MutationFn<GetBusinessDetailParams, GetBusinessDetailRet> = (
  options
) => mutationGet('detail', options)

/**  业务线ip列表 */
export const getIpList = (data: GetIpListParams) => get<GetIpListRet>('iplist', data)

/**  新增业务线 */
export const createBusiness = (data: CreateBusinessParams) => post('create', data)

/**  修改业务线 */
export const updateBusiness = (data: UpdateBusinessParams) => post('update', data)

/**  调控业务线sk */
export const skControl = (data: SkControlParams) => post('skcontrol', data)

/**  新增业务线sk */
export const skAdd = (data: SkAddParams) => post('skadd', data)

/**  调控业务线 */
export const busControl = (data: BusControlParams) => post('control', data)

/**  调控业务线ip */
export const ipControl = (data: IpControlParams) => post('ipcontrol', data)

/**  新增业务线ip */
export const addIp = (data: AddIpParams) => post('ipadd', data)

/** 业务线有权限的模型 */
export const getModelParams = (data: GetModelParamsParams) =>
  get<GetModelParamsRet>('modelparams', data)

/** 业务线有权限的模型 */
export const useGetModelParams: QueryFn<GetModelParamsParams, GetModelParamsRet> = (data) =>
  queryGet('modelparams', data)

/** 可选模型列表 */
export const getBusinessOptional = () => post<BusinessOptionalRet>('optional')

/** 更新sk */
export const updateSk = (data: SkUpdateParams) => post('skupdate', data)

/** 获取预算单元 */
export const getBusinessBudget = () => get<GetBusinessBudgetRet>('budget')

/** 修改业务线模型调度策略 */
export const updateModelStrategy = (data: UpdateModelStrategyParams) =>
  post<UpdateModelStrategyRet>('modelstrategy', data)

/** 批量赋值tpm/rpm */
export const useBatchSetRpmTpm: MutationFn<BatchSetRpmTpmParams> = (options) =>
  mutationPost('batchsetrpmtpm', options)

/** 导出sk demo */
export const useGetSkFileDemo: QueryFn<{}, GetSkFileDemoRet> = () => queryGet('skfiledemo')
