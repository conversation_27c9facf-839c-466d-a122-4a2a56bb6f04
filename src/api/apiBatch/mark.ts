import axios from '@/plugin/axios'
const { get, post } = axios('markbatch')

interface GlobalResponse {
  failedCount: number
  runningCount: number
  successCount: number
}

export interface BusinessItem {
  key: string
  value: string
}

interface BusinessResponse {
  list: BusinessItem[]
}

interface UserItem {
  value: string
  label: string
}

interface UserResponse {
  list: UserItem[]
}

export interface TaskItem {
  id: number
  businessCode: string
  businessName: string
  uname: string
  status: number
  total: number
  completed: number
  failed: number
  createAt: number
  completedAt: number
  inputUrl: string
  outputUrl: string
  promptTokens: number
  completionTokens: number
  totalTokens: number
}

interface TaskListResponse {
  list: TaskItem[]
  total: number
}

export const getGlobal = () => get<GlobalResponse>('global')
export const getBusiness = (params: { aggregationStatus?: number }) =>
  get<BusinessResponse>('business', params)
export const getUser = (params?: { businessCode?: string; aggregationStatus?: number }) =>
  get<UserResponse>('user', params)
export const getTaskList = (params: {
  businessCode?: string
  uname?: string
  status?: number
  pageNum: number
  pageSize: number
}) => get<TaskListResponse>('list', params)

// op 1: 暂停 2:恢复
export const execStatus = (params: { promptBatchId: number; op: number }) => post('exec', params)
