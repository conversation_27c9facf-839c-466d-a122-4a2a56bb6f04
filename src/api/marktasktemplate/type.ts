import { Id, UniversalListResult } from '@znzt-fe/declare'

export type MarkTaskTemplateListRet = UniversalListResult<MarkTaskTemplateListItem>
export interface MarkTaskTemplateListItem {
  id: string
  content: string
  name: string
  desc: string
  createTime: number
}

export interface CreateMarkTaskTemplateParams {
  content: string
  name: string
  desc: string
}

export interface EditMarkTaskTemplateParams {
  id: string
  content: string
  name: string
  desc: string
}

export type DelMarkTaskTemplateParams = Id

export type MarkTaskTemplateDetailParams = Id
