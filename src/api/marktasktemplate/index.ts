import axios from '@/plugin/axios'
import type {
  CreateMarkTaskTemplateParams,
  DelMarkTaskTemplateParams,
  EditMarkTaskTemplateParams,
  MarkTaskTemplateDetailParams,
  MarkTaskTemplateListItem,
  MarkTaskTemplateListRet
} from './type'
import { MutationFn } from '@znzt-fe/axios'
const { mutationPost, post } = axios('marktasktemplate')

/** 标注任务模板列表 */
export const getMarkTaskTemplateList = () => post<MarkTaskTemplateListRet>('list')

/** 添加标注任务模板 */
export const useCreateMarkTaskTemplate: MutationFn<CreateMarkTaskTemplateParams> = (options) =>
  mutationPost('create', options)

/** 编辑标注任务模板 */
export const useEditMarkTaskTemplate: MutationFn<EditMarkTaskTemplateParams> = (options) =>
  mutationPost('edit', options)

/** 删除标注任务模板 */
export const useDelMarkTaskTemplate: MutationFn<DelMarkTaskTemplateParams> = (options) =>
  mutationPost('del', options)

/** 标注任务模板详情 */
export const useGetMarkTaskTemplateDetail: MutationFn<
  MarkTaskTemplateDetailParams,
  MarkTaskTemplateListItem
> = (options) => mutationPost('detail', options)
