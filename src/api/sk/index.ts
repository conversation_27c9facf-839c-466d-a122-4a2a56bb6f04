import axios from '@/plugin/axios'
import { MutationFn } from '@znzt-fe/axios'
import {
  RecoverySkParams,
  RecoverySkRet,
  OptionListRet,
  PurchasingListParams,
  PurchasingListRet,
  OpListRet
} from './type'
const { get, post, mutationPost } = axios('sk')

/** sk测试 */
export const skTest = (data: any) => post<any>('test', data)

export const purchasingDel = (data: any) => post<any>('purchasingdel', data)

/** sk文件内容列表 */
export const getPurchasingList = (data: PurchasingListParams) =>
  get<PurchasingListRet>('purchasinglist', data)

/** sk 移动至其他业务线或者修改状态 */
export const adjust = (data: any) => post<any>('adjust', data)

/** 获取sk操作记录列表 */
export const getOpList = (data: { id: number }) => get<OpListRet>('opList', data)

/** 获取sk查询相关枚举值 */
export const getOptionList = () => get<OptionListRet>('option')

/** sk恢复 */
export const useRecoverySk: MutationFn<RecoverySkParams, RecoverySkRet> = (options) =>
  mutationPost('recovery', options)
