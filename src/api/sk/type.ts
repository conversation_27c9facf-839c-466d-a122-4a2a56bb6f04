import { Empty, Options, UniversalListResult } from '@znzt-fe/declare'

export interface RecoverySkParams {
  sk: string
  skType: SkType
  id: number
}
export const enum SkType {
  FREE = 1, // 灰产-免费
  PAY, // 灰产-付费 QpsLimit=58
  FORMAL // 正式账号 QpsLimit=58
}

export interface RecoverySkRet {
  result: Result
}
export const enum Result {
  Error,
  Success
}

export interface OptionListRet {
  channelList: Options[]
}

export interface PurchasingListParams {
  businessId: Empty
  sk: string
  model: string
  status: Empty<PurchasingStatus>
  startDate: Empty
  endDate: Empty
  startTime: Empty
  endTime: Empty
  accountType: Empty
  stopStartTime: Empty
  stopEndTime: Empty
  email: string
  channel: string
}

export type PurchasingListRet = UniversalListResult<PurchasingListItem>
export interface PurchasingListItem {
  sk: string
  cost: number
  date: number
  accountNumber: string
  openAiPassword: string
  emailPassword: string
  balance: string
  channel: string
  payment: string
  price: string
  accountType: number
  emptionType: number
  expirationTime: number
  id: number
  skType: number
  rpm: number
  tpm: number
  ipm: number
  trainTpm: number
  businessId: number
  status: PurchasingStatus
  pool: string
  scene: string
  priority: number
  model: string
  delType: string
  delCode: string
  delMsg: string
  organization: string
  startTime: number
  stopTime: number
  account: string
  businessName: string
  ext: ExtInfo
  location: string
  sId: number
  apiSecret: string
  recoveryLoading?: boolean
}
export const enum PurchasingStatus {
  NotEnabled = 1,
  Enabled,
  Delete,
  Init,
  InitFail
}
export interface ExtInfo {
  apiSecret?: string
  subscription?: string
  location?: string
  model?: string
  modelVersion?: string
  endpoint?: string
}

export interface OpListRet {
  list: OpListItem[]
}
export interface OpListItem {
  opContent: string
  businessName: string
  accountTypeName: string
  statusName: string
  createTime: string
  id: number
  skDetailId: number
  sk: string
  businessId: number
  accountType: number
  status: number
  type: number
  content: SkDetailOpContent
  opUname: string
  deleted: number
  updateTime: string
  typeName: string
}
export type SkDetailOpContent = SkDetailOpContentItem[]
export interface SkDetailOpContentItem {
  field: string
  old: any
  new: any
}
