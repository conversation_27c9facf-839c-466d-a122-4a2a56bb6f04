import { Directive } from 'vue'
const stopComposeEnterEvent = (e: KeyboardEvent) => {
  if (e.isComposing && e.code === 'Enter') e.stopPropagation()
}
export const stopComposeEnterDirective: Directive = {
  mounted(el: Element) {
    el.addEventListener('keydown', (e) => stopComposeEnterEvent(e as KeyboardEvent), {
      capture: true
    })
  },
  beforeUnmount(el) {
    el.removeEventListener('keydown', stopComposeEnterEvent)
  }
}
