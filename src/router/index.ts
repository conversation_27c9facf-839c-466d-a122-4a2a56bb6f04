import { createRouter, createWebHistory } from 'vue-router'
import useUserStore, { pendingLogin } from '@/store/user'
import routes from './routes'
import { RoleId } from '@/api/user/type'
import zs from '@/plugin/zs'
let userStore: ReturnType<typeof useUserStore> | undefined = undefined

const { VITE_BASE_URL } = import.meta.env
const router = createRouter({
  history: createWebHistory(VITE_BASE_URL),
  routes
})
const noNeedCheck = ['/home']

router.beforeEach(async (to) => {
  if (!userStore) {
    userStore = useUserStore()
  }
  const { path } = to

  // 不需要登录校验直接返回true
  if (noNeedCheck.includes(path)) {
    return true
  }
  // 没有登录 等待promise返回
  if (!userStore.login) {
    await pendingLogin()
  }
  // 模型价格界面是通用的
  if (path === '/price') return true

  const { roleId, businessId, status } = userStore.current
  const passedStatus = [2, 5]
  // 非正常状态
  if (!passedStatus.includes(status!) && !userStore.isGuest) {
    return '/home'
  }
  if (path.startsWith('/system')) {
    return roleId === RoleId.SuperAdmin ? true : '/home'
  }
  if (path.startsWith('/business')) {
    return roleId === RoleId.BusAdmin || roleId === RoleId.BusSuperAdmin ? true : '/home'
  }
  if (businessId) {
    return true
  }
  return '/home'
})
router.afterEach((guard) => {
  const matched = guard.matched
  const path = matched[matched.length - 1].path
  switch (path) {
    case '/statistics':
      zs.track('HO4_001', {
        userName1: userStore?.name
      })
      break
  }
})
const origin = router.push
router.push = function (...rest) {
  return origin(...rest).catch((err) => {
    if (!~err.message.search('Failed to fetch dynamically imported module')) return
    location.replace(location.origin + import.meta.env.VITE_BASE_URL)
  })
}

export default router
