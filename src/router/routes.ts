import { RouteRecordRaw } from 'vue-router'
const Home = () => import('@/views/home/<USER>')
const layout = () => import('@/components/layout/top-bottom')
const UserList = () => import('@/views/business/user/index.vue')
const SelfBuiltModelList = () => import('@/views/business/model/index.vue')
const ModelList = () => import('@/views/system/model')
const SKList = () => import('@/views/business/sk/index.vue')
const Alarm = () => import('@/views/business/alarm/index.vue')
const MarkStandard = () => import('@/views/business/markStandard')
const MarkCategory = () => import('@/views/business/markCategory')
const MarkTag = () => import('@/views/business/markTag')
const MarkTaskTemplate = () => import('@/views/business/markTaskTemplate')
const SecondView = () => import('@/views/router/index.vue')
const KeepAliveRouter = () => import('@/views/router/keep-alive-router.vue')
const IPList = () => import('@/views/system/ip/index.vue')
const IPFactory = () => import('@/views/system/factory/index.vue')
const BusinessList = () => import('@/views/system/business/index.vue')
const User = () => import('@/views/system/user/index.vue')
const Chat = () => import('@/views/chat/index.vue')
const Task = () => import('@/views/task/index.vue')
const MarkRate = () => import('@/views/mark/rate')
const MarkTask = () => import('@/views/mark/task')
const MarkResult = () => import('@/views/mark/result')
const MarkOuterResult = () => import('@/views/mark/outer-result/index.vue')
// const MarkCompare = () => import('@/views/mark/compare/index.vue')
const MarkOverlapResult = () => import('@/views/mark/overlap-result')
const Picture = () => import('@/views/picture')
const MarkSelfResult = () => import('@/views/mark/self-result')
const SchedulingConfig = () => import('@/views/system/scheduling-config/index.vue')
const SchedulingGroupList = () => import('@/views/system/scheduling-group-list/index.vue')
const BillManage = () => import('@/views/system/bill/index.vue')
const BatchManage = () => import('@/views/system/batch/index.vue')
const Price = () => import('@/views/price')

import zs from '@/plugin/zs'
const { VITE_BASE_URL } = import.meta.env
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: layout,
    meta: {
      keepAlive: true
    },
    children: [
      {
        path: '/home',
        component: Home
      },
      {
        path: '/business',
        component: SecondView,
        children: [
          {
            path: 'user',
            component: UserList
          },
          {
            path: 'sk',
            component: SKList
          },
          {
            path: 'alarm',
            component: Alarm
          },
          {
            path: 'modellist',
            component: SelfBuiltModelList
          },
          {
            path: 'markStandard',
            component: MarkStandard
          },
          {
            path: 'markTag',
            component: MarkTag
          },
          {
            path: 'markTaskTemplate',
            component: MarkTaskTemplate
          },
          {
            path: 'markCategory',
            component: MarkCategory
          }
        ]
      },
      {
        path: '/mark',
        meta: {
          keepAlive: true
        },
        component: KeepAliveRouter,
        children: [
          {
            path: '',
            component: MarkTask,
            meta: {
              keepAlive: true
            }
          },
          {
            path: 'result/:id',
            component: MarkResult
          },
          {
            path: 'result/:type/:id',
            component: MarkOuterResult
          },
          {
            path: 'compare/:type',
            component: MarkOuterResult
          },
          {
            path: 'self/:type/:id',
            component: MarkSelfResult
          },
          {
            path: 'self/:type',
            component: MarkSelfResult
          },
          {
            path: 'result/overlap/:id',
            component: MarkOverlapResult
          }
        ]
      },
      {
        path: '/picture',
        component: SecondView,
        children: [
          {
            path: '',
            component: Picture
          }
        ]
      },
      {
        path: '/price',
        component: SecondView,
        children: [
          {
            path: '',
            component: Price
          }
        ]
      },
      {
        path: '/system',
        component: SecondView,
        children: [
          {
            path: 'ip',
            component: IPList
          },
          {
            path: 'business',
            component: BusinessList
          },
          {
            path: 'factory',
            component: IPFactory
          },
          {
            path: 'user',
            component: User
          },
          {
            path: 'sk',
            component: SKList
          },
          {
            path: 'model',
            component: ModelList
          },
          {
            path: 'scheduling-config',
            component: SchedulingConfig
          },
          {
            path: 'scheduling-group-list',
            component: SchedulingGroupList
          },
          {
            path: 'bill',
            component: BillManage
          },
          {
            path: 'batch',
            component: BatchManage,
            meta: {
              requiresSuperAdmin: true
            }
          }
        ]
      },
      {
        path: '',
        redirect: '/home'
      },
      {
        path: '/chat',
        component: SecondView,
        children: [
          {
            path: '',
            component: Chat
          }
        ]
      },
      {
        path: '/task/:id?',
        name: 'Task',
        component: Task
      }
    ]
  },
  {
    path: '/mark/rate/:id',
    component: MarkRate
  },
  {
    path: '/**',
    redirect: '/home'
  }
]

// 会继承父路由meta信息的 keepalive，所以需要真实设置为false
const routerEnterFunc = (routes: RouteRecordRaw[]) => {
  routes.forEach((route) => {
    // 传入router的mode，默认为hash
    // 如果为非hash模式则需要传入base
    const mixin = zs.routeMixin(VITE_BASE_URL)
    const beforeEnter = route.beforeEnter
    if (!route.meta?.keepAlive) {
      route.meta = {
        ...route.meta,
        keepAlive: false
      }
    }
    if (beforeEnter) {
      typeof beforeEnter === 'function'
        ? (route.beforeEnter = [beforeEnter, mixin.beforeEnter])
        : (route.beforeEnter = [...beforeEnter, mixin.beforeEnter])
    } else {
      route.beforeEnter = mixin.beforeEnter
    }
    if (route.children?.length) {
      routerEnterFunc(route.children)
    }
  })
}
routerEnterFunc(routes)
export default routes
