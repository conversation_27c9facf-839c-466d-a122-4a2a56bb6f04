declare module '*.vue' {
  const component: any
  export default component
}

declare module '*.png' {
  const png: any
  export default png
}

declare module '*.ts' {
  const ts: any
  export default ts
}

declare module 'diff' {
  const diffWords: any
  export { diffWords }
}

declare module 'diff-match-patch' {
  const diffWords: any
  export default diffWords
}

interface Window {
  apmPlus?: (...rest: any[]) => any
  aegis?: any
}
