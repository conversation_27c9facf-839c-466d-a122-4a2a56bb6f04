import { ElButton, ElTable } from 'element-plus'
import { useTable } from './hooks'
import Modal from './modal'
import { useModal } from './hooks'
import SearchContainer from '@/components/search-container'
export default defineComponent({
  props: {
    view: Boole<PERSON>
  },
  emits: ['selectContent'],
  setup(props, { emit }) {
    const view = toRef(props, 'view')
    const { visible, modalId, openModal } = useModal()
    const { listParams, isLoading, tableColumn, refetchData } = useTable(
      openModal,
      view,
      (content: string) => emit('selectContent', content)
    )
    return () => (
      <div>
        {!view.value && (
          <SearchContainer>
            <div class="flex-just-end w-full">
              <ElButton onClick={() => openModal()} type="primary">
                新增
              </ElButton>
            </div>
          </SearchContainer>
        )}
        <ElTable data={listParams.list} style={{ width: '100%' }} v-loading={isLoading.value}>
          {tableColumn}
        </ElTable>
        <Modal
          id={modalId.value}
          onRefetch={refetchData}
          v-model={visible.value}
          view={view.value}
        />
      </div>
    )
  }
})
