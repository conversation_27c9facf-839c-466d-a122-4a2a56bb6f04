import { useDelMarkTaskTemplate, getMarkTaskTemplateList } from '@/api/marktasktemplate'
import { MarkTaskTemplateListItem } from '@/api/marktasktemplate/type'
import DelButton from '@/components/del-button'
import { generateTableList } from '@znzt-fe/utils'
import { useList, useElModal } from '@znzt-fe/hooks'
import { ElButton, ElButtonGroup } from 'element-plus'

export const useTable = (
  openModal: (id: string) => void,
  view: Ref<boolean>,
  selectContent: (content: string) => void
) => {
  const { listParams, isLoading, refetchData } = useList<MarkTaskTemplateListItem>({
    getList: getMarkTaskTemplateList
  })
  const { mutate: delMarkTaskTemplate } = useDelMarkTaskTemplate({
    onSuccess: refetchData
  })
  const tableColumn = generateTableList<MarkTaskTemplateListItem>([
    {
      prop: 'id',
      label: 'id'
    },
    {
      prop: 'name',
      label: '名称'
    },
    {
      prop: 'desc',
      label: '描述'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      slots: (scope) => dayjs(scope.row.createTime * 1000).format('YYYY-MM-DD')
    },
    {
      prop: 'operation',
      label: '操作',
      slots: (scope) =>
        view.value ? (
          <ElButtonGroup>
            <ElButton link onClick={() => openModal(scope.row.id)} type="primary">
              查看
            </ElButton>
            <ElButton link onClick={() => selectContent(scope.row.content)} type="primary">
              选择
            </ElButton>
          </ElButtonGroup>
        ) : (
          <ElButtonGroup>
            <ElButton link onClick={() => openModal(scope.row.id)} type="primary">
              编辑
            </ElButton>
            <DelButton onClick={() => delMarkTaskTemplate({ id: scope.row.id })}>删除</DelButton>
          </ElButtonGroup>
        )
    }
  ])
  return {
    isLoading,
    tableColumn,
    listParams,
    refetchData
  }
}

export const useModal = useElModal
