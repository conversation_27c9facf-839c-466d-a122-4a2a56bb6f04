import ModalBottom from '@/components/modal-bottom'
import QuillEditor from '@/components/quill-editor'
import { useOptions } from '@/views/mark/task/modal/hooks'
import QuestionSetting from '@/views/mark/task/modal/questionSetting'
import { ScoreType } from '@/views/mark/util'
import {
  ElButton,
  ElCheckbox,
  ElCheckboxGroup,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect
} from 'element-plus'
import { useForm, useModal } from './hooks'
export default defineComponent({
  props: {
    modelValue: Boolean,
    id: {
      type: String,
      default: ''
    },
    view: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'refetch'],
  setup(props, { emit }) {
    const { id, view } = toRefs(props)
    const visible = useModel(props, 'modelValue')
    /** 处理options */
    const { markStandardList, markStandardListLoading, optionsMap, generateOption } = useOptions()
    const { title, isEdit } = useModal(id, view)
    const refetch = () => emit('refetch')
    const {
      formRef,
      form,
      rules,
      resetFormData,
      submit,
      checkIdList,
      scoreTypeChange,
      displayModeCheckIdList
    } = useForm(isEdit, refetch, visible, id)
    return () => (
      <ElDialog title={title.value} v-model={visible.value}>
        <ElForm disabled={view.value} labelWidth="100px" model={form} ref={formRef} rules={rules}>
          <ElFormItem label="模板名称" prop="name">
            <ElInput class="!w-full" v-model={form.name} />
          </ElFormItem>
          <ElFormItem label="模板描述" prop="desc">
            <ElInput class="!w-full" v-model={form.desc} />
          </ElFormItem>
          <ElFormItem label="评分类型" prop="content.scoreType">
            <ElSelect class="!w-full" onChange={scoreTypeChange} v-model={form.content.scoreType}>
              {generateOption('scoreTypeList')}
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="简要描述">
            <ElInput type="textarea" v-model={form.content.desc} />
          </ElFormItem>
          <ElFormItem
            label="标注标准"
            prop="content.standardId"
            v-loading={markStandardListLoading.value}>
            <div class="w-full">
              <ElCheckboxGroup v-model={checkIdList.value}>
                {markStandardList.value?.map((item) => (
                  <ElCheckbox key={item.id} size="small" value={item.id}>
                    {item.name}
                  </ElCheckbox>
                ))}
              </ElCheckboxGroup>
              {!!markStandardList.value?.find((item) => item.id === form.content.standardId)
                ?.content && (
                <QuillEditor
                  height="100px"
                  modelValue={
                    markStandardList.value?.find((item) => item.id === form.content.standardId)
                      ?.content || ''
                  }
                />
              )}
            </div>
          </ElFormItem>
          <ElFormItem label="参与用户">
            <ElCheckboxGroup v-model={form.content.unames}>
              {optionsMap.value?.unameList.map((item) => (
                <ElCheckbox key={item.id} label={item.id} value={item.id} />
              ))}
            </ElCheckboxGroup>
          </ElFormItem>
          <ElFormItem label="附加题目设置">
            <QuestionSetting
              isMarkSession={form.content.scoreType === ScoreType['MarkSession']}
              isMedia={form.content.scoreType === ScoreType['MarkScoreTypeAudioAndVideo']}
              v-model={form.content.questionSetting}
            />
          </ElFormItem>
          <ElFormItem label="展示模式" prop="content.questionDisplayMode">
            <ElCheckboxGroup v-model={displayModeCheckIdList.value}>
              {optionsMap.value?.questionDisplayModeList.map((item) => (
                <ElCheckbox key={item.id} label={item.name} value={item.id} />
              ))}
            </ElCheckboxGroup>
          </ElFormItem>
        </ElForm>
        {view.value ? (
          <div class="flex-just-end">
            <ElButton onClick={() => (visible.value = false)}>关闭</ElButton>
          </div>
        ) : (
          <ModalBottom onConfirm={submit} onReset={resetFormData} />
        )}
      </ElDialog>
    )
  }
})
