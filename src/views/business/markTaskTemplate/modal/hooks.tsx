import {
  AnnotationGranularity,
  AnnotationRole,
  CreateMarkTaskParams,
  NumberConfigType,
  QuestionDisplayMode,
  QuestionSetting,
  QuestionType
} from '@/api/marktask/type'
import {
  useCreateMarkTaskTemplate,
  useEditMarkTaskTemplate,
  useGetMarkTaskTemplateDetail
} from '@/api/marktasktemplate'
import { MarkTaskTemplateListItem } from '@/api/marktasktemplate/type'
import { ScoreType } from '@/views/mark/util'
import { useCheckBoxSingle, useElForm, useRule } from '@znzt-fe/hooks'
import { afterDecorator, beforeNextTick, resetObj } from '@znzt-fe/utils'
import { ElMessage } from 'element-plus'
export type MarkTaskTemplateInitData = Omit<MarkTaskTemplateListItem, 'content' | 'createTime'> & {
  content: Pick<
    CreateMarkTaskParams,
    'standardId' | 'unames' | 'scoreType' | 'desc' | 'questionSetting' | 'questionDisplayMode'
  >
}
export const MarkScoreTypeWritingEvalQuestionSetting: QuestionSetting[] = [
  {
    type: QuestionType.multipleChoice,
    options: [],
    mustAnswer: true,
    numberConfig: {
      min: 0,
      max: 100,
      percision: 1,
      type: NumberConfigType['input']
    },
    name: '加分点',
    disabled: true,
    annotationRole: AnnotationRole['Annotationer'],
    annotationGranularity: AnnotationGranularity['Assistant']
  },
  {
    type: QuestionType.multipleChoice,
    options: [],
    mustAnswer: true,
    numberConfig: {
      min: 0,
      max: 100,
      percision: 0,
      type: NumberConfigType['input']
    },
    name: '扣分点',
    disabled: true,
    annotationRole: AnnotationRole['Annotationer'],
    annotationGranularity: AnnotationGranularity['Assistant']
  },
  {
    type: QuestionType.single,
    name: '诉求满足度',
    mustAnswer: true,
    options: [],
    numberConfig: {
      min: 0,
      max: 0,
      percision: 0,
      type: NumberConfigType['input']
    },
    disabled: true,
    annotationRole: AnnotationRole['Annotationer'],
    annotationGranularity: AnnotationGranularity['Assistant']
  }
]
export const useForm = (
  isEdit: Ref<boolean>,
  refetch: () => void,
  visible: Ref<boolean>,
  id: Ref<string>
) => {
  const scoreTypeChange = (e: ScoreType) => {
    if (e === ScoreType['MarkScoreTypeWritingEval']) {
      form.content.questionSetting = MarkScoreTypeWritingEvalQuestionSetting
    }
  }
  const initData: MarkTaskTemplateInitData = {
    name: '',
    id: '',
    desc: '',
    content: {
      standardId: '',
      unames: [],
      scoreType: '',
      desc: '',
      questionSetting: [],
      questionDisplayMode: QuestionDisplayMode['Horizontal']
    }
  }
  const { formRef, validate, clearValidate, validateField, resetForm, form } = useElForm(initData)

  const rules = useRule({
    name: '标注标签名称不能为空',
    'content.scoreType': '请输入评分类型',
    'content.standardId': '请选择标注标准',
    'content.questionDisplayMode': '请选择展示模式'
  })
  const options = {
    onSuccess() {
      ElMessage.success('操作成功')
      refetch()
      visible.value = false
    }
  }
  const resetFormData = async () => {
    resetForm()
    clearValidate()
    form.id = id.value
    checkId.value = ''
    displayModeCheckId.value = QuestionDisplayMode['Horizontal']
  }
  const getDetail = () => {
    if (!form.id) return
    getMarkTaskTemplateDetail({
      id: form.id
    })
  }
  const { checkIdList, checkId } = useCheckBoxSingle(form, 'content.standardId')
  const { checkIdList: displayModeCheckIdList, checkId: displayModeCheckId } =
    useCheckBoxSingle<QuestionDisplayMode>(form, 'content.questionDisplayMode')
  whenever(visible, beforeNextTick(afterDecorator(resetFormData, getDetail)))
  const { mutate: getMarkTaskTemplateDetail } = useGetMarkTaskTemplateDetail({
    onSuccess(data) {
      form.name = data.name
      form.desc = data.desc
      const query: MarkTaskTemplateInitData['content'] = JSON.parse(data.content)
      checkId.value = query.standardId
      displayModeCheckId.value = query.questionDisplayMode
      resetObj(form.content, query)
    }
  })
  const { mutate: createMarkTaskTemplate } = useCreateMarkTaskTemplate(options)
  const { mutate: editMarkTaskTemplate } = useEditMarkTaskTemplate(options)
  const submit = async () => {
    const result = await validate()
    if (!result) return
    if (
      form.content.scoreType !== ScoreType['MarkSession'] &&
      form.content.scoreType !== ScoreType['MarkScoreTypeAudioAndVideo']
    ) {
      form.content.questionSetting.forEach((item) => {
        item.annotationRole = AnnotationRole['Annotationer']
        item.annotationGranularity = AnnotationGranularity['Assistant']
      })
    }
    const { content, ...rest } = form
    const query = {
      content: JSON.stringify(content),
      ...rest
    }
    isEdit.value ? editMarkTaskTemplate(query) : createMarkTaskTemplate(query)
  }
  return {
    formRef,
    validate,
    clearValidate,
    validateField,
    resetFormData,
    form,
    rules,
    submit,
    checkIdList,
    displayModeCheckIdList,
    scoreTypeChange
  }
}

export const useModal = (id: Ref<string>, view: Ref<boolean>) => {
  const isEdit = computed(() => !!id.value)
  const title = computed(() =>
    view.value ? '查看标注任务模板' : !isEdit.value ? '新增标注任务模板' : '编辑标注任务模板'
  )
  return {
    title,
    isEdit
  }
}
