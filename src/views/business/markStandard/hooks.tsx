import { useDelMarkStandard, getMarkStandardList } from '@/api/markstandard'
import { MarkStandardListItem } from '@/api/markstandard/type'
import DelButton from '@/components/del-button'
import QuillEditor from '@/components/quill-editor'
import { generateTableList } from '@znzt-fe/utils'
import { useList, useElModal } from '@znzt-fe/hooks'
import { ElButton, ElButtonGroup } from 'element-plus'

export const useTable = (openModal: (id: string) => void) => {
  const { listParams, isLoading, refetchData } = useList<MarkStandardListItem>({
    getList: getMarkStandardList
  })
  const { mutate: delMarkStandard } = useDelMarkStandard({
    onSuccess: refetchData
  })
  const tableColumn = generateTableList<MarkStandardListItem>([
    {
      prop: 'id',
      label: 'id'
    },
    {
      prop: 'name',
      label: '名称'
    },
    {
      prop: 'content',
      label: '内容',
      slots: (scope) => <QuillEditor edit={false} v-model={scope.row.content} />
    },
    {
      prop: 'createTime',
      label: '创建时间',
      slots: (scope) => dayjs(scope.row.createTime * 1000).format('YYYY-MM-DD')
    },
    {
      prop: 'operation',
      label: '操作',
      slots: (scope) => (
        <ElButtonGroup>
          <ElButton link onClick={() => openModal(scope.row.id)} type="primary">
            编辑
          </ElButton>
          <DelButton onClick={() => delMarkStandard({ id: scope.row.id })}>删除</DelButton>
        </ElButtonGroup>
      )
    }
  ])
  return {
    isLoading,
    tableColumn,
    listParams,
    refetchData
  }
}

export const useModal = useElModal
