import { useElForm, useRule } from '@znzt-fe/hooks'
import {
  useCreateMarkStandard,
  useEditMarkStandard,
  useGetMarkStandardDetail
} from '@/api/markstandard'
import { ElMessage } from 'element-plus'
import { afterDecorator, beforeNextTick } from '@znzt-fe/utils'
export const useForm = (
  isEdit: Ref<boolean>,
  refetch: () => void,
  visible: Ref<boolean>,
  id: Ref<string>
) => {
  const initData = {
    name: '',
    id: '',
    content: ''
  }
  const { formRef, validate, clearValidate, validateField, resetForm, form, event } =
    useElForm(initData)
  const rules = useRule({
    name: '标注标准名称不能为空',
    content: {
      message: '标注标准内容不能为空',
      validator: () => !!(form.content && form.content !== '<p><br></p>')
    }
  })
  const options = {
    onSuccess() {
      ElMessage.success('操作成功')
      refetch()
      visible.value = false
    }
  }
  const resetFormData = async () => {
    resetForm()
    clearValidate()
    form.id = id.value
  }
  const getDetail = () => {
    if (!form.id) return
    getMarkStandardDetail({
      id: form.id
    })
  }

  whenever(visible, beforeNextTick(afterDecorator(resetFormData, getDetail)))

  const validateContent = () => validateField('content')

  const { mutate: getMarkStandardDetail } = useGetMarkStandardDetail({
    onSuccess(data) {
      form.name = data.name
      form.content = data.content
    }
  })
  const { mutate: createMarkStandard } = useCreateMarkStandard(options)
  const { mutate: editMarkStandard } = useEditMarkStandard(options)
  const submit = async () => {
    const result = await validate()
    if (!result) return
    isEdit.value
      ? editMarkStandard(form)
      : createMarkStandard({ name: form.name, content: form.content })
  }

  return {
    formRef,
    validate,
    clearValidate,
    validateField,
    resetFormData,
    form,
    rules,
    submit,
    validateContent,
    event
  }
}

export const useModal = (id: Ref<string>) => {
  const isEdit = computed(() => !!id.value)
  const title = computed(() => (!isEdit.value ? '新增标注标准' : '编辑标注标准'))
  return {
    title,
    isEdit
  }
}
