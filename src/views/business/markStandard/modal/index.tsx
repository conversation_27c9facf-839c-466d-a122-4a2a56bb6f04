import { ElDialog, ElForm, ElFormItem, ElInput } from 'element-plus'
import { useForm, useModal } from './hooks'
import QuillEditor from '@/components/quill-editor'
import ModalBottom from '@/components/modal-bottom'
export default defineComponent({
  props: {
    modelValue: Boolean,
    id: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue', 'refetch'],
  setup(props, { emit }) {
    const { id } = toRefs(props)
    const visible = useModel(props, 'modelValue')
    const { title, isEdit } = useModal(id)
    const refetch = () => emit('refetch')
    const { formRef, form, rules, resetFormData, submit, validateContent, event } = useForm(
      isEdit,
      refetch,
      visible,
      id
    )
    return () => (
      <ElDialog title={title.value} v-model={visible.value}>
        <ElForm {...event} model={form} ref={formRef} rules={rules}>
          <ElFormItem label="名称" prop="name">
            <ElInput v-model={form.name} />
          </ElFormItem>
          <ElFormItem label="内容" prop="content">
            <QuillEditor edit={true} onBlur={validateContent} v-model={form.content} />
          </ElFormItem>
        </ElForm>
        <ModalBottom onConfirm={submit} onReset={resetFormData} />
      </ElDialog>
    )
  }
})
