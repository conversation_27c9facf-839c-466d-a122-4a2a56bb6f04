<template>
  <section class="node-dialog">
    <el-dialog
      v-model="state.show"
      title="修改角色"
      width="450px"
      @close="close()"
      :close-on-click-modal="false">
      <el-form :model="state.detail" label-width="100px" ref="formRef">
        <el-form-item label="角色" prop="roleId">
          <el-select v-model="state.detail.roleId">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="实验室权限" prop="labPermission">
          <el-select v-model="state.detail.labPermission" multiple @change="labPermissionChange">
            <el-option
              v-for="item in labPermissionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="confirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </section>
</template>

<script lang="ts" setup>
import $http from '@/api'
import { RoleId } from '@/api/user/type'
import { useUserStore } from '@/store/user'
import { ElMessage, ElMessageBox } from 'element-plus'

const labPermissionOptions = [
  {
    label: '默认',
    value: 'default'
  },
  {
    label: '对话实验室',
    value: 'chat'
  },
  {
    label: '任务实验室',
    value: 'task'
  },
  {
    label: '标注实验室',
    value: 'mark'
  },
  {
    label: '图像实验室',
    value: 'image'
  }
]
const props = defineProps({
  detail: {
    type: Object,
    default: () => ({})
  }
})

const state: any = reactive({
  show: true,
  currentId: props.detail.roleId,
  detail: {
    roleId: props.detail.roleId,
    labPermission: props.detail.labPermission || ['default']
  }
})
const labPermissionChange = (permissions: string[]) => {
  const last = permissions[permissions.length - 1]
  if (last === 'default') {
    state.detail.labPermission = ['default']
  } else {
    state.detail.labPermission = permissions.filter((item: string) => item !== 'default')
  }
}
const userStore = useUserStore()
const { isSystemAdmin } = storeToRefs(userStore)
const options = computed(() => [
  {
    label: '业务用户',
    value: RoleId.User
  },
  {
    label: '业务管理员',
    value: RoleId.BusAdmin
  },
  {
    label: '业务超管',
    value: RoleId.BusSuperAdmin,
    disabled: !isSystemAdmin.value
  }
])
const emits = defineEmits(['close'])
const confirm = async () => {
  if (!state.detail.roleId) {
    ElMessage.error('请选择角色!')
    return
  }
  const labPermission = (state.detail.labPermission || []).sort().join('-')
  const oldLabPermission = (props.detail.labPermission || []).sort().join('-')
  const isCancelLabPermission = !labPermission.includes(oldLabPermission)
  const isDefault =
    state.detail.labPermission.includes('default') ||
    props.detail.labPermission?.includes('default')
  if (!isDefault && isCancelLabPermission) {
    const result = await ElMessageBox.confirm(
      '该操作将禁用这个业务线在这个模型上的调用权限，请确保在线业务不使用此模型',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    if (!result) {
      return
    }
  }
  await $http.updateRole({
    relationId: props.detail.relationId,
    fromRoleId: state.currentId,
    toRoleId: state.detail.roleId,
    labPermission: state.detail.labPermission
  })
  ElMessage.success('修改成功!')
  close(true)
}
const close = (refresh = false) => {
  state.show = false
  emits('close', refresh)
}
</script>
<style scoped lang="less"></style>
