<template>
  <section class="node-dialog">
    <el-dialog
      v-model="state.show"
      title="限制每天调用次数（0表示无限制）"
      width="450px"
      @close="close()"
      :close-on-click-modal="false">
      <el-form :model="state.detail" label-width="80px" ref="formRef">
        <el-form-item label="调用次数" prop="apiCntLimit">
          <el-input-number
            v-model="state.detail.apiCntLimit"
            :precision="0"
            :step="1"
            :min="0"
            placeholder="最大数量" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="confirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </section>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import $http from '@/api'
import useUserStore from '@/store/user'

const props = defineProps({
  detail: {
    type: Object,
    default: () => ({})
  }
})

const userStore = useUserStore()
const state: any = reactive({
  show: true,
  detail: {
    apiCntLimit: props.detail.apiCntLimit
  }
})
const emits = defineEmits(['close'])
const confirm = async () => {
  await $http.controlUser({
    businessId: userStore.current?.businessId,
    relationIds: [props.detail.relationId],
    apiCntLimit: state.detail.apiCntLimit,
    actionControl: 4
  })
  ElMessage.success('修改成功!5分钟后生效')
  close(true)
}
const close = (refresh = false) => {
  state.show = false
  emits('close', refresh)
}
</script>
<style scoped lang="less"></style>
