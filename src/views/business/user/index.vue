<template>
  <el-tabs v-model="state.type" class="demo-tabs">
    <el-tab-pane label="已审核" :name="2">
      <List :status="2"></List>
    </el-tab-pane>
    <el-tab-pane label="待审核" :name="1" v-if="isGuestBus">
      <List :status="1"></List>
    </el-tab-pane>
  </el-tabs>
</template>

<script lang="ts" setup>
import List from './list.vue'
import useUserStore from '@/store/user'

const userStore = useUserStore()
const isGuestBus = computed(() => {
  const { current } = userStore
  return current.businessId === 1
})
const state: any = reactive({
  type: 2
})
watch(
  () => isGuestBus.value,
  (val) => {
    if (!val) {
      state.type = 2
    }
  }
)
</script>
<style scoped lang="less"></style>
