import { use<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/api/user'
import { SettleBussParams } from '@/api/user/type'
import useUserStore from '@/store/user'
import { useElForm, useRule } from '@znzt-fe/hooks'
import { beforeNextTick } from '@znzt-fe/utils'
import { ElMessage } from 'element-plus'
const reg = /^[A-Za-z_]+$/
export const useForm = (visible: Ref<boolean>, refetch?: () => void) => {
  const userStore = useUserStore()
  const initData: SettleBussParams = {
    name: '',
    zhName: '',
    businessId: -1
  }
  const { formRef, validate, clearValidate, resetForm, form, event } = useElForm(initData)
  const rules = useRule({
    name: {
      validator: () => reg.test(form.name),
      message: '服务名称需要英文字母或者下划线'
    },
    zhName: '中文名不能为空'
  })
  const resetFormData = async () => {
    resetForm()
    clearValidate()
  }
  const { mutate: settleBussMutate, isLoading: settleBussLoading } = useSettleBuss({
    onSuccess: () => {
      refetch?.()
      visible.value = false
      ElMessage.success('创建成功')
    }
  })
  whenever(visible, beforeNextTick(resetFormData))
  const onConfirm = async () => {
    const result = await validate()
    if (!result) return
    settleBussMutate({ ...form, businessId: userStore.current.businessId })
  }
  return {
    onConfirm,
    settleBussLoading,
    rules,
    formRef,
    resetFormData,
    event,
    form
  }
}
