import { <PERSON><PERSON>utton, ElDialog, ElForm, ElFormItem, ElInput } from 'element-plus'
import { RoleId } from '@/api/markrole/type'
import { useForm } from './hooks'
export default defineComponent({
  props: {
    modelValue: Boolean,
    id: Number,
    refetch: {
      type: Function as PropType<() => void>
    },
    roleName: {
      type: String,
      default: ''
    },
    roleId: {
      type: Number as PropType<RoleId>,
      default: RoleId.Observer
    }
  },
  setup(props) {
    const visible = useModel(props, 'modelValue')
    const { event, form, formRef, rules, onConfirm, settleBussLoading } = useForm(
      visible,
      props.refetch
    )
    return () => (
      <ElDialog title={`创建用于线上服务的API Key`} v-model={visible.value}>
        {{
          default: () => (
            <ElForm labelWidth={100} {...event} model={form} ref={formRef} rules={rules}>
              <ElFormItem label="服务名称" prop="name">
                <ElInput v-model={form.name}>{{ append: () => '_svc' }}</ElInput>
              </ElFormItem>
              <ElFormItem label="中文名" prop="zhName">
                <ElInput v-model={form.zhName} />
              </ElFormItem>
            </ElForm>
          ),
          footer: () => (
            <span class="dialog-footer">
              <ElButton onClick={() => (visible.value = false)}>取消</ElButton>
              <ElButton loading={settleBussLoading.value} onClick={onConfirm} type="primary">
                确定
              </ElButton>
            </span>
          )
        }}
      </ElDialog>
    )
  }
})
