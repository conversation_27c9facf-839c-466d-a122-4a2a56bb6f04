<template>
  <el-dialog v-model="visible" title="API密钥" width="540px">
    <div class="tips px-4 !mb-6">
      请安全地使用 API 密钥。请勿分享这类密钥，也不要将其嵌入公众可以查看的代码中。
    </div>
    <div class="secret-list pb-5 px-4">
      <div
        v-for="(item, idx) in secrets"
        :key="idx"
        class="secret-item text-sm flex justify-between border-b border-gray-200 border-solid pb-3">
        <p>{{ item }}</p>
        <el-button link class="copy-btn" type="primary" @click="copy(item)">复制</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { copyText } from '@znzt-fe/utils'
import { getUserSecret } from '@/api/user'
const props = defineProps<{ modelValue: boolean; data: any }>()
const visible = useModel(props, 'modelValue')
const secrets = ref([])
const { mutate: getUserSecretMutation } = getUserSecret({
  onSuccess: (res: any) => {
    console.log('res', res)
  }
})
watch(
  () => props.data,
  () => {
    getUserSecretMutation(props.data)
  }
)

const copy = async (text: string) => {
  await copyText(text)
  ElMessage.success('复制成功')
}
</script>

<style scoped lang="less">
.tips {
  color: #666;
  font-size: 12px;
  margin-bottom: 12px;
}

.secret-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.secret-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.copy-btn {
  white-space: nowrap;
}
</style>
