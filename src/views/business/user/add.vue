<template>
  <section class="node-dialog">
    <el-dialog
      v-model="state.show"
      title="新增用户"
      width="450px"
      @close="close()"
      :close-on-click-modal="false">
      <el-form :model="state.detail" ref="formRef">
        <el-form-item label="用户" prop="uname">
          <SearchUser v-model="state.detail.uname"></SearchUser>
          <!-- <el-select
            v-model="state.detail.uname"
            :remote-method="remoteMethod"
            filterable
            remote
            clearable>
            <el-option
              v-for="item in state.options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select> -->
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="confirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </section>
</template>

<script lang="ts" setup>
import SearchUser from '@/components/search-user'
import { ElMessage } from 'element-plus'
import $http from '@/api'
import useUserStore from '@/store/user'
import { RoleId } from '@/api/user/type'
const userStore = useUserStore()
const state: any = reactive({
  show: true,
  detail: {}
})
const emits = defineEmits(['close'])
const confirm = async () => {
  const { detail } = state
  if (!detail.uname) {
    ElMessage.error('请选择要添加的用户！')
    return
  }
  const params = {
    uname: detail.uname,
    roleId: RoleId.User,
    businessId: userStore.current?.businessId
  }
  await $http.userSettle(params).finally(() => {
    state.disabled = false
  })
  ElMessage.success({
    message: '添加用户成功！5分钟后生效',
    duration: 3000
  })
  close(true)
}
const close = (refresh = false) => {
  state.show = false
  emits('close', refresh)
}
</script>
<style scoped lang="less">
.node-dialog {
  :deep(.el-dialog) {
    .el-dialog__body {
      padding-top: 8px;

      .el-table {
        .el-input,
        .el-select {
          width: 100%;
        }
      }
    }
  }
}
</style>
