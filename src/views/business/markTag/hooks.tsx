import { useDelMarkTag, getMarkTagList } from '@/api/marktag'
import { MarkTagListItem } from '@/api/marktag/type'
import DelButton from '@/components/del-button'
import { generateTableList } from '@znzt-fe/utils'
import { useList, useElModal } from '@znzt-fe/hooks'
import { ElButton, ElButtonGroup, ElTag } from 'element-plus'

export const useTable = (openModal: (id: string) => void) => {
  const { listParams, isLoading, refetchData } = useList<MarkTagListItem>({
    getList: getMarkTagList
  })
  const { mutate: delMarkTag } = useDelMarkTag({
    onSuccess: refetchData
  })
  const tableColumn = generateTableList<MarkTagListItem>([
    {
      prop: 'id',
      label: 'id'
    },
    {
      prop: 'name',
      label: '名称'
    },
    {
      prop: 'desc',
      label: '描述'
    },
    {
      prop: 'color',
      label: '颜色',
      slots: (scope) => (
        <ElTag color={scope.row.color} disableTransitions>
          <span style={{ color: 'white' }}>{scope.row.color}</span>
        </ElTag>
      )
    },
    {
      prop: 'createTime',
      label: '创建时间',
      slots: (scope) => dayjs(scope.row.createTime * 1000).format('YYYY-MM-DD')
    },
    {
      prop: 'operation',
      label: '操作',
      slots: (scope) => (
        <ElButtonGroup>
          <ElButton link onClick={() => openModal(scope.row.id)} type="primary">
            编辑
          </ElButton>
          <DelButton onClick={() => delMarkTag({ id: scope.row.id })}>删除</DelButton>
        </ElButtonGroup>
      )
    }
  ])
  return {
    isLoading,
    tableColumn,
    listParams,
    refetchData
  }
}

export const useModal = useElModal
