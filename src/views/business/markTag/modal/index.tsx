import { ElColorPicker, ElDialog, ElForm, ElFormItem, ElInput, ElTag } from 'element-plus'
import { useForm, useModal } from './hooks'
import ModalBottom from '@/components/modal-bottom'
export default defineComponent({
  props: {
    modelValue: Boolean,
    id: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue', 'refetch'],
  setup(props, { emit }) {
    const { id } = toRefs(props)
    const visible = useModel(props, 'modelValue')
    const { title, isEdit } = useModal(id)
    const refetch = () => emit('refetch')
    const { formRef, form, rules, resetFormData, submit } = useForm(isEdit, refetch, visible, id)
    return () => (
      <ElDialog title={title.value} v-model={visible.value}>
        <ElForm labelWidth="55px" model={form} ref={formRef} rules={rules}>
          <ElFormItem label="名称" prop="name">
            <ElInput v-model={form.name} />
          </ElFormItem>
          <ElFormItem label="描述" prop="desc">
            <ElInput v-model={form.desc} />
          </ElFormItem>
          <ElFormItem label="颜色" prop="color">
            <div class="v-center">
              <ElColorPicker v-model={form.color} />
              {form.color && (
                <ElTag
                  color={form.color}
                  disableTransitions
                  style={{ color: 'white', marginLeft: '10px' }}>
                  {form.color}
                </ElTag>
              )}
            </div>
          </ElFormItem>
        </ElForm>
        <ModalBottom onConfirm={submit} onReset={resetFormData} />
      </ElDialog>
    )
  }
})
