import { useElForm, useRule } from '@znzt-fe/hooks'
import { useCreateMarkTag, useEditMarkTag, useGetMarkTagDetail } from '@/api/marktag'
import { ElMessage } from 'element-plus'
import { afterDecorator, beforeNextTick } from '@znzt-fe/utils'
export const useForm = (
  isEdit: Ref<boolean>,
  refetch: () => void,
  visible: Ref<boolean>,
  id: Ref<string>
) => {
  const initData = {
    name: '',
    id: '',
    desc: '',
    color: ''
  }
  const { formRef, validate, clearValidate, validateField, resetForm, form } = useElForm(initData)

  const rules = useRule({
    name: '标注标签名称不能为空',
    color: '标注标签颜色不能为空'
  })
  const options = {
    onSuccess() {
      ElMessage.success('操作成功')
      refetch()
      visible.value = false
    }
  }
  const resetFormData = async () => {
    resetForm()
    clearValidate()
    form.id = id.value
  }
  const getDetail = () => {
    if (!form.id) return
    getMarkTagDetail({
      id: form.id
    })
  }
  whenever(visible, beforeNextTick(afterDecorator(resetFormData, getDetail)))
  const { mutate: getMarkTagDetail } = useGetMarkTagDetail({
    onSuccess(data) {
      form.name = data.name
      form.desc = data.desc
      form.color = data.color
    }
  })
  const { mutate: createMarkTag } = useCreateMarkTag(options)
  const { mutate: editMarkTag } = useEditMarkTag(options)
  const submit = async () => {
    const result = await validate()
    if (!result) return
    isEdit.value ? editMarkTag(form) : createMarkTag(form)
  }

  return {
    formRef,
    validate,
    clearValidate,
    validateField,
    resetFormData,
    form,
    rules,
    submit
  }
}

export const useModal = (id: Ref<string>) => {
  const isEdit = computed(() => !!id.value)
  const title = computed(() => (!isEdit.value ? '新增标注标签' : '编辑标注标签'))
  return {
    title,
    isEdit
  }
}
