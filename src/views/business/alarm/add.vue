<template>
  <section class="node-dialog">
    <el-dialog
      v-model="state.show"
      :title="state.detail.id ? '编辑报警信息' : '新增报警'"
      width="450px"
      @close="close()"
      :close-on-click-modal="false">
      <el-form :model="state.detail" :rules="state.rules" label-width="120px" ref="formRef">
        <el-form-item label="报警标题" prop="title">
          <el-input v-model="state.detail.title" maxlength="20" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="机器人token" prop="robotToken">
          <el-input v-model="state.detail.robotToken"></el-input>
        </el-form-item>
        <el-form-item label="机器人关键词" prop="robotKeyWord">
          <el-input v-model="state.detail.robotKeyWord"></el-input>
        </el-form-item>
        <el-form-item label="是否at全员" prop="atAll">
          <el-radio-group v-model="state.detail.atAll">
            <el-radio :value="1">是</el-radio>
            <el-radio :value="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="at的电话号码（用,分割）" prop="atPhones">
          <el-input type="textarea" :rows="5" v-model="state.detail.atPhones"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="confirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </section>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { isEmpty } from 'lodash-es'
import $http from '@/api'
import useUserStore from '@/store/user'
import { useRule } from '@znzt-fe/hooks'

const props = defineProps({
  detail: {
    type: Object,
    default: () => ({})
  }
})
const state: any = reactive({
  show: true,
  detail: {
    title: '',
    robotToken: '',
    robotKeyWord: '',
    atAll: 0,
    atPhones: ''
  },
  rules: useRule(
    {
      title: [
        { required: true, message: '请输入报警标题', trigger: 'blur' },
        { min: 1, max: 30, message: '长度不能超过 20 个字符', trigger: 'blur' }
      ],
      robotToken: '请输入机器人token',
      robotKeyWord: '请输入机器人关键词',
      atAll: '请选择是否at全员'
    },
    { trigger: 'blur' }
  )
})
if (!isEmpty(props.detail)) {
  Object.assign(state.detail, {
    ...props.detail
  })
}
const userStore = useUserStore()
const formRef = ref()
const emits = defineEmits(['close'])
const confirm = async () => {
  const form = formRef.value
  const valid = await form.validate().catch(() => false)
  if (valid) {
    const params = {
      businessId: userStore.current.businessId,
      ...state.detail
    }
    if (props.detail.id) {
      await $http.updateAlarm(params).finally(() => {
        state.disabled = false
      })
      ElMessage.success('修改成功!')
    } else {
      await $http.addAlarm(params).finally(() => {
        state.disabled = false
      })
      ElMessage.success('添加成功!')
    }
    close(true)
  }
}
const close = (refresh = false) => {
  state.show = false
  emits('close', refresh)
}
</script>
<style scoped lang="less">
.node-dialog {
  :deep(.el-dialog) {
    .el-dialog__body {
      padding-top: 8px;

      .el-table {
        .el-input,
        .el-select {
          width: 100%;
        }
      }
    }
  }
}
</style>
