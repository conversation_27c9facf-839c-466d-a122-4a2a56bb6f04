<template>
  <el-tabs v-model="state.tab" class="demo-tabs">
    <el-tab-pane label="采购入库" :name="1" v-if="isSuperAdmin">
      <InStorageList v-if="state.tab === 1"></InStorageList>
    </el-tab-pane>
    <el-tab-pane label="资源池状态" :name="2" v-if="isSuperAdmin">
      <ResourcePoolList v-if="state.tab === 2"></ResourcePoolList>
    </el-tab-pane>
    <el-tab-pane label="sk状态" :name="3" v-if="!isSuperAdmin">
      <List v-if="state.tab === 3"></List>
    </el-tab-pane>
  </el-tabs>
</template>

<script lang="ts" setup>
import List from './list.vue'
import InStorageList from './in-storage-list.vue'
import ResourcePoolList from './resource-pool-list.vue'
import useUserStore from '@/store/user'
import { RoleId } from '@/api/user/type'

const userStore = useUserStore()
const current = userStore.current
const isSuperAdmin = current && current.roleId === RoleId.SuperAdmin
const state: any = reactive({
  tab: isSuperAdmin ? 1 : 3
})
</script>
<style scoped lang="less"></style>
