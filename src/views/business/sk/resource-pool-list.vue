<template>
  <section class="table-container">
    <el-table
      :data="getSkPoolListQuery.data.value?.list || []"
      style="width: 100%"
      :show-overflow-tooltip="true">
      <el-table-column prop="business" label="业务线" />
      <el-table-column prop="skPoolType" label="资源池类型" />
      <el-table-column prop="model" label="模型" />
      <el-table-column prop="handSrv" label="服务方" />
      <el-table-column prop="tpm" label="TPM" />
      <el-table-column prop="rpm" label="RPM" />
      <el-table-column prop="usingNum" label="在用SK数量" />
      <el-table-column prop="watingNUm" label="未启用SK数量" />
    </el-table>
  </section>
</template>

<script lang="ts" setup>
import { getSkPoolList } from '@/api/skpool'
import { useQuery } from '@znzt-fe/axios'
const getSkPoolListQuery = useQuery(['getSkPoolList'], getSkPoolList)
</script>
<style scoped lang="less">
.table-container {
  border-radius: 4px;
  background: white;
  padding: 12px;
}
</style>
