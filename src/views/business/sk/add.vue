<template>
  <section class="node-dialog">
    <el-dialog
      v-model="state.show"
      title="新增SK"
      width="450px"
      @close="close()"
      :close-on-click-modal="false">
      <el-form :model="state.detail" ref="formRef" label-width="95px">
        <el-form-item label="SK类型" prop="skType">
          <el-select v-model="state.detail.skType">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="锁定时长" prop="lockDuration" v-if="issFreeType">
          <el-input v-model="state.detail.lockDuration" disabled>
            <template #append>秒</template>
          </el-input>
          <el-tooltip
            effect="dark"
            content="免费账号RPM:20，每个sk需锁定3s，系统冗余为4s"
            placement="top">
            <el-icon class="icon-tips">
              <InfoFilled />
            </el-icon>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="qpsLimit" prop="qpsLimit" v-if="!issFreeType">
          <el-input-number
            v-model="state.detail.qpsLimit"
            type="number"
            :max="58"
            :min="1"
            :step="1">
            <template #append>次/秒</template>
          </el-input-number>
          <el-tooltip effect="dark" :content="tipsText" placement="top">
            <el-icon class="icon-tips">
              <InfoFilled />
            </el-icon>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="tpmLimit" prop="tpmLimit" v-if="!issFreeType">
          <el-input-number v-model="state.detail.tpmLimit" type="number" :min="0" :step="1">
            <template #append>token/分钟</template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="单价(人民币)" prop="cost" v-if="state.detail.skType !== 3">
          <el-input-number
            v-model="state.detail.cost"
            :precision="2"
            :min="0"
            :step="1"
            placeholder="单价（单位元）">
          </el-input-number>
        </el-form-item>
        <el-form-item label="SK" prop="skListStr">
          <el-input type="textarea" :rows="5" v-model="state.detail.skListStr"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="confirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </section>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import $http from '@/api'
import useUserStore from '@/store/user'
const userStore = useUserStore()
const state: any = reactive({
  show: true,
  detail: {
    skListStr: '',
    skType: 1,
    lockDuration: 4,
    qpsLimit: 58,
    cost: 0,
    tpmLimit: 0
  }
})
const options = [
  {
    label: '免费',
    value: 1
  },
  {
    label: '付费',
    value: 2
  },
  {
    label: '正式付费',
    value: 3
  }
]
const emits = defineEmits(['close'])
const confirm = async () => {
  const { detail } = state
  if (!detail.skListStr) {
    ElMessage.error('请输入SK!')
    return
  }
  const params = {
    ...state.detail,
    businessId: userStore.current?.businessId
  }
  if (issFreeType.value) {
    params.qpsLimit = 0
    params.tpmLimit = 0
  } else {
    params.lockDuration = 0
  }
  if (state.detail.skType === 3) {
    params.cost = 0
  } else {
    params.cost = state.detail.cost * 100
  }
  await $http.skAdd(params).finally(() => {
    state.disabled = false
  })
  ElMessage.success('添加SK成功！5分钟后生效')
  close(true)
}
const issFreeType = computed(() => {
  return state.detail.skType === 1
})
const tipsText = computed(() => {
  let text = ''
  switch (state.detail.skType) {
    case 2:
      text = '付费账号RPM:3500，qps约等于58'
      break
    case 3:
      text = '正式付费账号RPM:3500，qps约等于58'
      break
  }
  return text
})
const close = (refresh = false) => {
  state.show = false
  emits('close', refresh)
}
</script>
<style scoped lang="less">
.node-dialog {
  :deep(.el-dialog) {
    .el-dialog__body {
      padding-top: 8px;

      .el-input,
      .el-input-number,
      .el-select {
        width: 280px;
      }

      .el-table {
        .el-input,
        .el-select {
          width: 100%;
        }
      }
    }
  }

  .icon-tips {
    font-size: 18px;
    margin-left: 10px;
  }
}
</style>
