import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
import $http from '@/api'

const skTest = async (data: any) => {
  const loadingInstance = ElLoading.service({ fullscreen: true })
  const timeout = 15000
  setTimeout(() => {
    loadingInstance.close()
  }, timeout)
  const res: any = await $http
    .skTest({
      id: data.id,
      skType: data.type,
      sk: data.sk
    })
    .finally(() => {
      loadingInstance.close()
    })
  const { isOk, failReason } = res || {}
  if (isOk) {
    ElMessage.success('sk测试成功')
  } else {
    ElMessageBox.alert(failReason, 'sk测试失败', {
      confirmButtonText: '关闭'
    })
  }
}

const skControl = async (ids: number[], status: number) => {
  await $http.skControl({
    ids,
    controlAction: status
  })
}

const getOpList = async (id: number) => {
  return await $http.getOpList({
    id
  })
}

enum STATUS {
  DELETE = 1,
  STOP = 2,
  REUSE = 3
}

export default {
  skTest,
  skControl,
  STATUS,
  getOpList
}
