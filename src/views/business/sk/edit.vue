<template>
  <section class="node-dialog">
    <el-dialog
      v-model="state.show"
      title="编辑库内SK"
      width="620px"
      @close="close()"
      :close-on-click-modal="false">
      <el-form :model="state.detail" ref="formRef" label-width="115px">
        <el-form-item label="sk" prop="sk" required>
          <el-input type="textarea" :row="3" v-model="state.detail.sk" disabled></el-input>
        </el-form-item>
        <el-form-item label="入库日期" prop="date">
          <el-date-picker
            v-model="state.detail.date"
            type="date"
            placeholder="选择入库日期"
            :disabled="isUsing" />
        </el-form-item>
        <el-form-item label="业务线" prop="businessName">
          <el-select v-model="state.detail.businessName" :disabled="isUsing">
            <el-option
              v-for="item in commonStore.config?.businessList || []"
              :key="item.name"
              :label="item.name"
              :value="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="账户类型" prop="account">
          <el-select v-model="state.detail.account" :disabled="isUsing">
            <el-option
              v-for="item in commonStore.config?.accountList || []"
              :key="item.name"
              :label="item.name"
              :value="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="账号/邮箱" prop="accountNumber">
          <el-input v-model="state.detail.accountNumber"></el-input>
        </el-form-item>
        <el-form-item label="账号密码" prop="openAiPassword">
          <el-input v-model="state.detail.openAiPassword"></el-input>
        </el-form-item>
        <el-form-item label="RPM" prop="rpm" required>
          <el-input-number v-model="state.detail.rpm" :min="1"></el-input-number>
        </el-form-item>
        <el-form-item label="TPM" prop="tpm" required>
          <el-input-number v-model="state.detail.tpm" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="IPM" prop="ipm">
          <el-input-number v-model="state.detail.ipm" :min="0" :precision="0"></el-input-number>
        </el-form-item>
        <el-form-item label="组织" prop="organization">
          <el-input v-model="state.detail.organization"></el-input>
        </el-form-item>
        <el-form-item label="余额" prop="balance">
          <el-input v-model="state.detail.balance" :disabled="isUsing"></el-input>
        </el-form-item>
        <el-form-item label="渠道" prop="channel" required>
          <el-input v-model="state.detail.channel"></el-input>
        </el-form-item>
        <el-form-item label="单价" prop="price" required>
          <el-input v-model="state.detail.price" disabled></el-input>
        </el-form-item>
        <el-form-item label="支付方式" prop="payment">
          <el-input v-model="state.detail.payment"></el-input>
        </el-form-item>
        <el-form-item
          v-if="state.detail.emptionType === 2"
          label="月租到期时间"
          prop="expirationTime"
          required>
          <el-date-picker
            v-model="state.detail.expirationTime"
            type="date"
            placeholder="选择月租到期时间"
            :disabled-date="isDisabledDate" />
        </el-form-item>
        <el-form-item label="优先级" prop="priority" required>
          <el-slider v-model="state.detail.priority" :min="1" />
        </el-form-item>
        <el-form-item
          v-if="state.detail.accountType === 24 || state.detail.accountType === 25"
          label="apiSecret"
          prop="apiSecret">
          <el-input v-model="state.detail.apiSecret"></el-input>
        </el-form-item>
        <el-form-item label="location" prop="location">
          <el-input v-model="state.detail.location"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="confirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </section>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import useCommonStore from '@/store/common'
import { updateSk } from '@/api/business'
import { SkUpdateParams } from '@/api/business/type'
const commonStore = useCommonStore()
const emits = defineEmits(['close'])
const props = defineProps({
  value: {
    type: Object as PropType<any>,
    default: () => ({})
  }
})
const state = reactive({
  show: true,
  disabled: false,
  detail: {
    ...props.value,
    date: props.value.date * 1000,
    priority: props.value.priority || 100
  }
})
if (state.detail.emptionType === 2) {
  state.detail.expirationTime = props.value.expirationTime * 1000
}
const isUsing = state.detail.status === 2
const formRef = ref()
const confirm = async () => {
  const target = formRef.value
  const valid = await target.validate().catch(() => false)
  if (!valid) {
    return
  }
  const params: SkUpdateParams = {
    ...state.detail,
    date: state.detail.date ? state.detail.date / 1000 : undefined,
    expirationTime: state.detail.expirationTime ? state.detail.expirationTime / 1000 : undefined
  }
  state.disabled = true
  await updateSk(params).finally(() => {
    state.disabled = false
  })
  ElMessage.success('SK更新成功！5分钟后生效')
  close(true)
}
const close = (refresh = false) => {
  state.show = false
  emits('close', refresh)
}
const isDisabledDate = (date: Date) => {
  return date.getTime() < Date.now() - 86400000 + 5
}
</script>
<style scoped lang="less">
.node-dialog {
  :deep(.el-dialog) {
    .el-dialog__body {
      padding-top: 8px;

      .el-input,
      .el-input-number,
      .el-slider,
      .el-textarea,
      .el-select {
        width: 380px;
      }

      .el-table {
        .el-input,
        .el-select {
          width: 100%;
        }
      }
    }
  }

  .icon-tips {
    font-size: 18px;
    margin-left: 10px;
  }
}
</style>
