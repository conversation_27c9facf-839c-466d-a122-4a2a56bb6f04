<template>
  <SearchContainer>
    <el-form inline>
      <el-form-item class="!w-[240px]" label="状态">
        <el-select v-model="state.query.status">
          <el-option
            v-for="item in state.options"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item class="!w-[240px]">
        <el-input v-model.trim="state.query.sk" placeholder="sk" clearable> </el-input>
      </el-form-item>
      <el-button type="primary" @click="queryList">搜索</el-button>
    </el-form>
    <el-button type="primary" @click="exportResult" v-if="userStore.isBusSuperAdmin"
      >导出</el-button
    >
  </SearchContainer>
  <section class="table-container">
    <el-table :data="state.list" style="width: 100%" :show-overflow-tooltip="true">
      <el-table-column prop="sk" label="sk" width="150px" />
      <el-table-column prop="date" label="入库日期" :formatter="timeFormat" />
      <el-table-column prop="model" width="130px" label="账户类型" />
      <el-table-column prop="rpmLimit" label="RPM" />
      <el-table-column prop="tpmLimit" label="TPM" />
      <el-table-column prop="importContent.balance" label="余额" />
      <el-table-column prop="cost" label="单价(¥)" :formatter="costFormatter" />
      <el-table-column prop="delType" label="删除类型" />
      <el-table-column prop="delCode" label="删除码" />
      <el-table-column prop="stopTime" label="删除时间" width="120" :formatter="stopTimeFormat" />
      <el-table-column prop="delMsg" label="删除的具体信息" />
      <el-table-column prop="status" label="状态" :formatter="statusFormatter" />
      <el-table-column fixed="right" label="操作" width="200">
        <template #default="scope">
          <DelButton
            @click="stopSk(scope.row.id)"
            v-if="scope.row.status !== 1"
            size="small"
            confirm-text="确认停用"
            >停用</DelButton
          >
          <el-button
            v-if="scope.row.status === 1"
            text
            type="primary"
            size="small"
            @click="reuse(scope.row.id)"
            >启用</el-button
          >
          <el-button
            v-if="scope.row.status !== 3"
            text
            type="primary"
            size="small"
            @click="Action.skTest(scope.row)"
            >SK测试</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-model:pageNum="state.pageInfo.pageNum"
      v-model:page-size="state.pageInfo.pageSize"
      :total="state.pageInfo.total"
      @refresh="getList">
    </Pagination>
  </section>
</template>

<script lang="ts" setup>
import { Pagination } from '@znzt-fe/components'
import $http from '@/api'
import useUserStore from '@/store/user'
import SearchContainer from '@/components/search-container'
import Action from './action'
import DelButton from '@/components/del-button'
import { useOpenUrl, useBusinessId } from '@/hooks/useBusinessCode'
const { openUrl } = useOpenUrl()
const exportResult = () =>
  openUrl('/openmis/sk/export', {
    businessId: useBusinessId()
  })
const userStore = useUserStore()
const timeFormat = (row: any) => {
  const res = dayjs.unix(row.date).format('YYYY-MM-DD')
  return res
}
const stopTimeFormat = (row: any) => {
  const time = row.stopTime
  if (!time) {
    return '-'
  }
  const res = dayjs.unix(time).format('YYYY-MM-DD')
  return res
}
const stopSk = (id: number) => {
  Action.skControl([id], Action.STATUS.STOP)
  queryList()
}
const reuse = (id: number) => {
  Action.skControl([id], Action.STATUS.REUSE)
  queryList()
}
const state: any = reactive({
  query: {
    sk: '',
    status: 0
  },
  list: [],
  pageInfo: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  dialog: false,
  detail: {},
  selection: [],
  show: false,
  options: [
    {
      name: '全部',
      id: 0
    },
    {
      name: '使用中',
      id: 2
    },
    {
      name: '未启用',
      id: 1
    },
    {
      name: '已删除',
      id: 3
    },
    {
      name: '初始化',
      id: 4
    },
    {
      name: '初始化失败',
      id: 5
    }
  ]
})
const costFormatter = (row: any) => {
  return row.cost / 100
}
const statusFormatter = (row: any) => {
  const statusMap: any = {
    1: '未启用',
    2: '使用中',
    3: '已删除',
    4: '初始化',
    5: '初始化失败'
  }
  return statusMap[row.status]
}
const queryList = () => {
  state.pageInfo.pageNum = 1
  getList()
}
const getList = async () => {
  const params = {
    ...state.query,
    ...state.pageInfo,
    businessId: userStore.current.businessId,
    withUsage: true,
    withSecret: true
  }
  const { list = [], total } = await $http.getSKList(params)
  state.list = list
  state.pageInfo.total = +total
}
queryList()
</script>
<style scoped lang="less">
.table-container {
  border-radius: 4px;
  background: white;
  padding: 12px;
}
</style>
