<template>
  <el-dialog
    v-model="state.show"
    title="操作记录"
    width="950px"
    @close="close()"
    :close-on-click-modal="false">
    <el-table :data="state.list" style="width: 100%" :show-overflow-tooltip="true">
      <el-table-column prop="businessName" label="所属业务线" />
      <el-table-column prop="accountTypeName" label="账号类型" width="140" />
      <el-table-column prop="statusName" width="140" label="账号状态" />
      <el-table-column prop="opUname" label="操作人员" width="120" />
      <el-table-column prop="createTime" label="操作时间" width="175" />
      <el-table-column prop="typeName" label="操作类型" />
      <el-table-column prop="opContent" label="操作内容" width="180" class-name="operation-cell" />
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="close">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
const props = defineProps({
  list: {
    type: Array,
    default: () => []
  }
})
const state: any = reactive({
  show: true,
  list: props.list
})
const emits = defineEmits(['close'])
const close = () => {
  state.show = false
  emits('close')
}
</script>
<style scoped lang="less">
:deep(.operation-cell) {
  .cell {
    white-space: normal;
  }
}
</style>
