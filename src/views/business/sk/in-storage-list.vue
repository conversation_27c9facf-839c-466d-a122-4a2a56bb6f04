<template>
  <SearchContainer class="search-form">
    <el-form inline>
      <el-form-item class="!w-[240px]" label="sk">
        <el-input v-model.trim="form.sk" placeholder="sk" clearable> </el-input>
      </el-form-item>
      <el-form-item class="!w-[240px]" label="邮箱">
        <el-input v-model.trim="form.email" placeholder="email" clearable> </el-input>
      </el-form-item>
      <el-form-item class="!w-[240px]" label="状态">
        <el-select v-model="form.status" clearable>
          <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="入库日期" label-width="70px" class="date-container">
        <el-date-picker
          v-model="date"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="截止日期"
          value-format="X"
          clearable />
      </el-form-item>
      <el-form-item label="开始日期" label-width="70px" class="date-container">
        <el-date-picker
          v-model="time"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="截止日期"
          value-format="X"
          clearable />
      </el-form-item>
      <el-form-item label="删除日期" label-width="70px" class="date-container">
        <el-date-picker
          v-model="delDate"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="截止日期"
          value-format="X"
          clearable />
      </el-form-item>
      <el-form-item class="!w-[240px]" label="类型">
        <el-select filterable v-model="form.accountType" clearable>
          <el-option
            v-for="item in commonStore?.config.accountList"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item class="!w-[240px]" label="业务线">
        <el-select filterable v-model="form.businessId" clearable>
          <el-option
            v-for="item in businessList"
            :key="item.id"
            :label="handleBusinessLabel(item.name, item.code)"
            :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item class="!w-[240px]" label="模型">
        <el-tree-select
          clearable
          filterable
          :props="{
            label: 'name',
            value: 'name',
            children: 'modelList',
            disabled: 'disabled'
          }"
          v-model="form.model"
          :data="companyList"
          :render-after-expand="false" />
      </el-form-item>
      <el-form-item class="!w-[240px]" label="渠道">
        <el-select filterable v-model="form.channel" clearable>
          <el-option
            v-for="item in commonStore.config.channelList"
            :key="item.name"
            :label="item.name"
            :value="item.name" />
        </el-select>
      </el-form-item>
      <el-button type="primary" @click="refetchData">搜索</el-button>
      <el-button type="primary" @click="exportResult">导出</el-button>
    </el-form>
    <section class="right-panel">
      <el-space wrap alignment="flex-start" :style="{ justifyContent: 'flex-end' }">
        <el-upload
          action="/openmis/business/skimport"
          :limit="100"
          :on-success="handleSuccess"
          accept=".xlsx">
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button type="success" @click="downLoad()">下载模板</el-button>
        <el-button type="primary" @click="assignmentBatch()" :disabled="!selection.length"
          >批量赋值</el-button
        >
        <el-button type="primary" @click="batchMove()" :disabled="!selection.length"
          >批量操作</el-button
        >
        <el-button type="danger" @click="deleteBatch()" :disabled="!selection.length"
          >批量删除</el-button
        >
      </el-space>
    </section>
  </SearchContainer>
  <section class="table-container">
    <el-table
      :data="listParams.list"
      style="width: 100%"
      :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" :selectable="selectable" />
      <el-table-column prop="date" width="110" label="入库日期" :formatter="timeFormat" />
      <!-- <el-table-column prop="businessName" label="业务线" /> -->
      <el-table-column prop="pool" label="资源池" width="170" />
      <el-table-column prop="account" label="账户类型" width="100">
        <template #default="scope">
          <el-tag disable-transitions v-if="scope.row.emptionType === 2" type="warning">月</el-tag>
          <span>{{ scope.row.account }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="accountNumber"
        width="200"
        label="账号/邮箱"
        :show-overflow-tooltip="false">
        <template #default="scope">
          <div class="accountNumber">
            <el-tooltip effect="dark" :content="scope.row.accountNumber" placement="top">
              <span class="tooltip">{{ scope.row.accountNumber }}</span>
            </el-tooltip>
            <el-button-group>
              <el-button
                @click="() => copyText(scope.row.accountNumber)"
                v-if="scope.row.accountNumber"
                class="icon-button"
                link
                icon="DocumentCopy"></el-button>
              <el-button
                @click="() => openApp(scope.row.accountNumber, scope.row.openAiPassword)"
                v-if="scope.row.accountNumber"
                class="icon-button"
                link
                icon="link"></el-button>
            </el-button-group>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="openAiPassword" label="账号密码" :show-overflow-tooltip="false">
        <template #default="scope">
          <div class="accountNumber">
            <el-button
              @click="() => copyText(scope.row.openAiPassword)"
              v-if="scope.row.openAiPassword"
              link
              class="button"
              icon="DocumentCopy"
              >复制</el-button
            >
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="emailPassword" label="邮箱密码" />
      <el-table-column prop="sk" label="sk" width="120" />
      <el-table-column prop="status" label="状态" :formatter="statusFormatter" />
      <el-table-column prop="delCode" label="删除代码" />
      <el-table-column prop="startTime" label="开始时间" width="160" :formatter="startTimeFormat" />
      <el-table-column prop="stopTime" label="删除时间" width="210" :formatter="stopTimeFormat" />
      <el-table-column prop="organization" label="组织" />
      <el-table-column prop="balance" label="余额" />
      <el-table-column prop="tpm" label="tpm" />
      <el-table-column prop="rpm" label="rpm" />
      <el-table-column prop="location" label="location" min-width="130" />
      <el-table-column prop="channel" label="渠道" />
      <el-table-column prop="price" label="单价" />
      <el-table-column fixed="right" label="操作" width="200" :show-overflow-tooltip="false">
        <template #default="scope">
          <el-button-group>
            <el-button link type="primary" size="small" @click="edit(scope.row)">编辑</el-button>
            <DelButton
              v-if="scope.row.status !== 3"
              size="small"
              @click="deleteBatch([scope.row.id])"></DelButton>
            <el-button
              v-if="scope.row.accountType !== 12"
              link
              type="primary"
              size="small"
              @click="Action.skTest(scope.row)"
              >SK测试</el-button
            >
            <el-button
              v-if="scope.row.status === 1 && scope.row.accountType === 12"
              link
              type="primary"
              size="small"
              @click="startBatch([scope.row.id])"
              >启用</el-button
            >
            <el-button
              :loading="scope.row.recoveryLoading"
              v-if="scope.row.status === 3"
              link
              type="primary"
              size="small"
              @click="recoverySk(scope.row)"
              >恢复</el-button
            >
            <el-button link type="primary" size="small" @click="getOpList(scope.row.id)"
              >操作记录</el-button
            >
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-model:pageNum="listParams.pageInfo.pageNum"
      v-model:page-size="listParams.pageInfo.pageSize"
      :total="listParams.pageInfo.total"
      @refresh="getPurchasingListMutate">
    </Pagination>
  </section>
  <Edit v-if="state.detail" @close="closeDialog" :value="state.detail"></Edit>
  <el-dialog v-model="batchMoveState.show" title="批量操作" width="420px">
    <el-form
      ref="batchMoveRef"
      label-width="100px"
      :rules="batchMoveRules"
      :model="batchMoveState.data">
      <el-form-item label="选择业务线">
        <el-select
          filterable
          v-model="batchMoveState.data.businessId"
          placeholder="Select"
          size="large">
          <el-option :key="-1" label="当前业务线" :value="-1" />
          <el-option
            v-for="item in batchMoveBusinessId"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="选择状态">
        <el-select v-model="batchMoveState.data.status" placeholder="Select" size="large">
          <el-option :key="-1" label="当前状态" :value="-1" />
          <el-option
            v-for="item in batchMoveStatusList"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="选择模型">
        <el-tree-select
          :disabled="modelDisabled"
          clearable
          filterable
          :props="{ label: 'name', value: 'name', children: 'modelList', disabled: 'disabled' }"
          v-model="batchMoveState.data.model"
          :data="companyList"
          :render-after-expand="false" />
      </el-form-item>
      <el-form-item label="操作类型">
        <el-radio-group v-model="batchMoveState.data.operate">
          <el-radio v-for="item in operateList" :key="item.value" :value="item.value">{{
            item.name
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="batchMoveState.data.operate === 2">
        <el-form-item label="RPM" prop="rpm">
          <el-input-number v-model="batchMoveState.data.rpm" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="TPM" prop="tpm">
          <el-input-number v-model="batchMoveState.data.tpm" :min="0"></el-input-number>
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="batchMoveState.show = false">取消</el-button>
        <el-button type="primary" @click="batchMoveSuccess"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog v-model="assignmentState.show" title="批量赋值" width="420px">
    <el-form
      :model="assignmentState.data"
      ref="batchAssignRef"
      :rules="assignmentRules"
      label-width="100px">
      <el-form-item label="RPM" prop="rpm">
        <el-input-number v-model="assignmentState.data.rpm" :min="0"></el-input-number>
      </el-form-item>
      <el-form-item label="TPM" prop="tpm">
        <el-input-number v-model="assignmentState.data.tpm" :min="0"></el-input-number>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="assignmentState.show = false">取消</el-button>
        <el-button type="primary" @click="batchAssignConfirm"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
  <Operations
    v-if="state.skOperationList.length"
    :list="state.skOperationList"
    @close="state.skOperationList = []"></Operations>
</template>

<script lang="ts" setup>
import { v4 as uuidv4 } from 'uuid'
import DelButton from '@/components/del-button'
import customProtocolCheck from 'custom-protocol-check'
import Operations from './operations.vue'
import { afterDecorator, fetchFile, copyText as utilsCopyText } from '@znzt-fe/utils'
import { Pagination } from '@znzt-fe/components'
import { ElMessage, ElMessageBox } from 'element-plus'
import Edit from './edit.vue'
import useCommonStore from '@/store/common'
import SearchContainer from '@/components/search-container'
import Action from './action'
import { adjust, getPurchasingList, useRecoverySk } from '@/api/sk'
import {
  OpListItem,
  PurchasingListItem,
  PurchasingListParams,
  PurchasingStatus,
  Result
} from '@/api/sk/type'
import { useOpenUrl } from '@/hooks/useBusinessCode'
import { useGetSkFileDemo, useBatchSetRpmTpm } from '@/api/business'
import { useDateRange, useElForm, useList, useRule } from '@znzt-fe/hooks'
import { Options, UniversalListParams } from '@znzt-fe/declare'
import { ResponseData } from '@/plugin/axios/interceptors'
const operateList = [
  {
    name: '移动',
    value: 1
  },
  {
    name: '复制',
    value: 2
  }
]
const assignmentRules = useRule({
  tpm: {
    message: 'tpm不能为空',
    validator: () => !!assignmentState.data.tpm
  },
  rpm: {
    message: 'rpm不能为空',
    validator: () => !!assignmentState.data.rpm
  }
})
const batchMoveRules = useRule({
  tpm: {
    message: 'tpm不能为空',
    validator: () => !!batchMoveState.data.tpm
  },
  rpm: {
    message: 'rpm不能为空',
    validator: () => !!batchMoveState.data.rpm
  }
})
const batchAssignRef = ref()
const batchMoveRef = ref()
const commonStore = useCommonStore()
const initData: PurchasingListParams = {
  sk: '',
  status: undefined,
  startDate: undefined,
  endDate: undefined,
  startTime: undefined,
  endTime: undefined,
  stopStartTime: undefined,
  stopEndTime: undefined,
  model: '',
  email: '',
  channel: '',
  businessId: undefined,
  accountType: undefined
}
const handleBusinessLabel = (name: string, code: string) => {
  if (!code) return name
  return `${name}(${code})`
}
const { form } = useElForm(initData)
const date = useDateRange(form, ['startDate', 'endDate'])
const delDate = useDateRange(form, ['stopStartTime', 'stopEndTime'])
const time = useDateRange(form, ['startTime', 'endTime'])
const state = reactive<{ detail: PurchasingListItem | null; skOperationList: OpListItem[] }>({
  detail: null,
  skOperationList: []
})

const selection = ref<number[]>([])
const companyList = computed(() => {
  const res = commonStore.config.companyList.map((item) => ({
    ...item,
    value: item.id + uuidv4(),
    disabled: !item.modelList?.length,
    modelList: item.modelList.map(({ modelList = [], ...items }: any) => ({
      ...items,
      value: item.id + uuidv4(),
      disabled: !modelList?.length,
      modelList: modelList.map((sub: any) => ({
        ...sub,
        value: sub.id
      }))
    }))
  }))
  return res
})
const { data } = useGetSkFileDemo()
const downLoad = () => {
  if (data.value?.url) {
    fetchFile(data.value?.url)
  }
}
const openApp = (username: string, password: string) => {
  try {
    customProtocolCheck(
      `cherish://${username}/${password}`,
      () => {
        ElMessage.warning('此功能需要先下载对应app')
      },
      () => {},
      1000,
      () => {}
    )
  } catch (err) {
    console.log(err)
  }
}
const businessList = computed(() => {
  const first = {
    id: 0,
    name: '公共池',
    code: ''
  }
  return [first, ...commonStore.config.businessList]
})
const options: Options[] = [
  {
    name: '全部',
    id: 0
  },
  {
    name: '使用中',
    id: 2
  },
  {
    name: '未启用',
    id: 1
  },
  {
    name: '已删除',
    id: 3
  },
  {
    name: '初始化',
    id: 4
  },
  {
    name: '初始化失败',
    id: 5
  }
]
const handleSuccess = (data: ResponseData) => {
  const { errNo, errMsg } = data
  if (errNo) {
    ElMessage.error(errMsg)
    return
  }
  ElMessage.success('导入成功！')
  getPurchasingListMutate()
}
const selectable = () => {
  return true
}
const timeFormat = (row: PurchasingListItem) => dayjs.unix(row.date).format('YYYY-MM-DD')
const statusFormatter = (row: PurchasingListItem) => {
  const statusMap = {
    [PurchasingStatus.NotEnabled]: '未启用',
    [PurchasingStatus.Enabled]: '使用中',
    [PurchasingStatus.Delete]: '已删除',
    [PurchasingStatus.Init]: '初始化',
    [PurchasingStatus.InitFail]: '初始化失败'
  }

  return statusMap[row.status]
}

const {
  listParams,
  mutate: getPurchasingListMutate,
  refetchData
} = useList<PurchasingListItem>({
  getList: (params: UniversalListParams) => getPurchasingList({ ...form, ...params })
})

const handleSelectionChange = (val: PurchasingListItem[]) =>
  (selection.value = val.map((item) => item.id))

const modelDisabled = computed(() =>
  listParams.list
    .filter((item) => selection.value.includes(item.id))
    .some((item) => item.accountType === 26)
)
const deleteBatch = async (ids = selection.value) => {
  const result = await ElMessageBox.confirm(`确认删除${ids.length}个账号吗?`, '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).catch(() => false)
  if (!result) return
  await Action.skControl(ids, Action.STATUS.DELETE)
  ElMessage.success('删除成功!')
  refetchData()
}

const startBatch = async (ids: number[]) => {
  await Action.skControl(ids, Action.STATUS.REUSE)
  ElMessage.success('启用成功!')
  refetchData()
}

const { mutateAsync: recoverySkAsync } = useRecoverySk({
  onSuccess(res) {
    switch (res.result) {
      case Result.Success:
        ElMessage.success('恢复成功!')
        refetchData()
        break
      case Result.Error:
      default:
        ElMessage.error('恢复失败!')
    }
  }
})
const recoverySk = async (row: PurchasingListItem) => {
  row.recoveryLoading = true
  try {
    await recoverySkAsync({ skType: row.skType, sk: row.sk, id: row.id })
  } finally {
    row.recoveryLoading = false
  }
}
const edit = (data: PurchasingListItem) => {
  state.detail = data
}
const closeDialog = (refresh = false) => {
  state.detail = null
  if (refresh) {
    refetchData()
  }
}
const assignmentBatch = () => {
  assignmentState.show = true
  assignmentState.data = {
    tpm: 0,
    rpm: 0
  }
}
const batchMove = () => {
  batchMoveState.show = true
  batchMoveState.data = {
    businessId: -1,
    status: 4,
    model: '',
    operate: 1,
    tpm: 0,
    rpm: 0
  }
}
const batchMoveSuccess = async () => {
  const validate = await batchMoveRef.value.validate().catch(() => false)
  if (!validate) return
  await adjust({
    ids: selection.value,
    ...batchMoveState.data
  })
  batchMoveState.show = false
  refetchData()
}
const { mutate } = useBatchSetRpmTpm({
  onSuccess: () => {
    assignmentState.show = false
    refetchData()
  }
})
const batchAssignConfirm = async () => {
  const validate = await batchAssignRef.value.validate().catch(() => false)
  if (!validate) return
  mutate({ arrId: selection.value, ...assignmentState.data })
}
const batchMoveStatusList: Options[] = [
  {
    name: '未启用',
    id: 1
  },
  {
    name: '使用中',
    id: 2
  },
  {
    name: '已删除',
    id: 3
  },
  {
    name: '初始化',
    id: 4
  }
]
const batchMoveBusinessId: ComputedRef<Array<{ id?: number; name: string }>> = computed(() => {
  const defaultItem = [
    {
      id: 0,
      name: '公共池'
    }
  ]
  const list = commonStore.config.businessList
    .filter((business) => business.status < 4)
    .map((item) => ({ ...item, name: `${item.name}(${item.code})` }))
  const res = [...defaultItem, ...list]
  return res
})
const batchMoveState = reactive({
  show: false,
  data: {
    businessId: 0,
    status: 1,
    model: 'gpt-3.5-turbo',
    operate: 1,
    tpm: 0,
    rpm: 0
  }
})
const assignmentState = reactive({
  show: false,
  data: {
    tpm: 0,
    rpm: 0
  }
})
const getOpList = async (id: number) => {
  const { list = [] } = await Action.getOpList(id)
  state.skOperationList = list
}
const startTimeFormat = (row: PurchasingListItem) => {
  const time = row.startTime
  if (!time) {
    return '-'
  }
  const res = dayjs.unix(time).format('YYYY-MM-DD HH:mm')
  return res
}
const stopTimeFormat = (row: PurchasingListItem) => {
  const time = row.stopTime
  if (!time) {
    return '-'
  }
  const res = dayjs.unix(time).format('YYYY-MM-DD HH:mm')
  let durationTime = ''
  if (row.status === PurchasingStatus.Delete && row.stopTime && row.startTime) {
    const duration = row.stopTime - row.startTime
    const day = duration / (60 * 60 * 24)
    const hour = duration / (60 * 60)
    if (day > 1) {
      durationTime = `(${day.toFixed(2)}D)`
    } else {
      durationTime = `(${hour.toFixed(2)}h)`
    }
  }
  return `${res}${durationTime}`
}

const copyText = afterDecorator(utilsCopyText, () => ElMessage.success('复制成功'))
const { openUrl } = useOpenUrl()
const exportResult = () =>
  openUrl('/openmis/sk/purchasinglistexport', {
    ...form,
    pageNum: listParams.pageInfo.pageNum,
    pageSize: listParams.pageInfo.pageSize
  })
</script>
<style scoped lang="less">
.search-form {
  background: white;
  padding: 16px;
  border-radius: 4px;
  min-height: 60px;
  box-sizing: border-box;
  margin-bottom: 12px;
  align-items: flex-start;

  .el-form-item {
    margin-bottom: 8px;
    align-items: center;

    :deep(.el-form-item__content) {
      min-width: 170px;
    }

    &.date-container {
      :deep(.el-form-item__content) {
        min-width: 180px;
      }
    }
  }

  .el-button {
    position: relative;
    top: -2px;
  }

  .right-panel {
    min-width: 280px;
    // display: flex;
    // justify-content: flex-end;
  }
}

.table-container {
  border-radius: 4px;
  background: white;
  padding: 12px;

  .el-tag {
    padding: 0 5px;
    margin-right: 3px;
  }

  .accountNumber {
    display: flex;
    overflow: hidden;
    white-space: nowrap;
  }

  .tooltip {
    text-overflow: ellipsis;
    flex: 1;
    overflow: hidden;
  }

  .icon-button {
    flex-shrink: 0;
  }
}

.upload-demo {
  display: inline-block;
  position: relative;
  top: 1px;
  left: -10px;

  :deep(.el-upload-list) {
    display: none;
  }
}
</style>
