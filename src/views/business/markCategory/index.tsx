import { ElButton, ElTable } from 'element-plus'
import { useTable } from './hooks'
import Modal from './modal'
import { useModal } from './hooks'
import SearchContainer from '@/components/search-container'
export default defineComponent({
  setup() {
    const { visible, modalId, openModal } = useModal()
    const { listParams, isLoading, tableColumn, refetchData } = useTable(openModal)
    return () => (
      <div>
        <SearchContainer>
          <div class="flex-just-end w-full">
            <ElButton onClick={() => openModal()} type="primary">
              新增
            </ElButton>
          </div>
        </SearchContainer>
        <ElTable data={listParams.list} style={{ width: '100%' }} v-loading={isLoading.value}>
          {tableColumn}
        </ElTable>
        <Modal id={modalId.value} onRefetch={refetchData} v-model={visible.value} />
      </div>
    )
  }
})
