import { useDelMarkCategory, getMarkCategoryList } from '@/api/markcategory'
import { MarkCategoryListItem } from '@/api/markcategory/type'
import DelButton from '@/components/del-button'
import { generateTableList } from '@znzt-fe/utils'
import { useElModal, useList } from '@znzt-fe/hooks'
import { ElButton, ElButtonGroup } from 'element-plus'

export const useCategoryList = () => {
  const { listParams, isLoading, refetchData } = useList<MarkCategoryListItem>({
    getList: getMarkCategoryList
  })
  return { listParams, isLoading, refetchData }
}
export const useTable = (openModal: (id: string) => void) => {
  const { listParams, isLoading, refetchData } = useCategoryList()
  const { mutate: delMarkCategory } = useDelMarkCategory({
    onSuccess: refetchData
  })
  const tableColumn = generateTableList<MarkCategoryListItem>([
    {
      prop: 'id',
      label: 'id'
    },
    {
      prop: 'name',
      label: '名称'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      slots: (scope) => dayjs(scope.row.createTime * 1000).format('YYYY-MM-DD')
    },
    {
      prop: 'operation',
      label: '操作',
      slots: (scope) => (
        <ElButtonGroup>
          <ElButton link onClick={() => openModal(scope.row.id)} type="primary">
            编辑
          </ElButton>
          <DelButton onClick={() => delMarkCategory({ id: scope.row.id })}>删除</DelButton>
        </ElButtonGroup>
      )
    }
  ])
  return {
    isLoading,
    tableColumn,
    listParams,
    refetchData
  }
}

export const useModal = useElModal
