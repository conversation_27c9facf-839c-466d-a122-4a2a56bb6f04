import { useElForm, useRule } from '@znzt-fe/hooks'
import {
  useCreateMarkCategory,
  useEditMarkCategory,
  useGetMarkCategoryDetail
} from '@/api/markcategory'
import { ElMessage } from 'element-plus'
import { afterDecorator, beforeNextTick } from '@znzt-fe/utils'
import { EditMarkCategoryParams } from '@/api/markcategory/type'
export const useForm = (
  isEdit: Ref<boolean>,
  refetch: () => void,
  visible: Ref<boolean>,
  id: Ref<string>
) => {
  const initData: EditMarkCategoryParams = {
    name: '',
    id: ''
  }
  const { formRef, validate, clearValidate, validateField, resetForm, form, event } =
    useElForm(initData)
  const rules = useRule({
    name: '标注类别名称不能为空'
  })
  const options = {
    onSuccess() {
      ElMessage.success('操作成功')
      refetch()
      visible.value = false
    }
  }
  const resetFormData = async () => {
    resetForm()
    clearValidate()
    form.id = id.value
  }
  const getDetail = () => {
    if (!form.id) return
    getMarkCategoryDetail({
      id: form.id
    })
  }

  whenever(visible, beforeNextTick(afterDecorator(resetFormData, getDetail)))

  const { mutate: getMarkCategoryDetail } = useGetMarkCategoryDetail({
    onSuccess(data) {
      form.name = data.name
    }
  })
  const { mutate: createMarkCategory, isLoading: createLoading } = useCreateMarkCategory(options)
  const { mutate: editMarkCategory, isLoading: editLoading } = useEditMarkCategory(options)
  const submit = async () => {
    const result = await validate()
    if (!result) return
    isEdit.value ? editMarkCategory(form) : createMarkCategory({ name: form.name })
  }
  const isLoading = computed(() => createLoading.value || editLoading.value)
  return {
    formRef,
    validate,
    clearValidate,
    validateField,
    resetFormData,
    form,
    rules,
    submit,
    event,
    isLoading
  }
}

export const useModal = (id: Ref<string>) => {
  const isEdit = computed(() => !!id.value)
  const title = computed(() => (!isEdit.value ? '新增标注类别' : '编辑标注类别'))
  return {
    title,
    isEdit
  }
}
