import { ElDialog, ElForm, ElFormItem, ElInput } from 'element-plus'
import { useForm, useModal } from './hooks'
import ModalBottom from '@/components/modal-bottom'
export default defineComponent({
  props: {
    modelValue: Boolean,
    id: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue', 'refetch'],
  setup(props, { emit }) {
    const { id } = toRefs(props)
    const visible = useModel(props, 'modelValue')
    const { title, isEdit } = useModal(id)
    const refetch = () => emit('refetch')
    const { formRef, form, rules, resetFormData, submit, event, isLoading } = useForm(
      isEdit,
      refetch,
      visible,
      id
    )
    return () => (
      <ElDialog title={title.value} v-model={visible.value}>
        <ElForm {...event} model={form} ref={formRef} rules={rules}>
          <ElFormItem label="名称" prop="name">
            <ElInput v-model={form.name} />
          </ElFormItem>
        </ElForm>
        <ModalBottom onConfirm={submit} onReset={resetFormData} v-loading={isLoading.value} />
      </ElDialog>
    )
  }
})
