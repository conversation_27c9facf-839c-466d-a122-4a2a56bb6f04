<template>
  <section class="search-form" :inline="true" :model="state.query">
    <el-button type="primary" @click="addItem">新建</el-button>
  </section>
  <section class="table-container">
    <el-table :data="state.list" style="width: 100%" :show-overflow-tooltip="true">
      <el-table-column prop="id" label="模型ID" />
      <el-table-column prop="name" label="模型名称" />
      <el-table-column prop="url" label="url" />
      <el-table-column prop="status" label="模型状态" :formatter="statusFormatter" />
      <el-table-column prop="desc" label="描述" />
      <el-table-column prop="modelCompanyName" label="模型场景" />
      <el-table-column fixed="right" label="操作" width="120">
        <template #default="scope">
          <el-button-group>
            <el-button link type="primary" size="small" @click="update(scope.row)">编辑</el-button>
            <el-button link type="primary" size="small" @click="testModel(scope.row)"
              >测试</el-button
            >
            <DelButton size="small" @click="delModel(scope.row)" />
            <DelButton
              size="small"
              v-if="scope.row.status !== 4"
              @click="forbiddenModel(scope.row)"
              confirmText="将批量禁用所有业务线对此模型的调用权限"
              >禁用
            </DelButton>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-model:pageNum="state.pageInfo.pageNum"
      v-model:page-size="state.pageInfo.pageSize"
      :total="state.pageInfo.total"
      @refresh="getList">
    </Pagination>
  </section>
  <el-dialog
    v-if="currentRow"
    :model-value="true"
    :title="currentRow.id ? '编辑模型' : '新建模型'"
    width="550px"
    @close="close()"
    :close-on-click-modal="false">
    <el-form :model="currentRow" ref="formRef" label-width="130px">
      <el-form-item label="模型名称" prop="name" required>
        <el-input v-model="currentRow.name" :disabled="!!currentRow.id"> </el-input>
      </el-form-item>
      <el-form-item label="请求url" prop="url" required>
        <el-input type="textarea" :rows="3" v-model="currentRow.url"></el-input>
      </el-form-item>
      <el-form-item label="max tokens" prop="maxTokens" required>
        <el-input-number
          v-model="currentRow.maxTokens"
          :min="1"
          :step="1"
          :precision="0"></el-input-number>
      </el-form-item>
      <el-form-item label="描述" prop="desc">
        <el-input type="textarea" :rows="3" v-model="currentRow.desc"></el-input>
      </el-form-item>
      <el-form-item label="是否支持深度思考" prop="supportThinking">
        <el-switch v-model="currentRow.supportThinking" />
      </el-form-item>
      <el-form-item label="adapter_name" prop="adapter_name">
        <el-switch v-model="currentRow.adapterName" />
      </el-form-item>
      <el-form-item label="模型场景" prop="modelCompanyId" required>
        <el-select v-model="currentRow.modelCompanyId" :disabled="!!currentRow.id">
          <el-option
            v-for="item in companyList"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="支持消息格式" prop="supportMsgType">
        <el-select v-model="currentRow.supportMsgType" multiple>
          <el-option
            v-for="item in modelOptions.supportMsgTypeList"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close()">取消</el-button>
        <el-button type="primary" @click="updateModel">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import DelButton from '@/components/del-button'
import { Pagination } from '@znzt-fe/components'
import { useGetAdminModelOptions } from '@/api/adminModel'
import $http from '@/api'
interface Model {
  name: string
  id?: number
  url: string
  desc: string
}
import useCommonStore from '@/store/common'
import { ElMessage } from 'element-plus'
const commonStore = useCommonStore()
const companyList = computed(() => {
  const res = commonStore.config?.companyList || []
  return res
    .filter((item) => item.name.includes('自研'))
    .map((item) => ({
      name: item.name,
      id: item.id
    }))
})
interface Model1 extends Model {
  maxTokens: number
  adapterName: boolean
  supportThinking: boolean
  modelCompanyId: number | null
  supportMsgType: string[]
}
const { data: modelOptions }: any = useGetAdminModelOptions()
const state = reactive({
  query: {
    source: 2
  },
  list: [],
  pageInfo: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  }
})
const queryList = () => {
  state.pageInfo.pageNum = 1
  getList()
}
const getList = async () => {
  const params = {
    ...state.pageInfo,
    ...state.query
  }
  const { list = [], total } = await $http.getModelList(params)
  state.list = list
  state.pageInfo.total = +total
}
queryList()
const currentRow: Ref<null | Model1> = ref(null)
const addItem = async () => {
  currentRow.value = {
    name: '',
    url: '',
    desc: '',
    maxTokens: 1024,
    adapterName: false,
    modelCompanyId: null,
    supportMsgType: [],
    supportThinking: false
  }
}
const statusFormatter = (row: any) => {
  const statusMap: any = {
    1: '正常',
    2: '无效',
    4: '禁用'
  }
  return statusMap[row.status]
}
const testModel = async (row: any) => {
  const params = {
    name: row.name
  }
  await $http.testModel(params)
  ElMessage.success('测试成功')
  getList()
}
const delModel = async (row: any) => {
  const params = {
    id: row.id
  }
  await $http.removeModelById(params)
  ElMessage.success('删除成功')
  queryList()
}
const forbiddenModel = async (row: any) => {
  await $http.controlModelStatus({ id: row.id, op: 3 })
  ElMessage.success('禁用成功')
  getList()
}

const update = (row: any) => {
  currentRow.value = row
}
const formRef = ref()
const close = (refresh = false) => {
  currentRow.value = null
  if (refresh) {
    queryList()
  }
}
const updateModel = async () => {
  const form = formRef.value
  if (form) {
    const valid = await form.validate().catch(() => false)
    const data = currentRow.value
    if (valid && data) {
      const action = data.id ? $http.updateModel : $http.addModel
      await action({
        ...data,
        type: 2,
        source: 2,
        needPool: 2
      })
      close(true)
    }
  }
}
</script>
<style scoped lang="less">
.search-form {
  background: white;
  padding: 16px;
  border-radius: 4px;
  min-height: 60px;
  box-sizing: border-box;
  margin-bottom: 12px;
  text-align: right;
}

.table-container {
  border-radius: 4px;
  background: white;
  padding: 12px;
}
</style>
