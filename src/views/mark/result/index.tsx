import { ElButton, ElTable } from 'element-plus'
import { useTable, usePermissions } from './hook'
import Back from '../components/back'
import style from './style.module.less'
import { useRouteId } from '@znzt-fe/hooks'
import SearchContainer from '@/components/search-container'
import { useOpenUrl } from '@/hooks/useBusinessCode'
export default defineComponent({
  setup() {
    const id = useRouteId()

    const { tableColumn, markResultList } = useTable()

    const { permissions } = usePermissions(markResultList)
    const { openUrl } = useOpenUrl()
    const downLoadResult = () => openUrl('/openmis/marktask/resultexport', { markTaskId: id })
    const downLoadDetail = () => openUrl('/openmis/marktask/detailexport', { markTaskId: id })
    const downLoadJsonlDetail = () =>
      openUrl('/openmis/marktask/detailexport', { markTaskId: id, exportFileType: 'jsonl' })
    return () => (
      <>
        <SearchContainer>
          <Back />
          <div>
            {markResultList.value?.name}{' '}
            {markResultList.value?.createTime &&
              dayjs.unix(markResultList.value?.createTime).format('YYYY-MM-DD')}
          </div>
          <span />
        </SearchContainer>
        <ElTable
          data={markResultList.value?.list}
          showOverflowTooltip
          style={{ width: '100%', marginTop: '12px' }}>
          {tableColumn.value}
        </ElTable>
        <div class={style.buttonGroup}>
          {permissions.value.HASDOWNLOADRESULT && (
            <ElButton link onClick={downLoadResult} type="primary">
              导出结果
            </ElButton>
          )}
          {permissions.value.HASDOWNLOADDETAIL && (
            <ElButton link onClick={downLoadDetail} type="primary">
              导出明细
            </ElButton>
          )}
          {permissions.value.HASDOWNLOADDETAIL && (
            <ElButton link onClick={downLoadJsonlDetail} type="primary">
              导出jsonl
            </ElButton>
          )}
        </div>
      </>
    )
  }
})
