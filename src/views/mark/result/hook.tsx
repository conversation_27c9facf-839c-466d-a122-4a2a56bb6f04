import { MarkResultListItem, MarkResultListRet } from '@/api/marktask/type'
import { generateTableList, getUserPermission } from '@znzt-fe/utils'
import { useGetMarkResultList } from '@/api/marktask'
import { finishRateTransform } from '../util'
import { usePermission, useRouteId } from '@znzt-fe/hooks'
export const useTable = () => {
  const { mutate: getMarkResultList, data: markResultList } = useGetMarkResultList()
  onMounted(() => {
    getMarkResultList({ markTaskId })
  })
  const markTaskId = useRouteId()
  const tableColumn = computed(() => {
    if (!markResultList.value?.models) return []
    const customColumn: Parameters<typeof generateTableList<MarkResultListItem>>[0] =
      markResultList.value.models.map((item) => ({
        prop: item,
        label: item,
        slots: (scope) =>
          scope.row.modelAvgMap[scope.column.property as keyof typeof markResultList.value.models]
      }))
    const tableColumn = generateTableList<MarkResultListItem>([
      {
        prop: 'ownerUname',
        label: '参与测试的用户'
      },
      {
        prop: 'finishRate',
        label: '完成度',
        slots: (scope) => finishRateTransform(scope.row.finishRate)
      },
      ...customColumn
    ])
    return tableColumn
  })
  return {
    tableColumn,
    markResultList
  }
}
export const usePermissions = (markResultList: Ref<undefined> | Ref<MarkResultListRet>) => {
  const { permissionObj, hasPermission } = usePermission(['DOWNLOADRESULT', 'DOWNLOADDETAIL'])
  const userPermissions = getUserPermission()
  const permissions = computed(() => {
    userPermissions.clear()
    const { DOWNLOADRESULT, DOWNLOADDETAIL } = permissionObj
    if (markResultList.value?.isOwner) {
      userPermissions.set(DOWNLOADRESULT | DOWNLOADDETAIL)
    }
    return hasPermission<'HASDOWNLOADRESULT' | 'HASDOWNLOADDETAIL'>(userPermissions.get())
  })
  return { permissions }
}
