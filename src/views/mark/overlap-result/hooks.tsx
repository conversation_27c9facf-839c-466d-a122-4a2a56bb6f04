import { useGetMarktaskOverlap } from '@/api/marktask'
import { afterDecorator, generateTableList } from '@znzt-fe/utils'
import * as echarts from 'echarts/core'
import { PieChart } from 'echarts/charts'
import { useRouteId } from '@znzt-fe/hooks'
import { MarktaskOverlapRet, MarktaskOverlapRetListItem } from '@/api/marktask/type'
import { useOpenUrl } from '@/hooks/useBusinessCode'
import { TooltipComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
echarts.use([TooltipComponent, PieChart, CanvasRenderer])
export const useTable = () => {
  const {
    mutate: getMarktaskOverlapMutate,
    data: marktaskOverlapData,
    isLoading: marktaskOverlapLoading
  } = useGetMarktaskOverlap()
  const markTaskId = useRouteId()
  const getMarktaskOverlap = () => {
    getMarktaskOverlapMutate({
      markTaskId,
      option: +option.value
    })
  }
  const tableColumn = computed(() => {
    if (!marktaskOverlapData.value) return []
    return generateTableList<MarktaskOverlapRetListItem>(
      marktaskOverlapData.value.headers.map((item) => ({
        prop: item.id + '',
        label: item.name
      }))
    )
  })

  const option = ref('1')
  const changeMarktaskOverlapOption = afterDecorator(
    (value: string) => (option.value = value),
    getMarktaskOverlap
  )
  onMounted(getMarktaskOverlap)
  return {
    tableColumn,
    changeMarktaskOverlapOption,
    marktaskOverlapData,
    marktaskOverlapLoading,
    option
  }
}

export const useChart = (marktaskOverlapData: Ref<MarktaskOverlapRet | undefined>) => {
  const chart = ref<HTMLElement>()
  const myChart = ref<echarts.ECharts>()
  onMounted(() => (myChart.value = echarts.init(chart.value!)))
  const chartSetOption = (inconsistentCount: number, consistentCount: number) => {
    const option = {
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '交叉数据',
          type: 'pie',
          data: [
            { value: consistentCount, name: '一致数' },
            { value: inconsistentCount, name: '非一致数' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    myChart.value!.setOption(option)
  }
  whenever(marktaskOverlapData, (data) => {
    const inconsistentCount = data.cnt - data.same
    const consistentCount = data.same
    chartSetOption(inconsistentCount, consistentCount)
  })
  return { chart }
}

export const useDownLoad = () => {
  const { openUrl } = useOpenUrl()
  const id = useRouteId()
  const downLoadAll = () => {
    openUrl('/openmis/marktask/overlapexport', {
      markTaskId: id
    })
  }
  const downLoadFilter = (option: string) => {
    openUrl('/openmis/marktask/overlapexport', {
      markTaskId: id,
      option
    })
  }
  return {
    downLoadAll,
    downLoadFilter
  }
}
