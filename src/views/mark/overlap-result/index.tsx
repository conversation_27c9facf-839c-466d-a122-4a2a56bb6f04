import {
  ElSelect,
  ElO<PERSON>,
  ElTable,
  ElCard,
  ElDescriptions,
  ElDescriptionsItem,
  ElForm,
  ElFormItem,
  ElButton
} from 'element-plus'
import { markOverlapOption } from '../util'
import { useChart, useDownLoad, useTable } from './hooks'
import style from './index.module.less'
import SearchContainer from '@/components/search-container'
export default defineComponent({
  setup() {
    const {
      marktaskOverlapLoading,
      marktaskOverlapData,
      option,
      changeMarktaskOverlapOption,
      tableColumn
    } = useTable()
    const { chart } = useChart(marktaskOverlapData)
    const { downLoadAll, downLoadFilter } = useDownLoad()
    return () => (
      <div class={style.container}>
        <div class={style['left-content']}>
          <div ref={chart} style={{ height: '300px', width: '400px' }} />
          <ElCard v-loading={marktaskOverlapLoading.value}>
            <ElDescriptions column={1} title="摘要">
              <ElDescriptionsItem label="Overlap总条数">
                {marktaskOverlapData.value?.cnt}
              </ElDescriptionsItem>
              <ElDescriptionsItem label="一致率">
                {marktaskOverlapData.value?.sameRate}%
              </ElDescriptionsItem>
              <ElDescriptionsItem label="一致数">
                {marktaskOverlapData.value?.same}
              </ElDescriptionsItem>
            </ElDescriptions>
          </ElCard>
        </div>
        <div class={style['right-content']}>
          <SearchContainer>
            <ElForm inline>
              <ElFormItem class="!w-[240px]" label="筛选">
                <ElSelect modelValue={option.value} onChange={changeMarktaskOverlapOption}>
                  {markOverlapOption.map((item) => (
                    <ElOption key={item.key} label={item.label} value={item.key} />
                  ))}
                </ElSelect>
              </ElFormItem>
            </ElForm>
          </SearchContainer>
          <ElTable
            data={marktaskOverlapData.value?.list || []}
            style={{ width: '100%', marginBottom: '10px' }}
            v-loading={marktaskOverlapLoading.value}>
            {tableColumn.value}
          </ElTable>
          <div class="flex-just-end">
            <ElButton link onClick={() => downLoadFilter(option.value)} type="primary">
              导出筛选结果
            </ElButton>
            <ElButton link onClick={downLoadAll} type="primary">
              导出全部结果
            </ElButton>
          </div>
        </div>
      </div>
    )
  }
})
