<template>
  <section class="content-container">
    <p class="form-title">命中率与准确率</p>
    <el-form :model="state.filter" label-width="70px" inline>
      <el-form-item label="" v-if="props.mode === 1">
        <el-radio-group v-model="state.chartType" size="small" @change="chartTypeChange">
          <el-radio-button v-for="item in options" :key="item.value" :value="item.value">{{
            item.label
          }}</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="命中率" prop="dataSets">
        <div class="flex">
          <el-input-number
            class="!w-[150px]"
            v-model="state.filter.startHitRate"
            :max="state.filter.endHitRate || 100"
            :controls="false"
            :min="0" />
          <span>-</span>
          <el-input-number
            class="!w-[150px]"
            v-model="state.filter.endHitRate"
            :min="state.filter.startHitRate || 0"
            :controls="false"
            :max="100" />
        </div>
      </el-form-item>
      <el-form-item label="准确率" prop="dataSets">
        <div class="flex">
          <el-input-number
            class="!w-[150px]"
            v-model="state.filter.startCorrectRate"
            :max="state.filter.endCorrectRate || 100"
            :controls="false"
            :min="0" />
          <span>-</span>
          <el-input-number
            class="!w-[150px]"
            v-model="state.filter.endCorrectRate"
            :min="state.filter.startCorrectRate || 0"
            :controls="false"
            :max="100" />
        </div>
      </el-form-item>
    </el-form>
    <section class="chart-container">
      <div v-show="+props.mode === 1" ref="chart" class="chart"></div>
    </section>
    <div v-show="+props.mode === 2">
      <TaskTable :list="state.list" :ids="props.ids" :type="type"></TaskTable>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { useGetMarktaskChart } from '@/api/marktask'
import { setBarChartOptions, setRadarChartOptions } from '../bar'
import TaskTable from './task-table.vue'
import { ChartType, options } from './type'
const type = 2
const props: any = defineProps({
  ids: Array,
  mode: Number,
  filterOptions: {
    type: Object,
    default: () => ({})
  }
})

const chart = ref()
const state: any = reactive({
  chartType: ChartType.Bar,
  instance: null,
  list: [],
  filter: {
    startCorrectRate: 0,
    endCorrectRate: 100,
    startHitRate: 0,
    endHitRate: 100
  },
  chartData: null
})
const { mutate: getChart } = useGetMarktaskChart({
  async onSuccess(data: any) {
    const { category = [], series = [], list = [] } = data
    state.list = list
    state.chartData = {
      category,
      series
    }
    setChartOptions()
  }
})
const setChartOptions = () => {
  const { instance, chartData } = state
  const { category = [], series = [] } = chartData
  if (!category.length || !series.length) {
    return
  }
  if (instance) {
    instance.dispose()
  }
  if (state.chartType === ChartType.Bar) {
    state.instance = setBarChartOptions(chartData, chart, {
      percentFormat: true,
      minWidth: 130
    })
  }
  if (state.chartType === ChartType.Radar) {
    state.instance = setRadarChartOptions(chartData, chart)
  }
}
const chartTypeChange = () => {
  setChartOptions()
}
const getChartData = () => {
  const data: any = {
    ...state.filter,
    ...props.filterOptions,
    chartType: 1
  }
  getChart({ markTaskIds: props.ids, ...data })
}
getChartData()
watch(
  () => [state.filter, props.filterOptions],
  () => {
    getChartData()
  },
  {
    deep: true
  }
)
</script>
<style scoped lang="less">
.content-container {
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 5px;
  margin-top: 8px;
  padding: 12px 16px;
  flex: 1;

  .form-title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .chart-container {
    width: 100%;
    overflow: auto;
    flex: 1;
    height: 100%;
    padding: 0px;
    box-sizing: border-box;

    .chart {
      height: 100%;
    }
  }

  .table-footer {
    text-align: right;
    margin-top: 16px;
  }
}
</style>
