<template>
  <section class="content-container">
    <div v-show="props.mode === 1" ref="chat" class="chat"></div>
    <TaskTable
      v-show="props.mode === 2"
      :list="state.list"
      :ids="props.ids"
      :type="type"></TaskTable>
  </section>
</template>

<script lang="ts" setup>
import { useGetMarktaskChart } from '@/api/marktask'
import { setBarChartOptions } from '../bar'
import TaskTable from './task-table.vue'
const type = 3
const props: any = defineProps({
  ids: Array,
  mode: Number
})
const chat = ref()
const state: any = reactive({
  mode: 1,
  list: [],
  filter: {
    startCorrectRate: 0,
    endCorrectRate: 100,
    startHitRate: 0,
    endHitRate: 100,
    mode: 2,
    models: [],
    dataSets: []
  },
  options: {}
})
const { mutate: getChart } = useGetMarktaskChart({
  async onSuccess(data: any) {
    setChart(data)
    state.list = data?.list || []
  }
})
const setChart = (data: any) => {
  if (chat.value) {
    setBarChartOptions(data, chat, {
      percentFormat: true,
      minWidth: 130
    })
    return
  }
  const timeout = 50
  setTimeout(() => {
    setChart(data)
  }, timeout)
}
getChart({ markTaskIds: props.ids, ...state.filter, chartType: 1 })
watch(
  () => state.filter,
  () => {
    getChart({ markTaskIds: props.ids, ...state.filter })
  },
  {
    deep: true
  }
)
</script>
<style scoped lang="less">
.content-container {
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 5px;
  margin-top: 8px;
  padding: 12px 16px;
  flex: 1;

  .chat {
    height: 100%;
  }

  .table-footer {
    text-align: right;
    margin-top: 16px;
  }
}
</style>
