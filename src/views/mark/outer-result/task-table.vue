<template>
  <section>
    <el-table :data="props.list">
      <template v-if="props.type === 2">
        <el-table-column prop="model" label="模型名称" />
        <el-table-column prop="dataSetName" label="数据集-分组名称" />
        <el-table-column prop="allNum" label="总数" />
        <el-table-column prop="hitNum" label="命中数" />
        <el-table-column prop="hitRate" label="命中率" />
        <el-table-column prop="correctNum" label="正确数" />
        <el-table-column prop="hitCorrectRate" label="命中准确率" />
        <el-table-column prop="correctRate" label="准确率" />
        <el-table-column prop="subCnt" label="科目数" />
      </template>
      <template v-if="props.type === 3">
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="cnt" label="数量" />
        <el-table-column prop="good" label="Good" />
        <el-table-column prop="same" label="Same" />
        <el-table-column prop="bad" label="Bad" />
      </template>
      <template v-if="props.type === 4">
        <el-table-column prop="model" label="模型" />
        <el-table-column prop="dataSetName" label="数据集" />
        <el-table-column prop="avgScore" label="平均分" />
        <el-table-column prop="avgHitScore" label="命中平均分" />
        <el-table-column prop="hitDeviation" label="命中标准差" />
      </template>
      <template v-if="props.type === 5">
        <el-table-column prop="model" label="模型" />
        <el-table-column prop="dataSetName" label="数据集" />
        <el-table-column prop="allNum" label="总数" />
        <el-table-column prop="hitNum" label="命中数" />
        <el-table-column prop="hitRate" label="命中率" />
      </template>
    </el-table>
    <section v-if="props.ids.length === 1" class="table-footer">
      <el-button link type="primary" @Click="downLoadResult"> 导出结果 </el-button>
      <el-button link type="primary" @click="downLoadDetail"> 导出明细 </el-button>
    </section>
  </section>
</template>

<script lang="ts" setup>
import { useOpenUrl } from '@/hooks/useBusinessCode'
const props: any = defineProps({
  list: {
    type: Array,
    default: () => []
  },
  ids: Array,
  type: Number
})
const { openUrl } = useOpenUrl()
const downLoadResult = () => openUrl('/openmis/marktask/resultexport', { markTaskId: props.ids[0] })
const downLoadDetail = () => openUrl('/openmis/marktask/detailexport', { markTaskId: props.ids[0] })
</script>
<style scoped lang="less">
.table-footer {
  text-align: right;
  margin-top: 16px;
}
</style>
