<template>
  <section class="content-container">
    <p class="form-title">评估结果统计</p>
    <el-form :model="state.filter" label-width="70px" inline>
      <el-form-item label="" v-if="props.mode === 1">
        <el-radio-group v-model="state.chartType" size="small">
          <el-radio-button v-for="item in options" :key="item.value" :value="item.value">{{
            item.label
          }}</el-radio-button>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <el-row :gutter="24">
      <el-col :span="12">
        <p class="form-title">基于其他模型评分</p>
        <ChartOrList
          v-if="state.averageData"
          :mode="props.mode"
          :value="state.averageData"
          :chartType="state.chartType"
          :ids="props.ids"
          :type="4"></ChartOrList>
      </el-col>
      <el-col :span="12">
        <p class="form-title">命中率</p>
        <ChartOrList
          v-if="state.rateHitData"
          :mode="props.mode"
          :value="state.rateHitData"
          :chartType="state.chartType"
          :ids="props.ids"
          :type="5"></ChartOrList>
      </el-col>
    </el-row>
  </section>
</template>

<script lang="ts" setup>
import { useGetMarktaskChart } from '@/api/marktask'
import { ChartType, options } from './type'
import ChartOrList from './chart-or-list.vue'
const props: any = defineProps({
  ids: Array,
  mode: Number,
  filterOptions: {
    type: Object,
    default: () => ({})
  }
})

const state: any = reactive({
  chartType: ChartType.Bar,
  list: [],
  filter: {
    startCorrectRate: 0,
    endCorrectRate: 100,
    startHitRate: 0,
    endHitRate: 100
  },
  rateHitData: null,
  averageData: null
})
const { mutate: getBarChart } = useGetMarktaskChart({
  async onSuccess(data: any) {
    const { category = [], series = [], list = [] } = data
    state.list = list
    state.rateHitData = {
      chartData: {
        category,
        series
      },
      list: list
    }
  }
})
const { mutate: getRadarChart } = useGetMarktaskChart({
  async onSuccess(data: any) {
    const { category = [], series = [], list = [] } = data
    state.list = list
    state.averageData = {
      chartData: {
        category,
        series
      },
      list: list
    }
  }
})

const getAllChartsData = () => {
  const data: any = {
    ...state.filter,
    ...props.filterOptions,
    markTaskIds: props.ids
  }
  getBarChart({ chartType: 1, ...data })
  getRadarChart({ chartType: 2, ...data })
}
getAllChartsData()
watch(
  () => [state.filter, props.filterOptions],
  () => {
    getAllChartsData()
  },
  {
    deep: true
  }
)
</script>
<style scoped lang="less">
.content-container {
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 5px;
  margin-top: 8px;
  padding: 12px 16px;
  flex: 1;

  .form-title {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .el-row {
    flex: 1;

    .el-col {
      display: flex;
      flex-direction: column;
      overflow: auto;
    }

    .form-title {
      font-size: 16px;
      position: relative;
      padding-left: 6px;

      &:after {
        content: ' ';
        display: inline-block;
        background-color: var(--el-color-primary);
        width: 3px;
        height: 14px;
        position: absolute;
        left: 0px;
        top: 5px;
      }
    }
  }

  .chart-container {
    width: 100%;
    overflow: auto;
    flex: 1;
    height: 100%;
    padding: 10px 30px;
    box-sizing: border-box;

    .chart {
      height: 100%;
    }
  }

  .table-footer {
    text-align: right;
    margin-top: 16px;
  }
}
</style>
