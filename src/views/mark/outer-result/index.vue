<template>
  <ResultBack v-model="state.mode" />
  <el-main>
    <el-form :model="state.filter" label-width="70px" inline v-if="type !== 3">
      <el-form-item class="!w-[240px]" label="聚合" prop="mode">
        <el-select v-model="state.filter.mode">
          <el-option
            v-for="mode in state.options.modes"
            :key="mode.id"
            :label="mode.name"
            :value="mode.id" />
        </el-select>
      </el-form-item>
      <el-form-item class="!w-[240px]" label="模型" prop="models">
        <el-select filterable v-model="state.filter.models" multiple>
          <el-option
            v-for="model in state.options.models"
            :key="model"
            :label="model"
            :value="model" />
        </el-select>
      </el-form-item>
      <el-form-item class="!w-[240px]" label="数据集" prop="dataSets">
        <el-select filterable v-model="state.filter.dataSets" multiple>
          <el-option
            v-for="item in state.options.dataSets"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <CommonTaskChart
      v-if="type === 2"
      :ids
      :mode="state.mode"
      :filterOptions="state.filter"></CommonTaskChart>
    <DownStreamTaskChart v-if="type === 3" :ids="ids" :mode="state.mode"></DownStreamTaskChart>
    <!-- <SelfCreateTaskChart
      v-if="type === 4"
      :ids="ids"
      :mode="state.mode"
      :filterOptions="state.filter"></SelfCreateTaskChart> -->
  </el-main>
</template>

<script lang="ts" setup>
import ResultBack from '../components/result-back'
import { useGetChartsOptions } from '@/api/marktask'
import CommonTaskChart from './common-task-chart.vue'
import DownStreamTaskChart from './down-stream-task-chart.vue'
// import SelfCreateTaskChart from './self-create-task-chart.vue'
const route = useRoute()
const params = route.params
const type = +params.type
const ids = params.id ? [+params.id] : (route.query.ids as string[]).map((item) => +item)

const state: any = reactive({
  mode: 1,
  list: [],
  filter: {
    mode: 2,
    models: [],
    dataSets: []
  },
  options: {}
})
const { mutate: getOptions } = useGetChartsOptions({
  async onSuccess(data: any) {
    state.options = data || {}
  }
})
getOptions({ markTaskIds: ids })
</script>
<style scoped lang="less">
.page-header {
  display: flex;
  justify-content: space-between;

  .header-left {
    display: flex;
    justify-content: center;
    align-items: center;

    :deep(.el-button) {
      margin-right: 10px;
    }
  }
}

.el-main {
  padding: 0;
  display: flex;
  flex-direction: column;

  .el-form {
    background-color: white;
    padding-top: 18px;
    border-radius: 5px;

    span {
      margin: 0 8px;
    }
  }
}
</style>
