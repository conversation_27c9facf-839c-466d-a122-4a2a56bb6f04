<template>
  <section class="content-container">
    <section class="chart-container" v-show="mode === Mode.Chart">
      <div :style="{ height: '500px' }" ref="chart" class="chart"></div>
    </section>
    <div v-show="mode === Mode.Table">
      <TaskTable :list="value.list" :ids="ids" :type="type"></TaskTable>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { setBarChartOptions, setDoubleAxisChartOptions, setRadarChartOptions } from '../bar'
import TaskTable from './task-table.vue'
import { ChartType } from './type'
import { Mode } from '../self-result/hooks'

const props = defineProps({
  mode: Number as PropType<Mode>,
  ids: Array,
  type: Number,
  chartType: Number as PropType<ChartType>,
  value: {
    type: Object,
    default: () => {
      return {
        list: [],
        chartData: {}
      }
    }
  }
})
const chart = ref()
const state: any = {
  instance: null
}
const setChartOptions = () => {
  const { instance } = state
  const { chartType, value } = props
  const { chartData } = value
  const { category = [], series = [] } = chartData
  if (!category.length || !series.length) {
    return
  }
  if (instance) {
    instance.dispose()
  }
  if (chartType === ChartType.Bar) {
    state.instance = setBarChartOptions(chartData, chart, {
      common: false,
      minWidth: 80
    })
  }
  if (chartType === ChartType.Radar) {
    state.instance = setRadarChartOptions(chartData, chart, {
      max: props.type === 4 ? 10 : 100
    })
  }
  if (chartType === ChartType.Double) {
    state.instance = setDoubleAxisChartOptions(chartData, chart, {
      common: false,
      minWidth: 80
    })
  }
}
onMounted(() => {
  setChartOptions()
})
watch(
  () => [props.chartType, props.value],
  () => {
    // 两个图标请求在一个接口，选项仅更新雷达图，所以在watch阶段 chartType造成的重新发送请求不需要更新双轴图
    if (props.chartType === ChartType.Double) return
    setChartOptions()
  },
  {
    deep: true
  }
)
</script>
<style scoped lang="less">
.content-container {
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 5px;
  margin-top: 8px;
  padding: 0 16px 12px 16px;
  flex: 1;

  .form-title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .chart-container {
    width: 100%;
    // overflow: auto;
    flex: 1;
    height: 100%;
    padding: 0px;
    box-sizing: border-box;

    .chart {
      height: 100%;
    }
  }

  .table-footer {
    text-align: right;
    margin-top: 16px;
  }
}
</style>
