import { MarkTaskStatus } from '@/api/marktask/type'
import { ElTag } from 'element-plus'

/** 评分类型映射 */
export const markTaskScore = {
  '1': 4,
  '2': 3,
  '3': 5,
  '5': 0,
  '6': 0,
  '7': 0,
  '8': 0,
  '9': 0,
  '10': 0
}
export const markTaskScoreTypeToggle = (markTaskScoreType: ScoreType) =>
  markTaskScore[markTaskScoreType]

export const enum ScoreType {
  MarkScoreType4 = 1,
  MarkScoreType3,
  MarkScoreType5,
  MarkScoreTypeGSB = 5,
  MarkScoreTypeNil,
  MarkScoreTypeSort,
  MarkScoreTypeWritingEval,
  MarkScoreTypeAudioAndVideo,
  MarkSession
}
export const finishRateTransform = (finishRate: number) => finishRate / 100 + '%'

export const permissionList = [
  'MARK',
  'DELETE',
  'PUBLIC',
  'LOCK',
  'READ',
  'REFRESH',
  'RESTART',
  'VIEW',
  'OVERLAP',
  'EDIT',
  'ADDMARK'
] as const
export const statusTag = {
  [MarkTaskStatus.success]: (
    <ElTag disableTransitions type="success">
      成功
    </ElTag>
  ),
  [MarkTaskStatus.error]: (
    <ElTag disableTransitions type="danger">
      失败
    </ElTag>
  ),
  [MarkTaskStatus.execute]: <ElTag disableTransitions>执行中</ElTag>,
  [MarkTaskStatus.inMark]: <ElTag disableTransitions>待标注</ElTag>,
  // [MarkTaskStatus.WaitForMark]: <ElTag>待标注</ElTag>,
  [MarkTaskStatus.markDone]: (
    <ElTag disableTransitions type="success">
      标注完成
    </ElTag>
  )
}

export const markOverlapOption = [
  {
    key: '1',
    label: '不同项'
  },
  {
    key: '2',
    label: '相反项'
  }
]

export const gsbOption = [
  { label: 'good', value: 1 },
  { label: 'same', value: 2 },
  { label: 'bad', value: 3 }
]
