import Back from './back'
import { ElSwitch } from 'element-plus'
import style from './index.module.less'
export default defineComponent({
  props: {
    modelValue: Number
  },
  emits: ['update:modelValue'],
  setup(props) {
    const mode = useModel(props, 'modelValue')
    const route = useRoute()
    const { name = '' } = route.query
    return () => (
      <section class={style['page-header']}>
        <Back class={style['header-left']}>{name}</Back>
        <ElSwitch
          activeText="图模式"
          activeValue={1}
          inactiveText="表格模式"
          inactiveValue={2}
          size="large"
          v-model={mode.value}
        />
      </section>
    )
  }
})
