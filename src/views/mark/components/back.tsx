import { ElButton } from 'element-plus'
import { ArrowLeftBold } from '@element-plus/icons-vue'
export default defineComponent({
  props: {
    backText: {
      type: String,
      default: '返回'
    }
  },
  setup(props, { slots }) {
    const router = useRouter()
    return () => (
      <div>
        <ElButton icon={<ArrowLeftBold />} link onClick={router.back} type="primary">
          {props.backText}
        </ElButton>
        <span class="ml-[4px]">{slots.default ? slots.default() : null}</span>
        <slot />
      </div>
    )
  }
})
