import { useGetTaskRole, useUpdateTaskRole } from '@/api/markrole'
import { RoleId } from '@/api/markrole/type'
import { ElMessage } from 'element-plus'

export const useRole = (
  visible: Ref<boolean>,
  id: Ref<number | undefined>,
  roleId: Ref<RoleId>,
  refetch?: () => void
) => {
  const { mutate, isLoading: getMarkRoleLoading } = useGetTaskRole({
    onSuccess: (data) => (unames.value = data.list.map((item) => item.uname))
  })
  const unames = ref<string[]>([])
  const { mutate: updateTaskRoleMutate, isLoading: updateTaskRoleLoading } = useUpdateTaskRole({
    onSuccess: () => {
      refetch?.()
      visible.value = false
      ElMessage.success('更新成功')
    }
  })
  const onConfirm = () =>
    updateTaskRoleMutate({
      markTaskId: id.value!,
      roleId: roleId.value,
      unames: unames.value
    })
  whenever(
    () => visible.value && id.value,
    () => mutate({ markTaskId: id.value!, roleId: roleId.value }),
    { immediate: true }
  )
  return {
    getMarkRoleLoading,
    unames,
    updateTaskRoleMutate,
    onConfirm,
    updateTaskRoleLoading
  }
}
