import { <PERSON>Button, ElDialog, ElForm<PERSON>tem, ElOption, ElSelect } from 'element-plus'
import { useOptions } from '@/views/mark/task/modal/hooks'
import { useRole } from './hooks'
import { RoleId } from '@/api/markrole/type'
export default defineComponent({
  props: {
    modelValue: Boolean,
    id: Number,
    refetch: {
      type: Function as PropType<() => void>
    },
    roleName: {
      type: String,
      default: ''
    },
    roleId: {
      type: Number as PropType<RoleId>,
      default: RoleId.Observer
    }
  },
  setup(props) {
    const visible = useModel(props, 'modelValue')
    const id = toRef(props, 'id')
    const roleId = toRef(props, 'roleId')
    const roleName = toRef(props, 'roleName')
    const { unames, updateTaskRoleLoading, getMarkRoleLoading, onConfirm } = useRole(
      visible,
      id,
      roleId,
      props.refetch
    )
    const { searchUserIsLoading, remoteMethod, unameList, optionsMap } = useOptions()
    return () => (
      <ElDialog title={`编辑${roleName.value}`} v-model={visible.value}>
        {{
          default: () => (
            <ElFormItem
              label={`${roleName.value}`}
              prop="qiList"
              v-loading={getMarkRoleLoading.value}>
              <ElSelect
                collapseTags
                collapseTagsTooltip
                filterable
                loading={searchUserIsLoading.value}
                multiple
                placeholder={`请选${roleName.value}`}
                remote
                remoteMethod={remoteMethod}
                remoteShowSuffix
                style="width: 200px"
                v-model={unames.value}
                v-stopComposeEnter>
                {unameList.value
                  ? unameList.value.map((item) => (
                      <ElOption key={item.uname} label={item.uname} value={item.uname} />
                    ))
                  : optionsMap.value?.unameList.map((item) => (
                      <ElOption key={item.id} label={item.id} value={item.id} />
                    ))}
              </ElSelect>
            </ElFormItem>
          ),
          footer: () => (
            <span class="dialog-footer">
              <ElButton onClick={() => (visible.value = false)}>取消</ElButton>
              <ElButton loading={updateTaskRoleLoading.value} onClick={onConfirm} type="primary">
                确定
              </ElButton>
            </span>
          )
        }}
      </ElDialog>
    )
  }
})
