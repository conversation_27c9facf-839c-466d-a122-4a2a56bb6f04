import { useAddAnnotator, useGetAnnotatorStatus } from '@/api/marktask'
import { AddAnnotatorParams, AddAnnotatorType, AnnotatorStatusItem } from '@/api/marktask/type'
import { useElForm, useRule } from '@znzt-fe/hooks'
import { beforeNextTick } from '@znzt-fe/utils'
import { ElMessage } from 'element-plus'
import { useOptions } from '../modal/hooks'

export const useOption = (visible: Ref<boolean>, id: Ref<number | undefined>) => {
  const getAnnotatorStatusMutate = () => {
    mutate({ taskId: id.value! })
  }
  whenever(() => visible.value && id.value, getAnnotatorStatusMutate)
  invoke(async () => {
    await until(visible).toBeTruthy()
    getAnnotatorStatusMutate()
  })

  const { mutate, isLoading: getAnnotatorStatusLoading } = useGetAnnotatorStatus({
    onSuccess: (data) => (annotatorStatusList.value = data.list)
  })
  const annotatorStatusList = ref<AnnotatorStatusItem[]>([])
  const {
    optionsMap: optionsMapOrigin,
    remoteMethod,
    unameList: unameListOrigin,
    searchUserIsLoading
  } = useOptions()
  const unameList = computed(() =>
    unameListOrigin.value?.filter(
      (item) => !annotatorStatusList.value.map((item) => item.uname).includes(item.uname)
    )
  )
  const optionsMapUnameList = computed(() =>
    optionsMapOrigin.value?.unameList.filter(
      (item) => !annotatorStatusList.value.map((item) => item.uname).includes(item.name)
    )
  )
  return {
    getAnnotatorStatusLoading,
    annotatorStatusList,
    unameList,
    searchUserIsLoading,
    optionsMapUnameList,
    remoteMethod
  }
}

export const useForm = (
  visible: Ref<boolean>,
  id: Ref<number | undefined>,
  refetch?: () => void
) => {
  const initData: AddAnnotatorParams = {
    newAnnotators: [],
    oldAnnotator: '',
    taskId: id.value!,
    type: AddAnnotatorType.Copy
  }
  const { formRef, validate, clearValidate, resetForm, form } = useElForm(initData)
  const rules = useRule({
    type: '操作类型不能为空',
    oldAnnotator: '已有标注员不能为空',
    newAnnotators: '新增标注员不能为空'
  })
  const resetFormData = async () => {
    resetForm()
    clearValidate()
  }
  whenever(visible, beforeNextTick(resetFormData))

  const { mutate: addAnnotatorMutate } = useAddAnnotator({
    onSuccess: () => {
      refetch?.()
      ElMessage.success('添加成功')
      visible.value = false
    }
  })
  const submit = async () => {
    const result = await validate()
    if (!result) return
    addAnnotatorMutate(form)
  }
  return { form, validate, clearValidate, rules, formRef, submit }
}
