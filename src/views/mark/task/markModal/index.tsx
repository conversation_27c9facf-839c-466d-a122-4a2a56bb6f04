import { ElButton, ElDialog, ElForm, ElFormItem, ElOption, ElSelect } from 'element-plus'
import { useOption, useForm } from './hooks'
import { AddAnnotatorType } from '@/api/marktask/type'
export default defineComponent({
  props: {
    modelValue: Boolean,
    id: Number,
    refetch: {
      type: Function as PropType<() => void>
    }
  },
  setup(props) {
    const visible = useModel(props, 'modelValue')
    const id = toRef(props, 'id')
    const {
      getAnnotatorStatusLoading,
      annotatorStatusList,
      searchUserIsLoading,
      remoteMethod,
      unameList,
      optionsMapUnameList
    } = useOption(visible, id)
    const { form, formRef, rules, submit } = useForm(visible, id, props.refetch)
    return () => (
      <ElDialog title="增加标注员" v-model={visible.value}>
        {{
          default: () => (
            <ElForm labelWidth="100px" model={form} ref={formRef} rules={rules}>
              <ElFormItem label="操作类型" prop="type">
                <ElSelect
                  onChange={() => {
                    form.oldAnnotator = ''
                  }}
                  v-model={form.type}>
                  <ElOption label={'复制'} value={AddAnnotatorType.Copy} />
                  <ElOption label={'均分'} value={AddAnnotatorType.Split} />
                </ElSelect>
              </ElFormItem>
              <ElFormItem
                label="已有标注员"
                prop="oldAnnotator"
                v-loading={getAnnotatorStatusLoading.value}>
                <ElSelect
                  filterable
                  onChange={(v: string) => (form.newAnnotators = [v])}
                  placeholder="请选择标注员"
                  v-model={form.oldAnnotator}>
                  {annotatorStatusList.value.map((item) => (
                    <ElOption
                      disabled={form.type === AddAnnotatorType.Split && item.marked}
                      key={item.uname}
                      label={item.uname}
                      value={item.uname}
                    />
                  ))}
                </ElSelect>
              </ElFormItem>
              <ElFormItem label="新增标注员" prop="newAnnotators">
                <ElSelect
                  collapseTags
                  collapseTagsTooltip
                  filterable
                  loading={searchUserIsLoading.value}
                  multiple
                  placeholder="请选择标注员"
                  remote
                  remoteMethod={remoteMethod}
                  remoteShowSuffix
                  v-model={form.newAnnotators}
                  v-stopComposeEnter>
                  {unameList.value
                    ? unameList.value.map((item) => (
                        <ElOption key={item.uname} label={item.uname} value={item.uname} />
                      ))
                    : optionsMapUnameList.value?.map((item) => (
                        <ElOption key={item.id} label={item.id} value={item.id} />
                      ))}
                </ElSelect>
              </ElFormItem>
            </ElForm>
          ),
          footer: () => (
            <span class="dialog-footer">
              <ElButton onClick={() => (visible.value = false)}>取消</ElButton>
              <ElButton
                onClick={submit}
                // loading={UpdateObserverLoading.value} onClick={onConfirm}
                type="primary">
                确定
              </ElButton>
            </span>
          )
        }}
      </ElDialog>
    )
  }
})
