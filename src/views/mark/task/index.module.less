.table-container {
  border-radius: 4px;
  @apply bg-white h-full flex flex-col flex-1 overflow-hidden;
}

.tabs {
  @apply flex flex-col overflow-auto shrink-0;
  border-bottom: none;
  :global {
    .el-tabs__content {
      @apply overflow-auto p-0 flex flex-col;
    }
    .el-tab-pane {
      @apply overflow-auto flex flex-col;
    }
    .el-table {
      @apply flex-1;
    }
  }
  .search {
    position: sticky;
    z-index: 10;
    top: 0;
    margin-bottom: 0;
  }
}
.tab-pane-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
  background: white;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.rotate-icon-container {
  animation: rotate 1s linear infinite;
}
