import {
  QuestionSetting,
  QuestionType,
  NumberConfigType,
  AnnotationRole,
  AnnotationGranularity
} from '@/api/marktask/type'
import { generateTableList, resetObj } from '@znzt-fe/utils'
import {
  ElButton,
  ElButtonGroup,
  ElFormItem,
  ElInputNumber,
  ElOption,
  ElSelect
} from 'element-plus'
import { cloneDeep } from 'lodash-es'
import { useRule, useElForm, useCheckBoxSingle } from '@znzt-fe/hooks'
import QuestionOptionsSetting from '../questionOptionsSetting'
export const enum DialogMode {
  Add,
  Edit
}
type OpenDialogFunc = (index: number, mode: DialogMode) => void
export const useDialog = (
  form: QuestionSetting,
  data: Ref<QuestionSetting[]>,
  clearValidate: () => Promise<void>
) => {
  const dialogInfo = reactive({
    visible: false,
    mode: DialogMode.Add,
    index: -1
  })
  const openDialog: OpenDialogFunc = (index, mode) => {
    if (mode === DialogMode.Edit && typeof index === 'number') {
      resetObj(form, data.value[index])
    }
    dialogInfo.mode = mode
    dialogInfo.visible = true
    dialogInfo.index = index
    clearValidate()
  }
  return {
    openDialog,
    dialogInfo
  }
}

export const useForm = (data: Ref<QuestionSetting[]>) => {
  const initData: QuestionSetting = {
    name: '',
    type: QuestionType.single,
    options: [],
    numberConfig: {
      max: 0,
      min: 0,
      percision: 0,
      type: NumberConfigType['input']
    },
    disabled: false,
    mustAnswer: true,
    annotationRole: AnnotationRole['Annotationer'],
    annotationGranularity: AnnotationGranularity['Assistant']
  }
  const {
    formRef,
    validate,
    clearValidate,
    validateField,
    form,
    resetForm: resetFormOrigin
  } = useElForm(initData)
  const { checkIdList, checkId } = useCheckBoxSingle<AnnotationRole>(form, 'annotationRole')
  const { checkIdList: granularityCheckIdList, checkId: granularityCheckId } =
    useCheckBoxSingle<AnnotationGranularity>(form, 'annotationGranularity')
  const resetForm = () => {
    resetFormOrigin()
    checkId.value = AnnotationRole['Annotationer']
    granularityCheckId.value = AnnotationGranularity['Assistant']
  }
  const resetNumberConfig = () =>
    (form.numberConfig = {
      ...initData.numberConfig,
      type: form.numberConfig.type
    })
  const rules = useRule({
    name: '请输入题目名称',
    type: '请输入任务类型',
    options: '请添加选项列表',
    annotationRole: '请选择标注角色',
    annotationGranularity: '请选择粒度'
  })
  const typeChange = (e: QuestionType) =>
    e === QuestionType.multipleChoice && (form.numberConfig.type = NumberConfigType['input'])
  const submitForm = async (index: number, mode: DialogMode) => {
    const result = await validate()
    if (!result) return
    if (mode === DialogMode.Add) {
      data.value.splice(index, 0, cloneDeep(form))
    } else if (mode === DialogMode.Edit) {
      data.value.splice(index!, 1, cloneDeep(form))
    }
    return true
  }

  const Question = () => {
    switch (form.type) {
      case QuestionType.single:
        return (
          <ElFormItem label="选项列表" prop="options">
            <QuestionOptionsSetting
              onChange={() => validateField('options')}
              v-model={form.options}
            />
          </ElFormItem>
        )
      case QuestionType.multiple:
        return (
          <ElFormItem label="选项列表" prop="options">
            <QuestionOptionsSetting
              levelTip
              onChange={() => validateField('options')}
              v-model={form.options}
            />
          </ElFormItem>
        )
      case QuestionType.number:
        return (
          <>
            <ElFormItem label="数字题类型" prop="numberConfig.type">
              <ElSelect
                class="!w-full"
                onChange={resetNumberConfig}
                v-model={form.numberConfig.type}>
                {NumberTypeOptions.map((item) => (
                  <ElOption key={item.value} label={item.label} value={item.value} />
                ))}
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="最大值" prop="numberConfig.max">
              <ElInputNumber
                class="!w-full"
                controls={false}
                min={form.numberConfig.min}
                precision={0}
                v-model={form.numberConfig.max}
                value-on-clear={initData.numberConfig.max}
              />
            </ElFormItem>
            <ElFormItem label="最小值" prop="numberConfig.min">
              <ElInputNumber
                class="!w-full"
                controls={false}
                max={form.numberConfig.max}
                precision={0}
                v-model={form.numberConfig.min}
                value-on-clear={initData.numberConfig.min}
              />
            </ElFormItem>
            {form.numberConfig.type === NumberConfigType.input && (
              <ElFormItem label="精度" prop="numberConfig.min">
                <ElInputNumber
                  controls={false}
                  min={0}
                  precision={0}
                  v-model={form.numberConfig.percision}
                  value-on-clear={initData.numberConfig.percision}
                />
              </ElFormItem>
            )}
          </>
        )
      case QuestionType.multipleChoice:
        return (
          <ElFormItem label="选项列表" prop="options">
            <QuestionOptionsSetting
              onChange={() => validateField('options')}
              v-model={form.options}
            />
          </ElFormItem>
        )
      default:
        return null
    }
  }
  return {
    formRef,
    rules,
    resetForm,
    form,
    submitForm,
    clearValidate,
    validateField,
    initData,
    resetNumberConfig,
    Question,
    typeChange,
    checkIdList,
    granularityCheckIdList
  }
}

export const useTable = (data: Ref<QuestionSetting[]>, openDialog: OpenDialogFunc) => {
  const tableColumn = generateTableList<QuestionSetting>([
    {
      prop: 'name',
      label: '题目'
    },
    {
      prop: 'type',
      label: '类型',
      slots: (scope) => TypeOptions.find((typeOption) => typeOption.value === scope.row.type)?.label
    },
    {
      prop: 'operation',
      label: '操作',
      slots: (scope) => {
        const index = scope.$index
        const isLastIndex = index === data.value?.length
        return isLastIndex ? (
          <ElButton link onClick={() => openDialog(index + 1, DialogMode['Add'])} type="primary">
            新增
          </ElButton>
        ) : (
          <ElButtonGroup>
            <ElButton link onClick={() => openDialog(index + 1, DialogMode['Add'])} type="primary">
              新增
            </ElButton>
            <ElButton link onClick={() => openDialog(index, DialogMode['Edit'])} type="primary">
              修改
            </ElButton>
            <ElButton
              disabled={scope.row.disabled}
              link
              onClick={() => data.value.splice(index, 1)}
              type="danger">
              删除
            </ElButton>
          </ElButtonGroup>
        )
      }
    }
  ])
  return {
    tableColumn
  }
}

export const TypeOptions = [
  { label: '单选题', value: QuestionType.single },
  { label: '多选题', value: QuestionType.multiple },
  { label: '数字题', value: QuestionType.number },
  { label: '主观题', value: QuestionType.subjective },
  { label: '多选数字题', value: QuestionType.multipleChoice }
]

export const NumberTypeOptions = [
  {
    label: '数字选择题',
    value: NumberConfigType.select
  },
  {
    label: '数字输入题',
    value: NumberConfigType.input
  }
]
