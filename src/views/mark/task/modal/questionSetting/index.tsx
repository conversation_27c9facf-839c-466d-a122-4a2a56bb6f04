import { QuestionSetting } from '@/api/marktask/type'
import ModalBottom from '@/components/modal-bottom'
import {
  ElCheckbox,
  ElCheckboxGroup,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElOption,
  ElSelect,
  ElSwitch,
  ElTable
} from 'element-plus'
import { useOptions } from '../hooks'
import style from '../style.module.less'
import { DialogMode, TypeOptions, useDialog, useForm, useTable } from './hooks'
export default defineComponent({
  props: {
    modelValue: {
      type: Array as PropType<QuestionSetting[]>,
      default: () => []
    },
    isMarkSession: {
      type: Boolean,
      default: false
    },
    isMedia: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue'],
  setup(props) {
    const questionSetting = useModel(props, 'modelValue')
    const {
      resetForm,
      formRef,
      form,
      rules,
      submitForm,
      clearValidate,
      Question,
      typeChange,
      checkIdList,
      granularityCheckIdList
    } = useForm(questionSetting)
    const { openDialog, dialogInfo } = useDialog(form, questionSetting, clearValidate)
    const { tableColumn } = useTable(questionSetting, openDialog)
    const submit = async () => {
      const result = await submitForm(dialogInfo.index, dialogInfo.mode)
      if (result) dialogInfo.visible = false
    }
    const { optionsMap } = useOptions()
    return () => (
      <>
        <ElTable data={[...questionSetting.value, {}]}>{tableColumn}</ElTable>
        <ElDialog
          onClosed={resetForm}
          title={dialogInfo.mode === DialogMode.Add ? '新增题目' : '编辑题目'}
          v-model={dialogInfo.visible}>
          <ElForm
            class={style['form-item-margin']}
            label-width="90px"
            model={form}
            ref={formRef}
            rules={{ ...rules }}>
            <ElFormItem label="题目名称" prop="name">
              <ElInput class="!w-full" disabled={form.disabled} v-model={form.name} />
            </ElFormItem>
            <ElFormItem label="题目类型" prop="type">
              <ElSelect
                class="!w-full"
                disabled={form.disabled}
                onChange={typeChange}
                v-model={form.type}>
                {TypeOptions.map((item) => (
                  <ElOption key={item.value} label={item.label} value={item.value} />
                ))}
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="是否必选" prop="type">
              <ElSwitch v-model={form.mustAnswer} />
            </ElFormItem>
            {props.isMarkSession && (
              <>
                <ElFormItem label="标注角色" prop="annotationRole">
                  <ElCheckboxGroup v-model={checkIdList.value}>
                    {optionsMap.value?.annotationRoleList.map((item) => (
                      <ElCheckbox key={item.id} value={item.id}>
                        {item.name}
                      </ElCheckbox>
                    ))}
                  </ElCheckboxGroup>
                </ElFormItem>
                <ElFormItem label="题目粒度" prop="annotationGranularity">
                  <ElCheckboxGroup v-model={granularityCheckIdList.value}>
                    {optionsMap.value?.annotationGranularityList.map((item) => (
                      <ElCheckbox key={item.id} value={item.id}>
                        {item.name}
                      </ElCheckbox>
                    ))}
                  </ElCheckboxGroup>
                </ElFormItem>
              </>
            )}
            {props.isMedia && (
              <ElFormItem label="题目粒度" prop="annotationGranularity">
                <ElCheckboxGroup v-model={granularityCheckIdList.value}>
                  {optionsMap.value?.annotationGranularityList.map((item) => (
                    <ElCheckbox key={item.id} value={item.id}>
                      {item.name}
                    </ElCheckbox>
                  ))}
                </ElCheckboxGroup>
              </ElFormItem>
            )}
            <Question />
          </ElForm>
          <ModalBottom onConfirm={submit} onReset={resetForm} />
        </ElDialog>
      </>
    )
  }
})
