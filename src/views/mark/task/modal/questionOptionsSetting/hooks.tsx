import { generateTableList, resetObj } from '@znzt-fe/utils'
import { ElButton } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import { useElForm, useRule } from '@znzt-fe/hooks'
export const enum DialogMode {
  Add,
  Edit
}
const JudgeMode = (index?: number) =>
  typeof index === 'number' && index >= 0 ? DialogMode.Edit : DialogMode.Add
type OpenDialogFunc = (index?: number) => void
export type QuestionOptionsSetting = {
  name: string
}
export const useDialog = (
  form: QuestionOptionsSetting,
  data: Ref<QuestionOptionsSetting[]>,
  clearValidate: () => Promise<void>
) => {
  const dialogInfo = reactive({
    visible: false,
    mode: DialogMode.Add,
    index: -1
  })
  const openDialog: OpenDialogFunc = (index) => {
    const mode = JudgeMode(index)
    if (mode === DialogMode.Edit && typeof index === 'number') {
      resetObj(form, data.value[index])
    }
    dialogInfo.mode = mode
    dialogInfo.visible = true
    dialogInfo.index = index ?? -1
    clearValidate()
  }
  return {
    openDialog,
    dialogInfo
  }
}

export const useForm = (data: Ref<QuestionOptionsSetting[]>) => {
  const initData: QuestionOptionsSetting = {
    name: ''
  }
  const { formRef, clearValidate, validate, form, resetForm, event } = useElForm(initData)
  const rules = useRule({
    name: '请输入题目名称'
  })
  const submitForm = async (index?: number) => {
    const result = await validate()
    if (!result) return
    const mode = JudgeMode(index)
    if (mode === DialogMode.Add) {
      data.value.push(cloneDeep(form))
    } else if (mode === DialogMode.Edit) {
      data.value.splice(index!, 1, cloneDeep(form))
    }
    return true
  }
  return {
    formRef,
    rules,
    resetForm,
    form,
    submitForm,
    clearValidate,
    event
  }
}

export const useTable = (data: Ref<QuestionOptionsSetting[]>, openDialog: OpenDialogFunc) => {
  const tableColumn = generateTableList<QuestionOptionsSetting>([
    {
      prop: 'name',
      label: '题目'
    },
    {
      prop: 'operation',
      label: '操作',
      slots: (scope) => {
        const index = scope.$index
        const isLastIndex = index === data.value?.length
        return isLastIndex ? (
          <ElButton link onClick={() => openDialog()} type="primary">
            新增
          </ElButton>
        ) : (
          <>
            <ElButton link onClick={() => openDialog(index)} type="primary">
              修改
            </ElButton>
            <ElButton link onClick={() => data.value.splice(index, 1)} type="danger">
              删除
            </ElButton>
          </>
        )
      }
    }
  ])
  return {
    tableColumn
  }
}
