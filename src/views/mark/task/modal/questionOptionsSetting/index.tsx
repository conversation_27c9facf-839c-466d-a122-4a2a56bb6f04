import { ElDialog, ElForm, ElFormItem, ElInput, ElTable } from 'element-plus'
import { useDialog, DialogMode, useForm, useTable, QuestionOptionsSetting } from './hooks'
import style from '../style.module.less'
import { isEqual } from 'lodash-es'
import ModalBottom from '@/components/modal-bottom'
import { Tooltip } from '@znzt-fe/components'
export default defineComponent({
  props: {
    modelValue: {
      type: Array as PropType<string[]>,
      default: () => []
    },
    levelTip: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    const questionOptionsSettingModel = useModel(props, 'modelValue') as Ref<string[]>
    const questionOptionsSetting = ref<QuestionOptionsSetting[]>([])
    watchImmediate(questionOptionsSettingModel, (val, oldVal) => {
      if (isEqual(val, oldVal)) return
      questionOptionsSetting.value = val.map((item) => ({ name: item }))
    })
    watchDeep(questionOptionsSetting, (val) => {
      emit('change')
      questionOptionsSettingModel.value = val.map((item) => item.name)
    })
    const { resetForm, formRef, form, rules, submitForm, clearValidate, event } =
      useForm(questionOptionsSetting)
    const { openDialog, dialogInfo } = useDialog(form, questionOptionsSetting, clearValidate)
    const { tableColumn } = useTable(questionOptionsSetting, openDialog)
    const submit = async () => {
      const result = await submitForm(dialogInfo.index)
      if (result) dialogInfo.visible = false
    }
    return () => (
      <>
        <ElTable data={[...questionOptionsSetting.value, {}]}>{tableColumn}</ElTable>
        <ElDialog
          onClosed={resetForm}
          title={dialogInfo.mode === DialogMode.Add ? '新增' : '编辑'}
          v-model={dialogInfo.visible}>
          <ElForm
            {...event}
            class={style['form-item-margin']}
            label-width="80px"
            model={form}
            ref={formRef}
            rules={{ ...rules }}>
            <ElFormItem label="选项名称" labelWidth={props.levelTip ? 100 : 80} prop="name">
              {{
                label: () =>
                  props.levelTip ? (
                    <div class="flex">
                      <span>选项名称</span>{' '}
                      <Tooltip
                        content="使用“/”增加二级节点选项（如果选项包含“/”，请使用“//”替代）"
                        placement="auto"
                      />
                    </div>
                  ) : (
                    '选项名称'
                  ),
                default: () => <ElInput class="!w-full" v-model={form.name} />
              }}
            </ElFormItem>
          </ElForm>
          <ModalBottom onConfirm={submit} onReset={resetForm} />
        </ElDialog>
      </>
    )
  }
})
