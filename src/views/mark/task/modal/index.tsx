import { TaskFileType } from '@/api/marktask/type'
import QuillEditor from '@/components/quill-editor'
import { useModelTree } from '@/hooks/useModelTree'
import { Delete, Download, Folder, Link } from '@element-plus/icons-vue'
import { Suspense } from '@znzt-fe/components'
import {
  ElAlert,
  ElButton,
  ElCheckbox,
  ElCheckboxGroup,
  ElDatePicker,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElOption,
  ElSelect,
  ElSkeleton,
  ElStep,
  ElSteps,
  ElSwitch,
  ElTabPane,
  ElTabs,
  ElTooltip,
  ElUpload
} from 'element-plus'
import { ScoreType } from '../../util'
import {
  CreateProgress,
  useAllModels,
  useForm,
  useMode,
  useOptions,
  useStep,
  useTab
} from './hooks'
import QuestionSetting from './questionSetting'
import style from './style.module.less'
import TaskTemplateModal from './taskTemplateModal'
const ModelParam = defineAsyncComponent(() => import('@/views/task/model-param.vue'))

export default defineComponent({
  emits: ['update:modelValue', 'refetch'],
  props: {
    modelValue: Boolean,
    addType: Number as PropType<TaskFileType>
  },
  setup(props, { emit }) {
    const visible = useModel(props, 'modelValue')
    const { modelMap, modelMapIsLoading, TreeSelect, model } = useModelTree(() => allModelsChange())
    /** 处理options */
    const {
      generateOption,
      optionsMap,
      marktaskTemplate,
      marktaskTemplateLoading,
      markStandardList,
      markStandardListLoading,
      remoteMethod,
      unameList,
      searchUserIsLoading
    } = useOptions()

    const { isCreate, downLoadFile, uploadType, addType } = useMode(marktaskTemplate)
    /** 处理model */
    const {
      allModels,
      modelParams,
      modelParamValue,
      initAllModels,
      finalModels,
      allModelsChange,
      modifyModelParamValue
    } = useAllModels(modelMap, model)

    const { activeTab } = useTab(finalModels)
    /** form基本能力 */
    const {
      submitTask,
      rules,
      formRef,
      form,
      isLoading,
      repeatSourceMessage,
      uploadRef,
      uploadSuccess,
      handleExceed,
      businessId,
      needAnnotator,
      checkIdList,
      selectContent,
      validateForm,
      clearValidate,
      fileList,
      categoryId,
      scoreTypeChange,
      displayModeCheckIdList
    } = useForm(
      optionsMap,
      () => {
        emit('refetch')
        active.value = CreateProgress.taskConfig
      },
      initAllModels,
      isCreate,
      visible,
      finalModels,
      () => (active.value = CreateProgress.taskConfig),
      model
    )

    const checkRepeatSource = (source: string, value: string) => {
      const isRepeat = finalModels.value
        ?.filter((item) => item.source !== source)
        .some((item) => item.editSource === value)
      if (isRepeat) {
        repeatSourceMessage(value)
      }
    }
    const disabledShowSource = ref(false)
    const { active, stepList, nextStep, preStep } = useStep(validateForm, clearValidate)
    watch(
      () => form.scoreType,
      (val) => {
        if (val === ScoreType['MarkSession']) {
          addType.value = TaskFileType['import']
          disabledShowSource.value = true
        } else {
          disabledShowSource.value = false
        }
      }
    )
    return () => (
      <ElDialog fullscreen title="新建标注任务" v-model={visible.value}>
        <ElSteps active={active.value} alignCenter>
          {stepList.map((title, index) => (
            <ElStep key={index} title={title} />
          ))}
        </ElSteps>
        {active.value === CreateProgress.taskConfig && (
          <TaskTemplateModal onSelectContent={selectContent} />
        )}
        <ElForm
          labelPosition="left"
          labelWidth="140px"
          model={form}
          ref={formRef}
          rules={{ ...rules }}>
          {active.value === CreateProgress.taskConfig ? (
            <>
              <h3>任务配置</h3>
              <ElFormItem label="任务名称" prop="name">
                <ElInput style={{ width: '189px' }} v-model={form.name} />
              </ElFormItem>
              <ElFormItem label="任务类别" prop="categoryId">
                <ElSelect clearable v-model={categoryId.value}>
                  {generateOption('categoryList')}
                </ElSelect>
              </ElFormItem>
              {/* <ElFormItem label="任务类型" prop="type">
                <ElSelect v-model={form.type}>{generateOption('taskTypeList')}</ElSelect>
              </ElFormItem> */}
              {/* <ElFormItem label="语言类型" prop="languageType">
                <ElSelect v-model={form.languageType}>
                  {generateOption('languageTypeList')}
                </ElSelect>
              </ElFormItem> */}
              <ElFormItem label="简要描述">
                <ElInput type="textarea" v-model={form.desc} />
              </ElFormItem>
              <ElFormItem
                label="标注标准"
                prop="standardId"
                v-loading={markStandardListLoading.value}>
                <div>
                  {markStandardList.value && !markStandardList.value.length && (
                    <ElAlert
                      closable={false}
                      style={{
                        padding: 0
                      }}
                      title={'请业务线管理员进入“业务管理”-“标注标准管理”中增加标注标准'}
                      type="warning"
                    />
                  )}
                  {markStandardList.value && !!markStandardList.value.length && (
                    <>
                      <ElCheckboxGroup class="ml-4" v-model={checkIdList.value}>
                        {markStandardList.value?.map((item) => (
                          <ElCheckbox key={item.id} size="small" value={item.id}>
                            {item.name}
                          </ElCheckbox>
                        ))}
                      </ElCheckboxGroup>
                      {!!markStandardList.value?.find((item) => item.id === form.standardId)
                        ?.content && (
                        <QuillEditor
                          height="100px"
                          modelValue={
                            markStandardList.value?.find((item) => item.id === form.standardId)
                              ?.content || ''
                          }
                        />
                      )}
                    </>
                  )}
                </div>
              </ElFormItem>
              <ElFormItem label="截止时间" prop="deadLine">
                <ElDatePicker
                  // https://dayjs.fenxianglu.cn/category/plugin.html#advancedformat
                  disabledDate={(val: Date) => dayjs(val).isBefore(dayjs().subtract(1, 'day'))}
                  placeholder="请选择截止时间"
                  type="datetime"
                  v-model={form.deadLine}
                  valueFormat="X"
                />
              </ElFormItem>
              <ElFormItem label="是否标注标签" prop="isMarkTag">
                <ElCheckbox v-model={form.isMarkTag} />
              </ElFormItem>
              <ElFormItem label="是否展示生成源" prop="isShowSource">
                <ElCheckbox v-model={form.isShowSource} />
              </ElFormItem>
              <h3>题型配置</h3>
              <ElFormItem label="评分类型" prop="scoreType">
                <ElSelect onChange={scoreTypeChange} v-model={form.scoreType}>
                  {generateOption('scoreTypeList')}
                </ElSelect>
              </ElFormItem>
              {(form.scoreType === ScoreType.MarkScoreType3 ||
                form.scoreType === ScoreType.MarkScoreType4 ||
                form.scoreType === ScoreType.MarkScoreType5) && (
                <ElFormItem label="是否支持半星" prop="halfScore">
                  <ElSwitch v-model={form.halfScore} />
                </ElFormItem>
              )}
              {form.scoreType === ScoreType.MarkScoreTypeSort && (
                <ElFormItem label="rank分数" prop="config.score">
                  <ElInputNumber min={1} step={1} stepStrictly v-model={form.config.score} />
                </ElFormItem>
              )}
              <ElFormItem label="附加题目设置" prop="questionSetting">
                <QuestionSetting
                  isMarkSession={form.scoreType === ScoreType['MarkSession']}
                  isMedia={form.scoreType === ScoreType['MarkScoreTypeAudioAndVideo']}
                  v-model={form.questionSetting}
                />
              </ElFormItem>
              <ElFormItem label="展示模式" prop="questionDisplayMode">
                <ElCheckboxGroup v-model={displayModeCheckIdList.value}>
                  {optionsMap.value?.questionDisplayModeList.map((item) => (
                    <ElCheckbox key={item.id} label={item.name} value={item.id} />
                  ))}
                </ElCheckboxGroup>
              </ElFormItem>
              <ElFormItem label="分配粒度" prop="allocateGranularity">
                <ElSelect class="!w-full" v-model={form.allocateGranularity}>
                  {generateOption('allocateGranularityList')}
                </ElSelect>
              </ElFormItem>
            </>
          ) : active.value === CreateProgress.dataConfig ? (
            <>
              {form.scoreType !== ScoreType.MarkScoreTypeAudioAndVideo && (
                <ElFormItem label="在线调用模型" prop="isShowSource">
                  <ElSwitch
                    activeValue={TaskFileType.create}
                    disabled={!!form.uploadId || disabledShowSource.value}
                    inactiveValue={TaskFileType.import}
                    v-model={addType.value}
                  />
                </ElFormItem>
              )}
              <ElFormItem label="导入prompt文本" prop="uploadId">
                <div class={style.buttonGroup}>
                  <ElUpload
                    accept=".xlsx,.json"
                    action="/openmis/marktask/import"
                    data={{ type: uploadType.value, businessId }}
                    limit={1}
                    onExceed={handleExceed}
                    onRemove={() => (form.uploadId = '')}
                    onSuccess={uploadSuccess}
                    ref={uploadRef}
                    v-model:file-list={fileList.value}>
                    <ElButton icon={<Folder />} size="small" type="primary">
                      选择文件
                    </ElButton>
                    <ElButton
                      icon={<Download />}
                      loading={marktaskTemplateLoading.value}
                      onClick={(e) => {
                        e.stopPropagation()
                        downLoadFile('xlsx')
                      }}
                      size="small"
                      type="success">
                      xlsx格式参考
                    </ElButton>
                    <ElButton
                      icon={<Link />}
                      loading={marktaskTemplateLoading.value}
                      onClick={(e) => {
                        e.stopPropagation()
                        downLoadFile('json')
                      }}
                      size="small"
                      type="success">
                      json格式参考
                    </ElButton>
                    <ElButton
                      link
                      onClick={(e) => {
                        window.open('https://docs.zuoyebang.cc/doc/1833461086213218324?ddtab=true')
                        e.stopPropagation()
                      }}
                      type="primary">
                      人工标注文件格式说明
                    </ElButton>
                  </ElUpload>
                </div>
              </ElFormItem>
              {isCreate.value && form.scoreType !== ScoreType.MarkScoreTypeAudioAndVideo && (
                <>
                  <ElFormItem label="选择模型" prop="models">
                    {modelMapIsLoading.value ? (
                      <ElSkeleton animated rows={3} />
                    ) : (
                      <>
                        <TreeSelect />
                        <div class="w-full">
                          {modelMap.value?.list
                            .filter((item) => model.value.includes(item.model))
                            .map((item) => (
                              <div class="mt-[4px] flex items-center" key={item.id}>
                                {item.desc ? (
                                  <ElTooltip content={item.desc} placement="top">
                                    {item.model}
                                  </ElTooltip>
                                ) : (
                                  item.model
                                )}
                                <ElInputNumber
                                  class={style.info}
                                  min={1}
                                  modelValue={
                                    allModels.value?.find((model) => item.id === model.id)
                                      ?.repeatTimes
                                  }
                                  onChange={(value) => {
                                    allModels.value!.find(
                                      (model) => item.id === model.id
                                    )!.repeatTimes = value
                                    allModelsChange()
                                  }}
                                  step={1}
                                  stepStrictly
                                  style={{ width: '150px', marginLeft: '10px' }}
                                  validate-event={false}
                                />
                                <span style={{ fontSize: '14px', marginLeft: '4px' }}>次</span>
                                <ElButton
                                  icon={<Delete />}
                                  link
                                  onClick={() => {
                                    const spliceIndex = model.value.findIndex(
                                      (model) => model === item.model
                                    )
                                    if (!~spliceIndex) return
                                    model.value.splice(spliceIndex, 1)
                                    allModelsChange()
                                  }}
                                  type="danger"
                                />
                              </div>
                            ))}
                        </div>
                      </>
                    )}
                  </ElFormItem>
                  {!!finalModels.value?.length && (
                    <ElFormItem label="生成源">
                      <div style={{ width: '100%' }}>
                        <ElTabs v-model={activeTab.value}>
                          {finalModels.value?.map((item) => (
                            <ElTabPane key={item.source} name={item.source}>
                              {{
                                label: () =>
                                  activeTab.value !== item.source ? (
                                    item.editSource
                                  ) : (
                                    <ElInput
                                      onChange={(value) => checkRepeatSource(item.source, value)}
                                      onKeydown={(e) => {
                                        e.stopPropagation()
                                        const { keyCode } = e as KeyboardEvent
                                        if (keyCode === 37 || keyCode === 39) e.preventDefault()
                                      }}
                                      style={{ minWidth: '100px' }}
                                      v-model={item.editSource}
                                    />
                                  )
                              }}
                            </ElTabPane>
                          ))}
                        </ElTabs>
                        {activeTab.value && (
                          <Suspense>
                            <ModelParam
                              modelValue={modelParamValue(activeTab.value)}
                              onUpdate={(data) => {
                                modifyModelParamValue({ source: activeTab.value, params: data })
                              }}
                              renderItems={modelParams(activeTab.value)!}
                            />
                          </Suspense>
                        )}
                      </div>
                    </ElFormItem>
                  )}
                </>
              )}
            </>
          ) : (
            <>
              {needAnnotator.value && (
                <>
                  <ElFormItem label="标注员" prop="unames">
                    <ElSelect
                      collapseTags
                      collapseTagsTooltip
                      filterable
                      loading={searchUserIsLoading.value}
                      multiple
                      placeholder="请选择标注员"
                      remote
                      remoteMethod={remoteMethod}
                      remoteShowSuffix
                      style="width: 230px"
                      v-model={form.unames}
                      v-stopComposeEnter>
                      {unameList.value
                        ? unameList.value.map((item) => (
                            <ElOption key={item.uname} label={item.uname} value={item.uname} />
                          ))
                        : optionsMap.value?.unameList.map((item) => (
                            <ElOption key={item.id} label={item.id} value={item.id} />
                          ))}
                    </ElSelect>
                  </ElFormItem>
                  <ElFormItem label="重复发放比例" prop="repetitionRatio">
                    <ElInputNumber
                      controls={false}
                      max={100}
                      min={0}
                      step={1}
                      step-strictly
                      style={{ width: '60px', marginRight: '4px' }}
                      v-model={form.repetitionRatio}
                    />
                    <span>%</span>
                  </ElFormItem>
                </>
              )}
              <ElFormItem label="观察员" prop="observers">
                <ElSelect
                  collapseTags
                  collapseTagsTooltip
                  filterable
                  loading={searchUserIsLoading.value}
                  multiple
                  placeholder="请选择观察员"
                  remote
                  remoteMethod={remoteMethod}
                  remoteShowSuffix
                  style="width: 230px"
                  v-model={form.observers}
                  v-stopComposeEnter>
                  {unameList.value
                    ? unameList.value.map((item) => (
                        <ElOption key={item.uname} label={item.uname} value={item.uname} />
                      ))
                    : optionsMap.value?.unameList.map((item) => (
                        <ElOption key={item.id} label={item.id} value={item.id} />
                      ))}
                </ElSelect>
              </ElFormItem>
              {form.scoreType === ScoreType['MarkScoreTypeWritingEval'] && (
                <ElFormItem label="质检员" prop="qiList">
                  <ElSelect
                    collapseTags
                    collapseTagsTooltip
                    filterable
                    loading={searchUserIsLoading.value}
                    multiple
                    placeholder="请选择质检员"
                    remote
                    remoteMethod={remoteMethod}
                    remoteShowSuffix
                    style="width: 230px"
                    v-model={form.qiList}
                    v-stopComposeEnter>
                    {unameList.value
                      ? unameList.value.map((item) => (
                          <ElOption key={item.uname} label={item.uname} value={item.uname} />
                        ))
                      : optionsMap.value?.unameList.map((item) => (
                          <ElOption key={item.id} label={item.id} value={item.id} />
                        ))}
                  </ElSelect>
                </ElFormItem>
              )}
            </>
          )}
        </ElForm>
        <div class={style.handleBtn}>
          {active.value !== CreateProgress.taskConfig && (
            <ElButton onClick={preStep}>上一步</ElButton>
          )}
          {active.value !== CreateProgress.personnelConfig && (
            <ElButton onClick={nextStep} type="primary">
              下一步
            </ElButton>
          )}
          <ElButton onClick={() => (visible.value = false)}>取消</ElButton>
          {active.value === CreateProgress.personnelConfig && (
            <ElButton loading={isLoading.value} onClick={submitTask} type="primary">
              提交
            </ElButton>
          )}
        </div>
      </ElDialog>
    )
  }
})
