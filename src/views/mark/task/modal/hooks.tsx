import { GetModelParamsRet } from '@/api/business/type'
import { useGetMarkStandardList } from '@/api/markstandard'
import { getMarkTaskOptions, useCreateMarkTask, useGetMarktaskTemplate } from '@/api/marktask'
import {
  CreateMarkTaskParams,
  CreateModelsItem,
  ImportMarkTaskResult,
  MarkTaskOptionsRet,
  MarktaskTemplateRet,
  ModelsItemParams,
  QuestionDisplayMode,
  TaskFileType,
  TaskTypeListItem
} from '@/api/marktask/type'
import { useSearchUser } from '@/api/user'
import { SearchUserRet } from '@/api/user/type'
import useUserStore from '@/store/user'
import {
  MarkScoreTypeWritingEvalQuestionSetting,
  MarkTaskTemplateInitData
} from '@/views/business/markTaskTemplate/modal/hooks'
import { useQuery } from '@znzt-fe/axios'
import { useCheckBoxSingle, useElForm, useRule, useSelect } from '@znzt-fe/hooks'
import type { ValidateField } from '@znzt-fe/hooks/dist/package/useElForm'
import { beforeNextTick, objectToArray, resetObj } from '@znzt-fe/utils'
import {
  ElMessage,
  ElOption,
  UploadInstance,
  UploadProps,
  UploadRawFile,
  genFileId
} from 'element-plus'
import { cloneDeep, intersectionBy, uniqBy } from 'lodash-es'
import { ScoreType } from '../../util'
export const enum CreateProgress {
  taskConfig = 1,
  dataConfig,
  personnelConfig
}
export const createProgress = {
  [CreateProgress['taskConfig']]: '任务配置',
  [CreateProgress['dataConfig']]: '数据配置',
  [CreateProgress['personnelConfig']]: '分配任务'
}
export type CompanyList = CompanyListItem[]
export interface CompanyListItem {
  label: string
  value: string
  modelList?: CompanyListItem[]
  disabled?: boolean
}
const createProgressArr = objectToArray(createProgress)
export const useStep = (
  validateForm: () => Promise<boolean | undefined>,
  clearValidate: () => Promise<void>
) => {
  const active = ref(CreateProgress.taskConfig)
  const stepList = createProgressArr.map((item) => item.value)
  const nextStep = async () => {
    const result = await validateForm()
    if (!result) return
    clearValidate()
    active.value++
  }
  watch(active, () => document.querySelector('.el-dialog__header')?.scrollIntoView())
  const preStep = () => active.value--
  return {
    active,
    stepList,
    nextStep,
    preStep
  }
}
export const useOptions = () => {
  const { current } = useUserStore()
  const { data: optionsMap } = useQuery(['getMarkTaskOptions'], () =>
    getMarkTaskOptions({ businessId: current.businessId })
  )
  const { data: marktaskTemplate, isLoading: marktaskTemplateLoading } = useGetMarktaskTemplate()
  const { data: markStandardListData, isLoading: markStandardListLoading } =
    useGetMarkStandardList()
  const markStandardList = computed(() => markStandardListData.value?.list)
  const generateOption = (key: keyof MarkTaskOptionsRet, value: keyof TaskTypeListItem = 'id') =>
    optionsMap.value?.[key].map((item) => (
      <ElOption key={item.id} label={item.name} value={item[value]} />
    ))
  const { mutate, isLoading: searchUserIsLoading } = useSearchUser({
    onSuccess: (data) => (unameList.value = data.list)
  })
  const unameList = ref<SearchUserRet['list'] | undefined>()
  const remoteMethod = (query: string) => {
    if (query) {
      mutate({
        pinyin: query,
        pageSize: 100,
        pageNum: 1
      })
    } else {
      unameList.value = undefined
    }
  }
  return {
    optionsMap,
    generateOption,
    marktaskTemplate,
    marktaskTemplateLoading,
    markStandardList,
    markStandardListLoading,
    remoteMethod,
    unameList,
    searchUserIsLoading
  }
}

type AllModels = Array<
  CreateModelsItem & {
    model: string
    repeatTimes: number | undefined
  }
>
type FinalModels = Array<CreateModelsItem & { editSource: string }>
export const useAllModels = (
  modelMap: Ref<GetModelParamsRet | undefined>,
  model: Ref<string[]>
) => {
  /** 完整的models数据结构 */
  const allModels = ref<AllModels>()

  /**，用于修改params中的 models */
  const finalModels = ref<FinalModels>([])

  /** 根据modelMap产生allModels */
  const initAllModels = () => {
    allModels.value = modelMap.value?.list.map((item) => ({
      source: item.model,
      id: item.id,
      repeatTimes: 1,
      params: item.advancedParameters.reduce<ModelsItemParams>((pre, now) => {
        const name = now.name
        Reflect.set(pre, name, now.defaultValue)
        return pre
      }, {} as ModelsItemParams),
      model: item.model
    }))
  }

  whenever(modelMap, initAllModels, { immediate: true })

  const allModelsChange = () => {
    const data = allModels.value
      ?.filter((item) => model.value.includes(item.model))
      .reduce<FinalModels>((pre, now) => {
        const { repeatTimes, source, ...rest } = now
        const newData = Array.from({ length: repeatTimes || 0 }).map((_item, index) => {
          return {
            ...cloneDeep({ ...rest }),
            source: `${source}${index !== 0 ? `(${index})` : ''}`,
            editSource: `${source}${index !== 0 ? `(${index})` : ''}`
          }
        })
        pre.push(...newData)
        return pre
      }, [])
    const oldExistModels = intersectionBy(toRaw(finalModels.value) || [], data || [], 'source')
    const uniqFinalModels = uniqBy([...oldExistModels, ...(data || [])], 'source')
    finalModels.value = uniqFinalModels
  }
  // invoke(() => allModelsChange())

  const modelParams = (source?: string) => {
    if (!source) return
    const id = finalModels.value?.find((item) => item.source === source)?.id
    return modelMap.value?.list.find((item) => item.id === id)?.advancedParameters
  }

  const modelParamValue = (source?: string) => {
    if (source)
      return finalModels.value?.find((item) => item.source === source)
        ?.params as Required<ModelsItemParams>
  }
  const modifyModelParamValue = ({
    source,
    params
  }: {
    source?: string
    params: ModelsItemParams
  }) => {
    if (!source) return
    const model = finalModels.value?.find((item) => item.source === source)
    if (!model?.params) return
    model.params = params
  }

  return {
    allModels,
    modelParams,
    modelParamValue,
    initAllModels,
    finalModels,
    model,
    allModelsChange,
    modifyModelParamValue
  }
}

export const useForm = (
  optionsMap: Ref<MarkTaskOptionsRet | undefined>,
  emitRefetch: () => void,
  initAllModels: () => void,
  isCreate: ComputedRef<boolean>,
  visible: Ref<boolean>,
  finalModels: Ref<FinalModels | undefined>,
  resetActive: () => void,
  model: Ref<string[]>
) => {
  const fileList = ref([])
  const initData: CreateMarkTaskParams = {
    name: '',
    type: undefined,
    scoreType: undefined,
    languageType: undefined,
    desc: '',
    unames: [],
    models: [],
    deadLine: '',
    businessCode: '',
    uploadId: '',
    standardId: '',
    repetitionRatio: 0,
    questionSetting: [],
    isMarkTag: false,
    isShowSource: false,
    config: {
      score: 0
    },
    halfScore: false,
    categoryId: undefined,
    observers: [],
    qiList: [],
    questionDisplayMode: QuestionDisplayMode['Horizontal'],
    allocateGranularity: ''
  }
  const scoreTypeChange = (e: ScoreType) => {
    if (e === ScoreType['MarkScoreTypeWritingEval']) {
      form.questionSetting = MarkScoreTypeWritingEvalQuestionSetting
    }
  }
  const selectContent = (data: string) => {
    const query: MarkTaskTemplateInitData['content'] = JSON.parse(data)
    checkId.value = query.standardId
    displayModeCheckId.value = query.questionDisplayMode
    resetObj(form, query)
    ElMessage.success('选择模板成功')
  }
  const resetFormData = async () => {
    fileList.value = []
    resetActive()
    resetForm()
    needAnnotator.value = false
    initAllModels()
    uploadRef.value?.clearFiles()
    clearValidate()
  }

  const { formRef, validate, clearValidate, validateField, form, resetForm } = useElForm(initData)
  const categoryId = useSelect(form, 'categoryId')
  const { checkIdList: displayModeCheckIdList, checkId: displayModeCheckId } =
    useCheckBoxSingle<QuestionDisplayMode>(form, 'questionDisplayMode')
  const { checkIdList, checkId } = useCheckBoxSingle(form, 'standardId')
  whenever(visible, beforeNextTick(resetFormData))
  const { current } = useUserStore()
  const rules = useRule({
    name: '请输入任务名称',
    type: '请选择任务类型',
    scoreType: '请选择评分类型',
    languageType: '请选择语言类型',
    models: {
      message: '请选择使用模型',
      validator: () => !!model.value.length
    },
    unames: '请选择参与用户',
    uploadId: {
      message: '请上传导入文本文件',
      validator: () => !!form.uploadId
    },
    standardId: '请选择标注标准',
    deadLine: '请选择截止时间',
    'config.score': '请填写评分数字',
    questionDisplayMode: '请选择展示模式',
    allocateGranularity: '请选择分配粒度'
  })
  const { mutate: createMarkTask, isLoading } = useCreateMarkTask({
    onSuccess(data) {
      emitRefetch()
      visible.value = false
      isCreate
        ? ElMessage.success(
            `本次生成任务共${data.promptNum}(prompt条数)x${data.modelNum}(模型数)=${
              data.modelNum * data.promptNum
            }条`
          )
        : ElMessage.success('创建成功')

      resetFormData()
    }
  })

  const { uploadSuccess, handleExceed, uploadRef, needAnnotator } = useUpload(form, validateField)
  /** form的unames读取上一次的结果 */
  const unamesInit = () =>
    (form.unames =
      optionsMap.value?.unameList.filter((item) => item.check).map((item) => item.id) || [])

  /** 自动填充任务类型，评分类型，语言类型 */
  whenever(
    () => {
      if (!optionsMap?.value || form.languageType || form.scoreType || form.type) return false
      return {
        optionsMap: optionsMap as ComputedRef<MarkTaskOptionsRet>,
        form
      }
    },
    (val) => {
      const { optionsMap, form } = val
      const { taskTypeList, scoreTypeList, languageTypeList, allocateGranularityList } =
        optionsMap.value
      form.languageType = languageTypeList?.[0].id
      form.scoreType = scoreTypeList?.[0].id
      form.allocateGranularity = allocateGranularityList?.[0].id
      form.type = taskTypeList?.[0].id
      unamesInit()
    }
  )

  const checkRepeatSource = () => {
    const editSourceSet = new Set(finalModels.value?.map((item) => item.editSource))
    const isRepeat = editSourceSet.size !== finalModels.value?.length
    if (isRepeat) {
      const sourceMap = new Map()
      const repeatSource = finalModels.value?.find((item) => {
        if (!editSourceSet.has(item.editSource)) {
          sourceMap.set(item.editSource, true)
          return false
        }
        return true
      })
      repeatSourceMessage(repeatSource!.editSource)
      return true
    }
    return false
  }
  const validateForm = async () => {
    const result = await validate()
    if (!result) return
    const repeatSource = checkRepeatSource()
    if (repeatSource) return
    return true
  }
  const submitTask = async () => {
    const result = await validateForm()
    if (!result) return
    form.models = isCreate.value
      ? finalModels.value!.map((item) => ({
          id: item.id,
          params: item.params,
          source: item.editSource
        }))
      : []
    form.businessCode = current.businessCode
    if (!needAnnotator) {
      form.repetitionRatio = 0
      form.questionSetting = []
    }
    createMarkTask(form)
  }

  const repeatSourceMessage = (source: string) => ElMessage.error(`生成源名称${source}重复，请修改`)

  return {
    submitTask,
    rules,
    formRef,
    form,
    isLoading,
    repeatSourceMessage,
    uploadSuccess,
    handleExceed,
    uploadRef,
    businessId: current.businessId,
    needAnnotator,
    checkIdList,
    displayModeCheckIdList,
    selectContent,
    validateForm,
    clearValidate,
    fileList,
    categoryId,
    scoreTypeChange
  }
}
type FileType = 'json' | 'xlsx'
export const useMode = (marktaskTemplate: Ref<MarktaskTemplateRet | undefined>) => {
  const isCreate = computed(() => addType.value === TaskFileType.create)
  const addType = ref<TaskFileType>(TaskFileType.create)
  const uploadType = computed(() => (isCreate.value ? 1 : 2))

  const fileMap = new Map<FileType, string>()
  whenever(marktaskTemplate, ({ jsonUrl, xlsxUrl }) =>
    fileMap.set('json', jsonUrl).set('xlsx', xlsxUrl)
  )

  const downLoadFile = (type: FileType) => window.open(fileMap.get(type))

  return {
    isCreate,
    addType,
    downLoadFile,
    uploadType
  }
}

export const useTab = (finalModels: Ref<FinalModels | undefined>) => {
  const activeTab = ref<string>()

  whenever(
    () => (finalModels.value?.length ? finalModels.value : false),
    (newVal, oldVal) => {
      /** 如果还没有赋值 */
      if (newVal?.length && !oldVal?.length) {
        activeTab.value = newVal[0].source
        return
      }
      /** 如果被删除之后active应该为当前的第0项 */
      if (!~toRaw(newVal).findIndex((item) => item.source === activeTab.value)) {
        activeTab.value = newVal[0].source
      }
    }
  )

  return {
    activeTab
  }
}

export const useUpload = (form: CreateMarkTaskParams, validateField: ValidateField) => {
  /** 是否需要展示标注员选择，分配比例 */
  const [needAnnotator, setNeedAnnotator] = useToggle(false)

  const uploadRef = ref<UploadInstance | undefined>()

  const uploadSuccess = async (data: {
    data: ImportMarkTaskResult
    errNo: string
    errMsg: string
  }) => {
    const { errNo, errMsg, data: result } = data
    if (errNo) {
      form.uploadId = ''
      ElMessage.error(errMsg)
      uploadRef.value?.clearFiles()
      setNeedAnnotator(false)
      return
    }
    setNeedAnnotator(result.needAnnotator)
    form.uploadId = result.id
    ElMessage.success('导入成功！')
    validateField('uploadId')
  }

  const handleExceed: UploadProps['onExceed'] = (files) => {
    uploadRef.value!.clearFiles()
    const file = files[0] as UploadRawFile
    file.uid = genFileId()
    uploadRef.value!.handleStart(file)
  }

  return {
    uploadSuccess,
    handleExceed,
    uploadRef,
    needAnnotator
  }
}
