import MarkTaskTemplate from '@/views/business/markTaskTemplate'
import { ElButton, ElPopover, PopoverInstance } from 'element-plus'
import { afterDecorator } from '@znzt-fe/utils'
export default defineComponent({
  emits: ['selectContent'],
  setup(_, { emit }) {
    const popoverRef = ref<PopoverInstance>()
    return () => (
      <ElPopover placement="right" ref={popoverRef} trigger="click" width="700">
        {{
          reference: () => (
            <ElButton size="small" style={{ marginBottom: '8px' }} type="primary">
              选择标注任务模板
            </ElButton>
          ),
          default: () => (
            <MarkTaskTemplate
              onSelectContent={afterDecorator(
                (value) => emit('selectContent', value),
                () => popoverRef.value?.hide()
              )}
              view={true}
            />
          )
        }}
      </ElPopover>
    )
  }
})
