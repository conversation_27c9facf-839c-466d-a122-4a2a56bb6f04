import {
  ElButton,
  ElTable,
  ElSelect,
  ElOption,
  ElTableColumn,
  ElFormItem,
  ElInput,
  ElTabs,
  ElTabPane,
  ElForm,
  ElDatePicker,
  ElRadioGroup,
  ElRadioButton
} from 'element-plus'
import SearchContainer from '@/components/search-container'
import AdvancedSearchContainer from '@/components/advanced-search-container'
import style from './index.module.less'
import { Pagination, Suspense } from '@znzt-fe/components'
import { useTable, useModal, useSelection, useForm, useSuspenseModal } from './hooks'
import { LabelType, MarkTypeFilter } from '@/api/marktask/type'
import useUserStore from '@/store/user'
import { RoleId } from '@/api/markrole/type'
const Modal = defineAsyncComponent(() => import('./modal'))
const MarkModal = defineAsyncComponent(() => import('./markModal'))
const RoleModal = defineAsyncComponent(() => import('./roleModal'))
export default defineComponent({
  name: 'mark-task',
  setup() {
    const {
      form,
      formRef,
      resetForm,
      event,
      markSearchOptions,
      categoryList,
      createTime,
      dataSetId,
      labelStyle,
      categoryId,
      fullTaskStatus,
      taskStatusList,
      getOptions
    } = useForm()
    const { createTask, visible, addType, suspenseVisible } = useModal()
    const {
      suspenseVisible: observerSuspenseVisible,
      modalId: observerModalId,
      visible: observerVisible,
      openModal: openObserverModal
    } = useSuspenseModal()
    const {
      suspenseVisible: qiListSuspenseVisible,
      modalId: qiListModalId,
      visible: qiListVisible,
      openModal: openQiListModal
    } = useSuspenseModal()
    const {
      suspenseVisible: markSuspenseVisible,
      modalId: markModalId,
      visible: markVisible,
      openModal: openMarkModal
    } = useSuspenseModal()
    const {
      isLoading,
      listParams,
      tableColumn,
      refetchData,
      changeMarkTypeFilter,
      getMarkTaskList,
      tabsList
    } = useTable(form, openObserverModal, openMarkModal, openQiListModal)
    const { sectionsRows, compareTask, handleSelectionChange } = useSelection()
    const eventAttr = {
      onChange: refetchData
    }
    const commonAttr = {
      clearable: true,
      ...eventAttr
    }
    const FormTaskName = () => (
      <ElFormItem class="!w-[240px]" label="任务名称">
        <ElInput {...commonAttr} placeholder="请输入任务名称" v-model={form.taskName} />
      </ElFormItem>
    )

    const userStore = useUserStore()
    const { isRefreshBusiness } = storeToRefs(userStore)
    onActivated(() => {
      // 切换业务线后，为了防止keep-alive造成的数据缓存，所以需要重新拉取options&refetch
      if (!isRefreshBusiness.value) return
      userStore.resetRefreshBusiness()
      resetForm()
      getOptions()
      refetchData()
    })
    return () => (
      <>
        <ElTabs
          beforeLeave={() => {
            tableColumn.value = []
          }}
          class={style['tabs']}
          onTabChange={() => changeMarkTypeFilter()}
          type="border-card"
          v-model={form.markTypeFilter}>
          {tabsList.map((item) => (
            <ElTabPane key={item.value} label={item.label} name={item.value} />
          ))}
        </ElTabs>
        <div class={style['tab-pane-container']}>
          <SearchContainer
            class={style.search}
            v-show={form.markTypeFilter === MarkTypeFilter.Automatic}>
            <div>
              <div>
                <ElForm inline labelWidth="70px">
                  <FormTaskName />
                  <ElFormItem class="!w-[240px]" label="数据集">
                    <ElSelect
                      filterable
                      v-model={dataSetId.value}
                      {...commonAttr}
                      placeholder="请选择数据集">
                      {markSearchOptions.value?.dataSets.map((item) => (
                        <ElOption key={item.id} label={item.name} value={item.id} />
                      ))}
                    </ElSelect>
                  </ElFormItem>
                </ElForm>
              </div>
              <ElButton
                disabled={sectionsRows.value.length < 2 || !form.dataSetId}
                onClick={compareTask}
                size="small"
                type="primary">
                对比
              </ElButton>
            </div>
          </SearchContainer>
          <div v-show={form.markTypeFilter === MarkTypeFilter.Manual}>
            <ElRadioGroup
              {...eventAttr}
              style={{ padding: '8px 16px 0' }}
              v-model={labelStyle.value}>
              <ElRadioButton value={undefined}>全部</ElRadioButton>
              {markSearchOptions.value?.labelStyles.map((item) => (
                <ElRadioButton key={item.id} value={item.id}>
                  {item.name} ({item.num})
                </ElRadioButton>
              ))}
            </ElRadioGroup>
            <ElForm
              labelWidth="70px"
              {...event}
              class={style['form']}
              inline
              model={form}
              ref={formRef}>
              <AdvancedSearchContainer
                onReset={() => {
                  resetForm()
                  refetchData()
                }}>
                {{
                  default: () => (
                    <div class="flex-just-space">
                      <ElButton onClick={createTask} type="primary">
                        新建标注任务
                      </ElButton>
                      <div>
                        <FormTaskName />
                        <ElFormItem class="!w-[240px]" label="任务类别">
                          <ElSelect
                            filterable
                            {...commonAttr}
                            placeholder="请选择任务类别"
                            v-model={categoryId.value}>
                            {categoryList.value.map((item) => (
                              <ElOption key={item.id} label={item.name} value={item.id} />
                            ))}
                          </ElSelect>
                        </ElFormItem>
                        {labelStyle.value !== LabelType.Mark && (
                          <ElFormItem class="!w-[240px]" label="任务状态">
                            <ElSelect
                              {...commonAttr}
                              placeholder="请选择任务状态"
                              v-model={fullTaskStatus.value}>
                              {taskStatusList.map((item) => (
                                <ElOption key={item.id} label={item.name} value={item.id} />
                              ))}
                            </ElSelect>
                          </ElFormItem>
                        )}
                      </div>
                    </div>
                  ),
                  extra: () => (
                    <div class="flex-just-end">
                      {labelStyle.value !== LabelType.Create && (
                        <ElFormItem class="!w-[240px]" label="创建者">
                          <ElInput
                            placeholder="请输入创建者"
                            {...commonAttr}
                            v-model={form.ownerUname}
                          />
                        </ElFormItem>
                      )}
                      <ElFormItem class="!w-[240px]" label="标注员">
                        <ElInput
                          placeholder="请输入标注员"
                          {...commonAttr}
                          v-model={form.annotator}
                        />
                      </ElFormItem>
                      <ElFormItem class="!w-[339px]" label="创建时间">
                        <ElDatePicker
                          disabledDate={(val: Date) => dayjs(val).isAfter(dayjs())}
                          {...eventAttr}
                          type="daterange"
                          v-model={createTime.value}
                          valueFormat="X"
                        />
                      </ElFormItem>
                    </div>
                  )
                }}
              </AdvancedSearchContainer>
            </ElForm>
          </div>
          <div class={style['table-container']} v-loading={isLoading.value}>
            <ElTable
              data={listParams.list}
              onSelection-change={handleSelectionChange}
              rowKey="id"
              style={{ width: '100%' }}>
              {form.markTypeFilter === MarkTypeFilter.Automatic && (
                <ElTableColumn align="center" fixed="left" type="selection" width={60} />
              )}
              {tableColumn.value}
            </ElTable>
            <Pagination
              onRefresh={getMarkTaskList}
              style={{ marginRight: '6px' }}
              total={listParams.pageInfo.total}
              v-model:pageNum={listParams.pageInfo.pageNum}
              v-model:pageSize={listParams.pageInfo.pageSize}
            />
          </div>
        </div>
        {suspenseVisible.value && (
          <Suspense empty>
            <Modal
              addType={addType.value}
              onRefetch={() => {
                refetchData()
                getOptions()
              }}
              v-model={visible.value}
            />
          </Suspense>
        )}
        {observerSuspenseVisible.value && (
          <Suspense empty>
            <RoleModal
              id={observerModalId.value}
              refetch={getOptions}
              roleId={RoleId['Observer']}
              roleName="观察员"
              v-model={observerVisible.value}
            />
          </Suspense>
        )}
        {qiListSuspenseVisible.value && (
          <Suspense empty>
            <RoleModal
              id={qiListModalId.value}
              refetch={getOptions}
              roleId={RoleId['QI']}
              roleName="质检员"
              v-model={qiListVisible.value}
            />
          </Suspense>
        )}
        {markSuspenseVisible.value && (
          <Suspense empty>
            <MarkModal
              id={markModalId.value}
              refetch={() => {
                refetchData()
                getOptions()
              }}
              v-model={markVisible.value}
            />
          </Suspense>
        )}
      </>
    )
  }
})
