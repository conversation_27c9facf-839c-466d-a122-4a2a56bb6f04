import {
  useDelMarkTask,
  useGetMarkTaskDetail,
  useMarktaskUpdate,
  useRetryMarkTask,
  useGetMarkSearchOptions,
  getMarkList,
  useRedistribute
} from '@/api/marktask'
import {
  MarkTaskListItem,
  MarkTaskStatus,
  PublishResult,
  TaskFileType,
  LockType,
  MarkResultListDetailParams,
  MarkTypeFilter,
  GetMarkTaskListParamQuery,
  GetMarkTaskListParams,
  LabelType,
  MarkType
} from '@/api/marktask/type'
import {
  useDateRange,
  useElForm,
  useList,
  usePermission,
  useSelect,
  useSuspense
} from '@znzt-fe/hooks'
// import style from './index.module.less'
import {
  afterDecorator,
  beforeNextTick,
  generateTableList,
  getUserPermission
} from '@znzt-fe/utils'
import {
  ButtonInstance,
  ElButton,
  ElButtonGroup,
  ElMessage,
  ElSwitch,
  ElTooltip,
  ElIcon,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElPopover,
  ElDescriptions,
  ElDescriptionsItem,
  ElEmpty,
  ElMessageBox
} from 'element-plus'
import duration from 'dayjs/plugin/duration'
import { statusTag, permissionList, finishRateTransform } from '@/views/mark/util'
import useUserStore from '@/store/user'
import { RoleId } from '@/api/user/type'
import { ArrowDown, QuestionFilled } from '@element-plus/icons-vue'
// Refresh
import { useCategoryList } from '@/views/business/markCategory/hooks'
import TooltipMessage from '@/components/tooltip-message'
import { JSX } from 'vue/jsx-runtime'
dayjs.extend(duration)
const useTimerMap = () => {
  const debounce = (func: Function, id: number) => () => {
    if (timerMap.value.has(id)) return
    const timer = setTimeout(() => timerMap.value.delete(id), 1000 * 60)
    timerMap.value.set(id, timer)
    func()
  }
  const timerMap = ref<Map<MarkTaskListItem['id'], NodeJS.Timeout>>(new Map())
  const clearAll = () => [...timerMap.value.values()].forEach(clearTimeout)
  onBeforeUnmount(clearAll)
  return {
    debounce
  }
}
type InitData = GetMarkTaskListParamQuery
export const useForm = () => {
  const initData: InitData = {
    annotator: '',
    ownerUname: '',
    dataSetId: undefined,
    markTypeFilter: MarkTypeFilter.Manual,
    taskName: '',
    categoryId: undefined,
    createStartTime: undefined,
    createEndTime: undefined,
    labelStyle: undefined,
    fullTaskStatus: undefined
  }
  const { formRef, resetForm, form, event } = useElForm(initData)
  const createTime = useDateRange(form, ['createStartTime', 'createEndTime'])
  const dataSetId = useSelect(form, 'dataSetId')
  const labelStyle = useSelect(form, 'labelStyle')
  const categoryId = useSelect(form, 'categoryId')
  const fullTaskStatus = useSelect(form, 'fullTaskStatus')
  const { listParams } = useCategoryList()
  const categoryList = computed(() => listParams.list)
  const { mutate: getOptions, data: markSearchOptions } = useGetMarkSearchOptions()
  onMounted(() => getOptions({}))
  const taskStatusList = [
    {
      id: MarkTaskStatus.inMark,
      name: '待标注'
    },
    {
      id: MarkTaskStatus.markDone,
      name: '已完成'
    }
  ]
  return {
    formRef,
    resetForm,
    form,
    event,
    markSearchOptions,
    categoryList,
    createTime,
    dataSetId,
    labelStyle,
    fullTaskStatus,
    categoryId,
    taskStatusList,
    getOptions: () => getOptions({})
  }
}
export const useLoadingList = () => {
  const loadingMap = ref<Record<number, boolean>>({})
  const setLoadingMap = (id: number, value: boolean) => {
    loadingMap.value[id] = value
  }
  return {
    loadingMap,
    setLoadingMap
  }
}
export const useTable = (
  query: InitData,
  openObserverModal: (id: number) => void,
  openMarkModal: (id: number) => void,
  openQiListModal: (id: number) => void
) => {
  const { mutate: redistributeMutate } = useRedistribute({
    onSuccess: () => ElMessage.success('分配成功')
  })
  // loadingMap
  const { setLoadingMap } = useLoadingList()
  const tabsList = [
    {
      value: MarkTypeFilter.Automatic,
      label: '自动化标注'
    },
    {
      value: MarkTypeFilter.Manual,
      label: '人工标注'
    }
  ]
  const { businessList } = storeToRefs(useUserStore())
  const BUTTONTYPE: Pick<ButtonInstance, 'link' | 'type'> = {
    link: true,
    type: 'primary'
  }
  const router = useRouter()
  const {
    listParams,
    refetchData,
    refetchDataAsync,
    isLoading: listLoading,
    mutate: getMarkTaskList
  } = useList<MarkTaskListItem>({
    getList: (params: GetMarkTaskListParams) => {
      let mutationParams: GetMarkTaskListParams
      if (params.markTypeFilter === MarkTypeFilter.Automatic) {
        const { markTypeFilter, taskName, dataSetId, pageNum, pageSize } = params
        mutationParams = { taskName, dataSetId, markTypeFilter, pageNum, pageSize }
      } else {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { dataSetId, ...rest } = params
        const manualProps = { ...rest }
        if (params.labelStyle === LabelType.Create) {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { ownerUname, ...rest } = manualProps
          // 选择标签我创建的，则自动屏蔽高级输入中的创建者
          mutationParams = { ...rest }
        } else if (params.labelStyle === LabelType.Mark) {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { fullTaskStatus, ...rest } = manualProps
          // 选择标签我标注的，则自动屏蔽任务状态
          mutationParams = { ...rest }
        } else {
          mutationParams = manualProps
        }
      }
      return getMarkList(mutationParams)
    },
    query
  })

  const { mutate: marktaskUpdate } = useMarktaskUpdate({
    onSuccess: (_data, params) => refreshMarkTaskDetail({ id: params.id })
  })
  const { mutate: delMarkTask, isLoading: delMarkTaskLoading } = useDelMarkTask({
    onSuccess() {
      ElMessage.success('删除成功')
      refetchData()
    }
  })
  const { mutate: retryMarkTask, isLoading: retryMarkTaskLoading } = useRetryMarkTask({
    onSuccess() {
      ElMessage.success('重试成功')
      getMarkTaskList()
    }
  })
  const updateDetail = (data: MarkTaskListItem, params: MarkResultListDetailParams) => {
    const index = listParams.list.findIndex((item) => item.id === params.id)
    listParams.list[index] = data
    setLoadingMap(params.id, false)
  }
  const { mutate: getMarkTaskDetail } = useGetMarkTaskDetail({
    onSuccess: updateDetail
  })
  const { mutate: refreshMarkTaskDetail } = useGetMarkTaskDetail({
    onSuccess: afterDecorator(
      updateDetail,
      beforeNextTick(() => ElMessage.success('更新成功'))
    ),
    onMutate: (param) => (setLoadingMap(param.id, true), undefined)
  })

  const gotoDetail = (row: MarkTaskListItem) => {
    const { type = MarkType.NlpSingle, id } = row
    const query = {
      name: row.name
    }
    switch (type) {
      case MarkType.NlpSingle:
        router.push({
          path: `/mark/result/${id}`,
          query
        })
        break
      case MarkType.Self:
        router.push({
          path: `/mark/self/${type}/${id}`,
          query
        })
        break
      case MarkType.Common:
      case MarkType.DownStream:
      default:
        router.push({
          path: `/mark/result/${type}/${id}`,
          query
        })
        break
    }
  }

  const { permissionObj, hasPermission } = usePermission(permissionList)
  const cacheMap = new WeakMap()
  const getPermission = (row: MarkTaskListItem) => {
    const result = cacheMap.get(row)
    if (result) return result
    const isManual = query.markTypeFilter === MarkTypeFilter.Manual
    const isObserver = row.isObserver
    const isQi = row.isQi
    const userPermissions = getUserPermission()
    const { REFRESH, RESTART, MARK, READ, LOCK, PUBLIC, DELETE, VIEW, OVERLAP, EDIT, ADDMARK } =
      permissionObj
    const { status, isMark, isOwner } = row
    const success = status !== MarkTaskStatus.error && status !== MarkTaskStatus.execute
    const error = status === MarkTaskStatus.error
    const roleId = businessList.value.find((item) => item.businessCode === row.businessCode)?.roleId
    const isBusAdmin = roleId === RoleId.BusAdmin || roleId === RoleId.BusSuperAdmin
    userPermissions.set(REFRESH)
    if (isMark) {
      if (success) {
        if (isManual) userPermissions.set(MARK)
      }
    }
    if (isOwner || isBusAdmin) {
      if (error) {
        userPermissions.set(RESTART)
      } else {
        if (isManual) {
          userPermissions.set(PUBLIC | LOCK | VIEW | EDIT)
          if (status === MarkTaskStatus.inMark) {
            userPermissions.set(ADDMARK)
          }
        }
        userPermissions.set(READ)
      }
      userPermissions.set(DELETE)
    }
    if (success) {
      if (isObserver) userPermissions.set(VIEW)
      if (isQi) userPermissions.set(VIEW | EDIT)
      if (userPermissions.has(READ) && row.repetitionRatio) {
        userPermissions.set(OVERLAP)
      }
    }
    const permission = hasPermission<
      | 'HASREFRESH'
      | 'HASRESTART'
      | 'HASMARK'
      | 'HASREAD'
      | 'HASLOCK'
      | 'HASPUBLIC'
      | 'HASDELETE'
      | 'HASVIEW'
      | 'HASOVERLAP'
      | 'HASEDIT'
      | 'ADDMARK'
    >(userPermissions.get())
    cacheMap.set(row, permission)
    return permission
  }
  const { debounce } = useTimerMap()
  const getDurationTime = (time: string) => {
    const duration = dayjs.duration(dayjs(time, 'X').diff(dayjs()))
    const hours = duration.hours()
    const minutes = duration.minutes()
    return {
      hours,
      minutes
    }
  }
  const tableColumn = ref<JSX.Element[]>([])
  const markTypeFilter = toRef(query, 'markTypeFilter')
  onMounted(() => renderTableColumn())
  const renderTableColumn = () => {
    const isManual = markTypeFilter.value === MarkTypeFilter.Manual
    tableColumn.value = generateTableList<MarkTaskListItem>([
      {
        prop: 'name',
        label: '任务名称',
        fixed: 'left',
        slots: (scope) => (
          <ElTooltip content={`${scope.row.name} ${scope.row.desc}`} placement="top">
            <span style={{ whiteSpace: 'nowrap' }}>{scope.row.name}</span>
          </ElTooltip>
        )
      },
      {
        prop: 'markCategory',
        label: '任务类别',
        width: '120'
      },
      // {
      //   prop: 'markTaskType',
      //   label: '任务类型'
      // },
      {
        prop: 'markScoreType',
        label: '评分类型'
      },
      // {
      //   prop: 'markLanguageType',
      //   label: '语言类型'
      // },
      {
        prop: 'detailCnt',
        label: '文本数目'
      },
      {
        prop: 'createTime',
        label: '创建时间',
        width: '110',
        slots: (scope) => dayjs(scope.row.createTime * 1000).format('YYYY-MM-DD')
      },
      {
        prop: 'ownerUname',
        label: '创建人'
      },
      {
        prop: 'status',
        label: '任务状态',
        width: '120px',
        slots: (scope) => {
          // const { HASREFRESH } = getPermission(scope.row)
          return (
            <>
              {statusTag[scope.row.status]}
              {scope.row.status === MarkTaskStatus['error'] && (
                <TooltipMessage content={scope.row.failReason} />
              )}
              {/* {HASREFRESH && (
                <ElButton link onClick={() => refreshMarkTaskDetail({ id: scope.row.id })}>
                  <ElIcon
                    class={loadingMap.value[scope.row.id] ? style['rotate-icon-container'] : ''}>
                    <Refresh />
                  </ElIcon>
                </ElButton>
              )} */}
            </>
          )
        },
        showOverflowTooltip: false
      },
      {
        prop: 'currentProgress',
        label: '当前进度',
        slots: (scope) => {
          const id = scope.row.id
          const beforeEnter = debounce(() => getMarkTaskDetail({ id }), id)
          const { hours: startHours, minutes: startMinutes } = getDurationTime(scope.row.startTime)
          const { hours: stopHours, minutes: stopMinutes } = getDurationTime(scope.row.stopTime)
          return (
            <div class="flex-just-start">
              <span>{finishRateTransform(+scope.row['currentProgress'])}</span>
              {scope.row.status === MarkTaskStatus.execute && (
                <ElPopover onBefore-enter={beforeEnter} placement="top" width="260px">
                  {{
                    reference: () => <ElButton icon={<QuestionFilled />} link />,
                    default: () => (
                      <ElDescriptions column={1}>
                        <ElDescriptionsItem label="任务生成时间">
                          已进行 {Math.abs(startHours)}小时{Math.abs(startMinutes)}分钟
                        </ElDescriptionsItem>
                        <ElDescriptionsItem label="剩余时间预估">
                          {stopHours < 0 || (stopHours === 0 && stopMinutes < 0)
                            ? '已结束'
                            : `剩余 ${stopHours}小时${stopMinutes}分钟`}
                        </ElDescriptionsItem>
                      </ElDescriptions>
                    )
                  }}
                </ElPopover>
              )}
            </div>
          )
        }
      },
      isManual
        ? {
            prop: 'lock',
            label: '锁定',
            slots: (scope) => {
              const { row } = scope
              const { HASLOCK } = getPermission(row)
              return (
                <ElSwitch
                  activeValue={LockType['lock']}
                  disabled={!HASLOCK}
                  inactiveValue={LockType['unLock']}
                  onChange={(lock) => marktaskUpdate({ id: scope.row.id, lock: lock as LockType })}
                  v-model={scope.row.lock}
                />
              )
            }
          }
        : null,
      {
        prop: 'operation',
        label: '操作',
        fixed: 'right',
        width: isManual ? '250' : '150',
        slots: (scope) => {
          const { row } = scope
          const { id, publishResult } = row
          const {
            HASEDIT,
            HASRESTART,
            HASMARK,
            HASREAD,
            HASPUBLIC,
            HASDELETE,
            HASVIEW,
            HASOVERLAP,
            HASADDMARK
          } = getPermission(row)
          const isPrivate = publishResult === PublishResult['private']
          return (
            <ElButtonGroup>
              {HASMARK && (
                <ElButton {...BUTTONTYPE} onClick={() => router.push(`/mark/rate/${id}`)}>
                  标注
                </ElButton>
              )}
              {HASREAD && (
                <ElButton {...BUTTONTYPE} onClick={() => gotoDetail(row)}>
                  查看结果
                </ElButton>
              )}
              {HASVIEW && (
                <ElDropdown
                  onCommand={(value: string) =>
                    router.push({ path: `/mark/rate/${id}`, query: { uname: value } })
                  }>
                  {{
                    default: () => (
                      <ElButton {...BUTTONTYPE}>
                        查看标注
                        <ElIcon>
                          <ArrowDown />
                        </ElIcon>
                      </ElButton>
                    ),
                    dropdown: () => (
                      <ElDropdownMenu>
                        {scope.row.annotators?.length ? (
                          scope.row.annotators.map((item) => (
                            <ElDropdownItem command={item} key={item}>
                              {item}
                            </ElDropdownItem>
                          ))
                        ) : (
                          <div style={{ padding: '5px 16px' }}>
                            <ElEmpty description="暂无数据" imageSize={120} />
                          </div>
                        )}
                      </ElDropdownMenu>
                    )
                  }}
                </ElDropdown>
              )}
              {HASOVERLAP && (
                <ElButton {...BUTTONTYPE} onClick={() => router.push(`/mark/result/overlap/${id}`)}>
                  交叉检验结果
                </ElButton>
              )}
              {HASRESTART && (
                <ElButton {...BUTTONTYPE} onClick={() => retryMarkTask({ id })}>
                  重新执行
                </ElButton>
              )}
              {(HASPUBLIC || HASDELETE || HASEDIT) && (
                <ElDropdown>
                  {{
                    default: () => (
                      <ElButton {...BUTTONTYPE}>
                        更多
                        <ElIcon>
                          <ArrowDown />
                        </ElIcon>
                      </ElButton>
                    ),
                    dropdown: () => (
                      <ElDropdownMenu>
                        {HASPUBLIC && (
                          <ElDropdownItem
                            onClick={() =>
                              marktaskUpdate({
                                id,
                                publishResult: isPrivate
                                  ? PublishResult['public']
                                  : PublishResult['private']
                              })
                            }>
                            {isPrivate ? '公开结果' : '私有结果'}
                          </ElDropdownItem>
                        )}
                        {row.lock === LockType['unLock'] &&
                          row.repetitionRatio === 0 &&
                          HASADDMARK && (
                            <ElDropdownItem onClick={() => redistributeMutate({ taskId: row.id })}>
                              重新分配
                            </ElDropdownItem>
                          )}
                        {HASDELETE && (
                          <ElDropdownItem
                            onClick={async () => {
                              const result = await ElMessageBox.confirm('确认删除？', '警告', {
                                confirmButtonText: '确认',
                                cancelButtonText: '取消',
                                type: 'warning'
                              }).catch(() => false)
                              if (!result) return
                              delMarkTask({ id })
                            }}>
                            删除任务
                          </ElDropdownItem>
                        )}
                        {HASEDIT && (
                          <ElDropdownItem onClick={() => openObserverModal(id)}>
                            编辑观察员
                          </ElDropdownItem>
                        )}
                        {HASEDIT && (
                          <ElDropdownItem onClick={() => openQiListModal(id)}>
                            编辑质检员
                          </ElDropdownItem>
                        )}
                        {HASADDMARK && (
                          <ElDropdownItem onClick={() => openMarkModal(id)}>
                            添加标注人员
                          </ElDropdownItem>
                        )}
                      </ElDropdownMenu>
                    )
                  }}
                </ElDropdown>
              )}
            </ElButtonGroup>
          )
        }
      }
    ])
  }
  const changeMarkTypeFilter = async () => {
    setTimeout(async () => {
      await nextTick()
      await refetchDataAsync()
      renderTableColumn()
    })
  }
  const isLoading = computed(
    () => retryMarkTaskLoading.value || listLoading.value || delMarkTaskLoading.value
  )

  return {
    isLoading,
    tableColumn,
    delMarkTask,
    retryMarkTask,
    listParams,
    refetchData,
    changeMarkTypeFilter,
    getMarkTaskList,
    tabsList
  }
}

export const useModal = () => {
  const { visible, setVisible, suspenseVisible } = useSuspense()
  const addType = ref<TaskFileType>(TaskFileType.create)
  const createTask = () => setVisible(true)
  return {
    visible,
    createTask,
    addType,
    suspenseVisible
  }
}
export const useSuspenseModal = () => {
  const { visible, setVisible, suspenseVisible } = useSuspense()
  const modalId = ref<number | undefined>()
  const openModal = (id: number) => {
    modalId.value = id
    setVisible(true)
  }
  return {
    visible,
    setVisible,
    suspenseVisible,
    modalId,
    openModal
  }
}
export const useSelection = () => {
  const sectionsRows: Ref<Array<{ id: number; type: number }>> = ref([])
  const router = useRouter()
  const compareTask = () => {
    const ids = sectionsRows.value.map((item) => item.id)
    const type = sectionsRows.value[0]?.type
    switch (type) {
      case MarkType.Self:
        router.push({
          path: `/mark/self/${type}`,
          query: {
            ids
          }
        })
        break
      case MarkType.NlpSingle:
      case MarkType.Common:
      case MarkType.DownStream:
      default:
        router.push({
          path: `/mark/compare/${type}`,
          query: {
            ids
          }
        })
        break
    }
  }
  const handleSelectionChange = (data: MarkTaskListItem[]) => {
    const sectionList = data.map((item) => ({
      id: item.id,
      type: item.type as number
    }))
    sectionsRows.value = sectionList
  }
  return {
    compareTask,
    sectionsRows,
    handleSelectionChange
  }
}
