.left-container {
  .flex(flex-start);
}

.right-container {
  .flex(flex-end);
  flex: 1;
  overflow: auto;
}

.text-area {
  height: 80%;
  :global(.el-textarea__inner) {
    height: 100%;
  }
}

.table {
  :global(.el-table__body) {
    height: 100%;
  }
  :global(.cell) {
    overflow: unset;
  }
}

.sticky {
  position: sticky;
  top: 8px;
}

.required {
  &::before {
    color: var(--el-color-danger);
    content: '*';
    margin-right: 4px;
  }
}
