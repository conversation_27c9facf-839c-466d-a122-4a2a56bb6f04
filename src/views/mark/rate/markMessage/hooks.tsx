import { MarkTagListItem } from '@/api/marktag/type'
import { afterDecorator } from '@znzt-fe/utils'
import { produce } from 'immer'
import { isEqual } from 'lodash-es'
import { VNodeRef } from 'vue'
import style from './index.module.less'
export const FontSize = 16
export const StartNodeFontSize = 12
export const StartNodePaddingTop = 4
export const StartNodeHeight = StartNodeFontSize + StartNodePaddingTop * 2
export const NodeMargin = 4
export const EndTagHeight = 4
type MarkList = Array<MarkListItem>
export interface MarkListItem {
  startId: string
  endId: string
  color: string
  zIndex: number
  tagName: string
}
export const useMarkList = () => {
  const markList = ref<MarkList>([])

  const setMarkList = (fallback: (val: MarkList) => void) => {
    const newVal = produce(markList.value, (draft) => {
      fallback(draft)
      draft.forEach((item) => {
        const zIndexArr = draft
          .filter((items) => {
            if (items === item) return // 遍历到自身
            /**
             * 两个string类型的number之间比较大小，必须先转换为数字
             * 此处隐式转换会转换为首字符的Unicode
             * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Less_than#description
             */
            if (+items.startId > +item.endId) return // 如果items在item右侧无覆盖则返回
            if (+items.endId < +item.startId) return // 如果items在item左侧无覆盖则返回
            if (!~items.zIndex) return // 如果items在覆盖的情况中，但没有层级则返回
            return true
          })
          .map((item) => item.zIndex)
        let zIndex = 0
        // eslint-disable-next-line no-constant-condition
        while (true) {
          if (!zIndexArr.includes(zIndex)) {
            item.zIndex = zIndex
            break
          }
          zIndex++
        }
      })
    })
    markList.value = newVal
  }
  return [readonly(markList), setMarkList] as const
}

export const useRef = () => {
  const refList = ref<Element[]>([])
  const setRef: VNodeRef = (el) => refList.value.push(el as Element)
  const changeRefsBgc = (markListItem: MarkListItem) => {
    const start = +markListItem.startId
    const end = +markListItem.endId
    for (let i = start; i <= end; i++) {
      const element = refList.value[i] as HTMLSpanElement
      element.style.background = markListItem.color
      element.style.color = markListItem.color === 'unset' ? 'unset' : 'white'
    }
  }
  const mouseLeave = (markListItem: MarkListItem) => {
    const newMarkListItem = produce(markListItem, (draft) => {
      draft.color = 'unset'
    })
    changeRefsBgc(newMarkListItem)
  }
  const mouseOver = changeRefsBgc
  return {
    mouseLeave,
    setRef,
    refList,
    mouseOver
  }
}

export const useSelection = (
  text: Ref<string>,
  checkTag: Ref<MarkTagListItem>,
  commentLabelContent: Ref<string>,
  onChange: (val: string) => void,
  disabled: Ref<boolean>
) => {
  invoke(async () => {
    await until(commentLabelContent).toBeTruthy()
    setMarkList((val) => {
      val.splice(0, val.length, ...JSON.parse(commentLabelContent.value))
    })
  })
  const [markList, setMarkList] = useMarkList()
  const { mouseOver, mouseLeave, setRef } = useRef()

  const innerText = () => {
    return text.value.split('').map((item, indexs) => {
      const lineList = markList.value.filter(
        (item) => +item.startId <= indexs && indexs <= +item.endId
      )
      /**
       * 为什么不使用length直接赋值，因为在上一行可能zindex为 0,1,2 ，到了下一行，zindex可能只剩下了 0，2，之前的1已经结束
       * 如果用length，则高度为2，但是实际占位是0, 2，高度为3
       */
      const lineCount = lineList.length ? Math.max(...lineList.map((item) => item.zIndex)) + 1 : 0
      const lineAst = lineList.map((item) => {
        // 开始节点
        return +item.startId === indexs ? (
          <span
            class={cx(style['start-tag'], !disabled.value && style['close-icon'])}
            onClick={() => deleteMarkTag(item)}
            onMouseleave={() => mouseLeave(item)}
            onMouseover={() => mouseOver(item)}
            style={{
              paddingBottom: StartNodePaddingTop + 'px',
              paddingTop: StartNodePaddingTop + 'px',
              fontSize: StartNodeFontSize + 'px',
              top: FontSize + NodeMargin + item.zIndex * (StartNodeHeight + NodeMargin) + 'px',
              backgroundColor: item.color
            }}>
            {item.tagName}
          </span>
        ) : (
          // 过程节点
          <span
            class={style['end-tag']}
            style={{
              height: EndTagHeight + 'px',
              top:
                FontSize +
                NodeMargin +
                item.zIndex * (StartNodeHeight + NodeMargin) +
                StartNodeHeight / 2 -
                EndTagHeight / 2 +
                'px',
              backgroundColor: item.color
            }}
          />
        )
      })
      let encodeItem
      try {
        encodeItem = encodeURIComponent(item)
      } catch {
        encodeItem = item
      }
      return (
        <span
          class={style['wrapper']}
          data-index={indexs + ''}
          key={indexs}
          style={{
            display: encodeItem === '%0A' ? 'block' : 'inline-block',
            height: FontSize + lineCount * (StartNodeHeight + NodeMargin) + 'px',
            fontWeight: lineAst.length ? 'bold' : 'normal'
          }}>
          <span data-index={indexs + ''} ref={setRef} style={{ display: 'inline-block' }}>
            {item}
          </span>
          {lineAst.map((item) => item)}
        </span>
      )
    })
  }
  const markListChange = () => onChange(JSON.stringify(markList.value))
  const addSelection = afterDecorator(
    () => {
      if (disabled.value) return
      if (!checkTag.value.color) return
      const selection = window.getSelection()
      if (!selection || selection.type === 'None') return
      const range = selection.getRangeAt(0)
      const cloneContents = range.cloneContents()
      const childNodes = cloneContents.childNodes
      // 选择空白
      if (!childNodes.length) return
      let startNode: Node = childNodes[0]
      // 获取最底部的节点
      while (startNode?.childNodes?.[0]) {
        startNode = startNode.childNodes[0]
      }
      // 如果节点为文本节点，且有文字内容（覆盖了文本，文本为选中状态），如果不满足，则代表当前节点在界面中现实未选择到，所以放弃当前节点
      if (startNode.nodeType === Node.TEXT_NODE && !!startNode.textContent) {
        startNode = range.startContainer
      } else {
        startNode = childNodes[1]
      }
      startNode = startNode.nodeType === Node.TEXT_NODE ? startNode.parentElement! : startNode

      const endIndex = childNodes.length - 1
      let endNode: Node = childNodes[endIndex]
      // 获取最底部的节点
      while (endNode?.childNodes?.[0]) {
        endNode = endNode.childNodes[0]
      }
      // 如果节点为文本节点，且有文字内容（覆盖了文本，文本为选中状态），如果不满足，则代表当前节点在界面中现实未选择到，所以放弃当前节点
      if (endNode.nodeType === Node.TEXT_NODE && !!endNode.textContent) {
        endNode = range.endContainer
      } else {
        endNode = childNodes[endIndex - 1]
      }
      endNode = endNode.nodeType === Node.TEXT_NODE ? endNode.parentElement! : endNode
      const startId = (startNode as HTMLElement).getAttribute('data-index') as string
      const endId = (endNode as HTMLElement).getAttribute('data-index') as string
      setMarkList((val) => {
        val.push({
          startId,
          endId,
          color: checkTag.value.color,
          tagName: checkTag.value.name,
          zIndex: -1
        })
      })
      window.getSelection()?.empty()
      return true
    },
    (executeSuccess: boolean) => executeSuccess && markListChange()
  )

  const deleteMarkTag = afterDecorator(
    (markListItem: MarkListItem) => {
      if (disabled.value) return
      setMarkList((val) => {
        const index = val.findIndex((item) => isEqual(item, markListItem))
        if (!~index) return
        mouseLeave(markListItem)
        val.splice(index, 1)
      })
      return true
    },
    (executeSuccess: boolean) => executeSuccess && markListChange()
  )
  return {
    addSelection,
    innerText,
    setMarkList
  }
}
