import { FontSize, useSelection } from './hooks'
import { MarkTagListItem } from '@/api/marktag/type'
export default defineComponent({
  props: {
    text: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    checkTag: {
      type: Object as PropType<MarkTagListItem>,
      default: () => ({})
    },
    commentLabelContent: {
      type: String,
      default: ''
    }
  },
  emits: ['change'],
  setup(props, { emit }) {
    const onChange = (val: string) => emit('change', val)
    const { text, checkTag, commentLabelContent, disabled } = toRefs(props)
    const { addSelection, innerText } = useSelection(
      text,
      checkTag,
      commentLabelContent,
      onChange,
      disabled
    )
    return () => (
      <span
        onMouseup={() => checkTag.value.color && addSelection()}
        style={{ whiteSpace: 'pre-line', fontSize: FontSize + 'px' }}>
        {innerText().map((item) => item)}
      </span>
    )
  }
})
