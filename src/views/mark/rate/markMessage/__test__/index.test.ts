import { describe, it } from 'vitest'
import { useMarkList } from '../hooks'
import { expect } from 'vitest'
describe('useMarkList', () => {
  it('test useMarkList', () => {
    const [markList, setMarkList] = useMarkList()
    setMarkList((val) => {
      val.push({
        startId: '3',
        endId: '5',
        zIndex: -1,
        color: 'skyblue',
        tagName: ''
      })
    })
    expect(markList.value).toEqual([
      {
        startId: '3',
        endId: '5',
        zIndex: 0,
        color: 'skyblue',
        tagName: ''
      }
    ])
    setMarkList((val) => {
      val.push({
        startId: '4',
        endId: '6',
        zIndex: -1,
        color: 'skyblue',
        tagName: ''
      })
    })
    expect(markList.value).toEqual([
      {
        startId: '3',
        endId: '5',
        zIndex: 0,
        color: 'skyblue',
        tagName: ''
      },
      {
        startId: '4',
        endId: '6',
        zIndex: 1,
        color: 'skyblue',
        tagName: ''
      }
    ])

    setMarkList((val) => {
      val.push({
        startId: '10',
        endId: '20',
        zIndex: -1,
        color: 'skyblue',
        tagName: ''
      })
    })

    expect(markList.value).toEqual([
      {
        startId: '3',
        endId: '5',
        zIndex: 0,
        color: 'skyblue',
        tagName: ''
      },
      {
        startId: '4',
        endId: '6',
        zIndex: 1,
        color: 'skyblue',
        tagName: ''
      },
      {
        startId: '10',
        endId: '20',
        zIndex: 0,
        color: 'skyblue',
        tagName: ''
      }
    ])
  })
})
