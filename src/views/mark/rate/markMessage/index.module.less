.common-tag {
  position: absolute;
  left: 0;
}
.start-tag {
  .common-tag();
  color: white;
  padding-left: 3px;
  padding-right: 3px;
  box-sizing: border-box;
  font-weight: bold;
  border-radius: 3px;
  user-select: none;
  white-space: nowrap;
  line-height: 1;
  z-index: 2;
  cursor: pointer;
  &:hover {
    z-index: 3;
  }
}
.close-icon {
  &:hover {
    z-index: 3;
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: -6px;
      transform: translate(-50%, -50%) rotate(135deg);
      width: 8px;
      height: 2px;
      background-color: #606266;
    }

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: -6px;
      transform: translate(-50%, -50%) rotate(45deg);
      width: 8px;
      height: 2px;
      background-color: #606266;
    }
  }
}
.end-tag {
  .common-tag();
  z-index: 1;
  width: 100%;
}

.wrapper {
  line-height: 1;
  white-space: pre;
  position: relative;
  ::selection {
    background: #ffe184;
  }
}
