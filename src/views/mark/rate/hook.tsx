import { getMarkTagList } from '@/api/marktag'
import { MarkTagListItem } from '@/api/marktag/type'
import {
  useGetMarkTaskDetail,
  useMarktaskdetailList,
  useResultUpdate,
  useUpdateGsb
} from '@/api/marktask'
import {
  AnnotationGranularity,
  AnnotationRole,
  Caption,
  GsbListItem,
  LockType,
  MarkResultListDetailRet,
  MarkTaskDetailType,
  MarkTaskListItem,
  MarktaskdetailItem,
  MarktaskdetailRet,
  MultiChoice,
  NumberConfigType,
  PromptListItem,
  PublishResult,
  Question,
  QuestionDisplayMode,
  QuestionType,
  ResultType,
  ResultUpdateParams
} from '@/api/marktask/type'
import { RoleId } from '@/api/user/type'
import QuillEditor from '@/components/quill-editor'
import { totalHeight } from '@/components/search-container'
import useUserStore from '@/store/user'
import {
  arrayToNumber,
  getInverseValue,
  numberToArray,
  numberToString,
  stringToNumber
} from '@/utils'
import { useQuery } from '@znzt-fe/axios'
import type { ValueOf } from '@znzt-fe/declare'
import { useCheckBoxSingle, useElModal, useRouteId, useTableScroll } from '@znzt-fe/hooks'
import { generateTableList, type GenerateTableListParams } from '@znzt-fe/utils'
import {
  ElButton,
  ElCheckbox,
  ElCheckboxGroup,
  ElInput,
  ElInputNumber,
  ElMessageBox,
  ElOption,
  ElRadio,
  ElRadioGroup,
  ElRate,
  ElSelect,
  ElTag,
  RenderRowData,
  TableColumnCtx
} from 'element-plus'
import { cloneDeep, flow } from 'lodash-es'
import { StyleValue } from 'vue'
import { LocationQueryValue } from 'vue-router'
import { JSX } from 'vue/jsx-runtime'
import { ScoreType, gsbOption, markTaskScoreTypeToggle } from '../util'
import AssistantMessage from './assistantMessage'
import ChatMessages from './chatMessages'
import style from './index.module.less'
import MarkMessage from './markMessage'
import UserMessage from './userMessage'
const AudioArrReg = [/(<player>[\s\S]*?<\/player>)/g]
export const AudioReg = [/(https:)(.*)(mp3|wav|ogg|m4a|flac|aac)(.*)/]
export const VideoReg = [/^(https|http).*\.(mp4|mov|wmv|avi|flv|mkv|webm)(.*)/]
const tableHeaderHeight = 40
const paginationHeight = 40
const cellPY = 8 * 2
export const getPureText = (text: string) => text.replace(AudioReg[0], '')
export const getSpeechText = (text: string) =>
  text.split(/(<player>[\s\S]*?<\/player>)/).filter(Boolean)
export const getSpeechTag = (text: string) => {
  const audioArrUrl = AudioArrReg.reduce<string[] | null>((pre, now) => {
    const result = text.match(now)
    if (result) {
      pre = result.map((item) => item.replace('<player>', '').replace('</player>', ''))
    }
    return pre
  }, null)

  const audioUrl = AudioReg.reduce<string | null>((pre, now) => {
    const result = text.match(now)
    pre = result ? result[0] : pre
    return pre
  }, null)
  const videoUrl = VideoReg.reduce<string | null>((pre, now) => {
    const result = text.match(now)
    pre = result ? result[0] : pre
    return pre
  }, null)
  switch (true) {
    case !!audioArrUrl:
      return null
    case !!audioUrl:
      return (
        <div class="mt-2" style={{ overflowX: 'auto' }}>
          <audio class="w-full" controls src={audioUrl} />
        </div>
      )
    case !!videoUrl:
      return (
        <div style={{ overflowX: 'auto' }}>
          <video class="w-full" controls src={videoUrl} />
        </div>
      )
    default:
      return null
  }
}
export const parseMultipleChoice = (value: string | undefined) => {
  if (typeof value === 'string') {
    return value === '' ? undefined : +value
  }
  return value
}
export const useTable = (
  markTaskDetail: Ref<MarkResultListDetailRet | undefined>,
  checkTag: ComputedRef<MarkTagListItem | undefined>
) => {
  const { sortModalVisible, openSortModal, sortApiResultList, sortScore } = useSortModal()
  const {
    audioVisible,
    openAudioModal,
    questionCaption,
    audioSrc,
    updateQuestionResultFn,
    audioId
  } = useAudioModal()
  const { name, businessList } = storeToRefs(useUserStore())
  const isBusAdmin = computed(() => {
    const busInfo = businessList.value.find(
      (item) => item.businessCode === markTaskDetail.value?.businessCode
    )
    return busInfo?.roleId === RoleId.BusAdmin || busInfo?.roleId === RoleId.BusSuperAdmin
  })

  const isLock = computed(() => markTaskDetail.value?.lock === LockType['lock'])
  const isDisabled = computed(() => {
    const isSessionDisabled = {
      mark: false,
      qi: false
    }
    if (isLock.value) {
      isSessionDisabled.mark = true
      isSessionDisabled.qi = true
      return isSessionDisabled
    }
    if (markTaskDetail.value?.scoreType === ScoreType['MarkSession']) {
      if (isBusAdmin.value || markTaskDetail.value?.ownerUname === name.value) {
        return isSessionDisabled
      }
      if (uname) {
        isSessionDisabled.mark = true
      }
      if (!markTaskDetail.value?.isQi) {
        isSessionDisabled.qi = true
      }
      return isSessionDisabled
    } else {
      /** 如果是查看他人标注 */
      if (
        !!uname &&
        /** 管理员可以修改 */
        !isBusAdmin.value &&
        /** 质检员可以修改 */
        !markTaskDetail.value?.isQi &&
        /** 自己可以修改 */
        markTaskDetail.value?.ownerUname !== name.value
      ) {
        isSessionDisabled.mark = true
        isSessionDisabled.qi = true
      }
      return isSessionDisabled
    }
  })
  const isHistory = computed(
    () => isBusAdmin.value || markTaskDetail.value?.ownerUname === name.value
  )
  const listParams = reactive<{
    total: number
    allList: MarktaskdetailRet['list']
    filterList: MarktaskdetailRet['list']
    pageCount: number
    pageNum: number
    gsbList: GsbListItem[][]
    list: MarktaskdetailItem[]
    pageSize: number
    pageSizes: number[]
    questions: Question[]
  }>({
    total: 0,
    allList: [],
    filterList: [],
    pageCount: 0,
    pageNum: 1,
    list: [],
    gsbList: [],
    pageSize: 1,
    pageSizes: [1, 2, 5, 10],
    questions: []
  })
  const markTaskId = useRouteId()
  const filterCheck = ref(false)
  watch(filterCheck, () => listHandle())
  const route = useRoute()
  /** 是否查看他人标注 */
  const uname = route.query.uname as LocationQueryValue
  onMounted(() => {
    uname
      ? marktaskdetailListMutate({ markTaskId, ownerUname: uname })
      : marktaskdetailListMutate({ markTaskId })
  })

  const { mutate: updateGsbMutate } = useUpdateGsb({
    onSuccess(_, params) {
      const flatGsbList: GsbListItem[] = listParams.gsbList.reduce(
        (pre, now) => pre.concat(...now),
        []
      )
      const gsbItem = flatGsbList.find(
        (item) =>
          item.anotherDetailId === params.anotherDetailId && item.detailId === params.detailId
      )
      if (!gsbItem) return
      gsbItem.gsb = params.gsb
    }
  })
  const getGsbListItem = (id: number) => {
    const index = listParams.gsbList.findIndex((item) =>
      item.find((items) => items.detailId === id || items.anotherDetailId === id)
    )
    if (!~index) return
    return listParams.gsbList[index]
  }
  const { tableRef, scrollInit } = useTableScroll()
  const getSlotElementSlots = (
    row: MarktaskdetailItem,
    item: Question,
    showName: boolean,
    type: keyof typeof isDisabled.value
  ) => {
    const updateQuestionResult = (value: ValueOf<ResultType>) => {
      updateResult({
        key: 'question',
        value: {
          [item.id + '']: value
        },
        markTaskDetailId: row.id
      })
    }
    const updateQuestionMultipleChoice = (
      value: string | undefined,
      arr: MultiChoice[],
      index: number
    ) => {
      arr = arr || []
      const detail = arr.find((item) => item.index === index)
      if (detail) detail.score = value || '0'
      else arr.push({ index, score: value || '0' })
      updateResult({
        key: 'question',
        value: {
          [item.id + '']: arr
        },
        markTaskDetailId: row.id
      })
    }
    const groupConfig = {
      disabled: isDisabled.value[type],
      class: 'flex-wrap'
    }
    const slotElementSlots = {
      [QuestionType.single]: () => (
        <ElRadioGroup
          {...groupConfig}
          modelValue={arrayToNumber(row.result[item.id] as number[]) ?? ''}
          onChange={flow([numberToArray, updateQuestionResult])}>
          {item.options.map((option) => (
            <ElRadio key={item.id} label={option.name} value={option.id} />
          ))}
        </ElRadioGroup>
      ),
      [QuestionType.multiple]: () => {
        const val = row.result[item.id] as number[]
        const arr = Object.entries(
          item.options.reduce<Record<string, { name: string; id: number }[]>>((pre, now) => {
            const nameArr = now.name
              .replaceAll('//', '$$$')
              .split('/')
              .map((item) => item.replaceAll('$$', '/'))
            const key = nameArr.shift() as keyof typeof pre
            if (!pre[key]) {
              pre[key] = []
            }
            pre[key].push({ name: nameArr.join(','), id: now.id })
            return pre
          }, {})
        )
        const isNormal = arr.every((item) => item[1].length === 1)
        return isNormal ? (
          <ElCheckboxGroup
            {...groupConfig}
            modelValue={val}
            onChange={(value) => updateQuestionResult(value as number[])}>
            {item.options.map((option) => (
              <ElCheckbox key={item.id} value={option.id}>
                {option.name}
              </ElCheckbox>
            ))}
          </ElCheckboxGroup>
        ) : (
          arr.map((itemArr) => (
            <div key={itemArr[0]}>
              <div>
                {itemArr[0]}
                {val.some((item) => itemArr[1].map((item) => item.id).includes(item)) ? (
                  <ElTag class="ml-2" disableTransitions>
                    已标注
                  </ElTag>
                ) : (
                  <ElTag class="ml-2" disableTransitions>
                    未标注
                  </ElTag>
                )}
              </div>
              {itemArr[1].map((items) => {
                return (
                  <ElCheckbox
                    key={items.id}
                    {...groupConfig}
                    modelValue={val.includes(items.id)}
                    onChange={(v) => {
                      v
                        ? val.push(items.id)
                        : val.splice(
                            val.findIndex((item) => item === items.id),
                            1
                          )
                      updateQuestionResult(val)
                    }}
                    value={items.id}>
                    {items.name}
                  </ElCheckbox>
                )
              })}
            </div>
          ))
        )
      },
      [QuestionType.number]: () => {
        const { type, max, min, percision } = item.numberConfig
        const numberConfigElement = {
          [NumberConfigType.input]: () => (
            <ElInputNumber
              class="input-number"
              controls={false}
              disabled={groupConfig.disabled}
              max={max}
              min={min}
              modelValue={stringToNumber(row.result[item.id] as string)}
              onChange={flow([numberToString, updateQuestionResult])}
              precision={percision}
            />
          ),
          [NumberConfigType.select]: () => {
            const options = Array.from({ length: max - min + 1 }, (_, i) => i + min + '')
            return (
              <ElSelect
                disabled={groupConfig.disabled}
                modelValue={row.result[item.id]}
                onChange={updateQuestionResult}>
                {options.map((option) => (
                  <ElOption key={option} label={option} value={option} />
                ))}
              </ElSelect>
            )
          }
        }
        return numberConfigElement[type]()
      },
      [QuestionType.subjective]: () => {
        const { class: className, disabled } = groupConfig
        return (
          <ElInput
            class={cx(className, style['text-area'])}
            disabled={disabled}
            onChange={updateQuestionResult}
            type="textarea"
            v-model={row.result[item.id]}
          />
        )
      },
      [QuestionType.multipleChoice]: () => {
        const { percision, min } = item.numberConfig
        let { max } = item.numberConfig
        if (!max && !min) max = 100
        return (
          <>
            {item.options.map((option, index) => {
              return (
                <div class="flex items-center" key={item.id}>
                  <ElInputNumber
                    class="shrink-0"
                    controls={false}
                    disabled={groupConfig.disabled}
                    max={max}
                    min={min}
                    modelValue={parseMultipleChoice(
                      (row.result[item.id] as MultiChoice[])?.find(
                        (item) => option.id === item.index
                      )?.score
                    )}
                    onChange={(v) =>
                      updateQuestionMultipleChoice(
                        v?.toString(),
                        row.result[item.id] as MultiChoice[],
                        index + 1
                      )
                    }
                    precision={percision}
                    style={{
                      width: '60px',
                      marginRight: '6px',
                      marginTop: index === 0 ? '0px' : '6px'
                    }}
                  />
                  <span class="grow overflow-auto">{option.name}</span>
                </div>
              )
            })}
          </>
        )
      },
      [QuestionType.captions]: () => (
        <ElButton
          onClick={() =>
            openAudioModal(updateQuestionResult, row.result[item.id], row.apiUser, item.id)
          }
          type="primary">
          标注字幕
        </ElButton>
      )
    }
    return (
      <div>
        {showName && <div class={item.mustAnswer && style['required']}>{item.name}</div>}
        {slotElementSlots[item.type]?.()}
      </div>
    )
  }
  const { mutate: marktaskdetailListMutate, data: marktaskdetailListData } = useMarktaskdetailList()
  const combination = ref(false)
  invoke(async () => {
    await until(markTaskDetail).toBeTruthy()
    await until(marktaskdetailListData).toBeTruthy()
    // const newList = cloneDeep(marktaskdetailListData.value!)
    // newList.list.forEach((item) => {
    //   item.promptList.forEach((item) =>
    //     item.detailList.forEach((item) => (item.apiUser = location.origin + '/audio.webm'))
    //   )
    // })
    if (markTaskDetail.value!.questionDisplayMode === QuestionDisplayMode['Vertical'])
      combination.value = true
    marktaskdetailListHandle(marktaskdetailListData.value!)
    // marktaskdetailListHandle(newList)
  })
  const marktaskdetailListHandle = (res: MarktaskdetailRet) => {
    const {
      list,
      questions,
      isGsb,
      isNil,
      isSort,
      score,
      isWritingEval,
      isCaptions,
      hasSession,
      extDispTitles
    } = res
    sortScore.value = score
    listParams.allList = cloneDeep(list)
    listParams.questions = questions
    listHandle()
    let questionTableColumnList: GenerateTableListParams<MarktaskdetailItem>
    console.log(questions)
    if (!combination.value) {
      questionTableColumnList = questions
        ?.filter((item) => item.annotationGranularity !== AnnotationGranularity['Captions'])
        .map((item) => ({
          prop: item.id + '',
          label: item.name,
          header: () =>
            item.mustAnswer ? <div class={style['required']}>{item.name}</div> : item.name,
          slots: (scope) => (
            <div style={{ position: 'sticky', top: '8px' }}>
              {getSlotElementSlots(
                scope.row,
                item,
                false,
                item.annotationRole === AnnotationRole['PolyQualityInspector'] ? 'qi' : 'mark'
              )}
            </div>
          )
        }))
    } else {
      const annotatorsList: Question[] = []
      const annotatorsSessionList: Question[] = []
      const qiList: Question[] = []
      const qiSessionList: Question[] = []
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      let _never: never
      questions.forEach((item) => {
        switch (item.annotationRole) {
          case AnnotationRole['Annotationer']:
            switch (item.annotationGranularity) {
              case AnnotationGranularity['Assistant']:
                annotatorsList.push(item)
                break
              case AnnotationGranularity['Session']:
                annotatorsSessionList.push(item)
                break
              case AnnotationGranularity['Captions']:
                break
              default:
                _never = item.annotationGranularity
            }
            break
          case AnnotationRole['PolyQualityInspector']:
            switch (item.annotationGranularity) {
              case AnnotationGranularity['Assistant']:
                qiList.push(item)
                break
              case AnnotationGranularity['Session']:
                qiSessionList.push(item)
                break
              case AnnotationGranularity['Captions']:
                break
              default:
                _never = item.annotationGranularity
            }
            break
          default:
            annotatorsList.push(item)
        }
      })
      let detailArr: {
        question: Question[]
        name: string
        type: keyof typeof isDisabled.value
        prop: 'mark' | 'qi' | 'sessionMark' | 'sessionQi'
      }[] = [
        {
          question: annotatorsList,
          name: '标注',
          type: 'mark',
          prop: 'mark'
        },
        {
          question: qiList,
          name: '质检',
          type: 'qi',
          prop: 'qi'
        },
        {
          question: annotatorsSessionList,
          name: 'session标注',
          type: 'mark',
          prop: 'sessionMark'
        },
        {
          question: qiSessionList,
          name: 'session质检',
          type: 'qi',
          prop: 'sessionQi'
        }
      ]
      if (
        markTaskDetail.value?.publishResult === PublishResult['private'] &&
        !isDisabled.value.mark &&
        isDisabled.value.qi
      ) {
        detailArr = detailArr.filter((item) => item.type === 'mark')
      }
      questionTableColumnList = detailArr
        .filter((item) => !!item.question.length)
        .map((item) =>
          questionTableColumnListHandle(item.question, item.name, item.type, item.prop)
        )
    }
    const commonTableColumnList: GenerateTableListParams<MarktaskdetailItem> = [
      markTaskDetail.value?.scoreType !== ScoreType['MarkSession']
        ? {
            prop: 'id',
            label: 'id',
            resizable: true,
            width: '60',
            slots: (scope) => (
              <div style={{ position: 'sticky', top: '8px' }}>
                {scope.row.id}
                <div style={{ marginTop: '4px' }}>{scope.row.sid}</div>
              </div>
            )
          }
        : null,
      !hasSession
        ? null
        : {
            prop: 'session',
            label: 'session',
            resizable: true,
            width: '200',
            slots: (scope) => {
              const session = scope.row.session
              if (!session) return
              const regExpMatchArray = session.match(/\(https?:\/\/[^\s)]+\)$/)
              const imageUrl = regExpMatchArray?.[0].slice(1, -1)
              const text = session.slice(0, regExpMatchArray?.index)
              return (
                <div style={{ position: 'sticky', top: '8px' }}>
                  {imageUrl && <img src={imageUrl} />}
                  {text}
                </div>
              )
            }
          },
      ...(extDispTitles?.map((item) => ({
        prop: item,
        label: item,
        resizable: true,
        width: '100',
        slots: (scope: RenderRowData<MarktaskdetailItem>) => (
          <div style={{ position: 'sticky', top: '8px' }}>
            {scope.row.ext.extList.find((extDetail) => extDetail.t === item)?.v}
          </div>
        )
      })) || []),
      {
        prop: 'finish',
        label: '完成状态',
        resizable: true,
        width: '90',
        slots: (scope) => (
          <div style={{ position: 'sticky', top: '8px' }}>
            {finishStatus(scope.row) ? (
              <ElTag disableTransitions type="success">
                已完成
              </ElTag>
            ) : (
              <ElTag disableTransitions type="danger">
                未完成
              </ElTag>
            )}
          </div>
        )
      },
      {
        prop: 'apiUser',
        label: isWritingEval
          ? 'query信息'
          : markTaskDetail.value?.scoreType === ScoreType['MarkSession']
          ? '角色信息'
          : 'user轮文本',
        className: 'text-top',
        resizable: true,
        slots: (scope) => {
          const { id, apiUser, writingEvalQuery, image, inputImage } = scope.row
          const innerText = isWritingEval ? writingEvalQuery : apiUser
          let style: StyleValue = {}
          if (markTaskDetail.value?.scoreType === ScoreType['MarkSession']) {
            style = {
              maxHeight: `calc( 100vh -  ${totalHeight}px - ${tableHeaderHeight}px - ${paginationHeight}px - ${cellPY}px)`,
              overflow: 'auto'
            }
          }
          return (
            <div
              style={{
                position: 'sticky',
                top: '8px',
                minHeight: '100px',
                overflow: 'auto',
                ...style
              }}>
              {scope.row.type == MarkTaskDetailType['single'] ? (
                <UserMessage key={id}>
                  {getSpeechText(innerText).map((item) =>
                    item.startsWith('<player>') ? (
                      <div class="mt-2" key={item} style={{ overflowX: 'auto' }}>
                        <audio
                          class="w-full"
                          controls
                          src={item.replace('<player>', '').replace('</player>', '')}
                        />
                      </div>
                    ) : (
                      getPureText(item)
                    )
                  )}
                  {getSpeechTag(innerText)}
                </UserMessage>
              ) : (
                <ChatMessages key={id} messages={innerText} />
              )}
              {image && <img src={image} style={{ width: '100%' }} />}
              {inputImage && <img src={inputImage} style={{ width: '100%' }} />}
            </div>
          )
        }
      },
      markTaskDetail.value?.scoreType === ScoreType['MarkSession']
        ? {
            prop: 'id',
            label: 'id',
            resizable: true,
            width: '60',
            slots: (scope) => (
              <div style={{ position: 'sticky', top: '8px' }}>
                {scope.row.id}
                <div style={{ marginTop: '4px' }}>{scope.row.sid}</div>
              </div>
            )
          }
        : null,
      isCaptions
        ? null
        : {
            prop: 'apiResult',
            label:
              markTaskDetail.value?.scoreType === ScoreType['MarkSession'] ? '对话内容' : '回答',
            resizable: true,
            minWidth: 200,
            slots: (scope) => {
              return (
                <div style={{ position: 'sticky', top: '8px' }}>
                  {markTaskDetail.value?.isMarkTag ? (
                    <MarkMessage
                      checkTag={checkTag.value}
                      commentLabelContent={scope.row.commentLabelContent}
                      disabled={isDisabled.value.mark}
                      key={scope.row.id}
                      onChange={(commentLabelContent) =>
                        updateResult({
                          key: 'commentLabelContent',
                          value: commentLabelContent,
                          markTaskDetailId: scope.row.id
                        })
                      }
                      text={scope.row.apiResult}
                    />
                  ) : !scope.row.apiResult.match(AudioArrReg[0]) ? (
                    <>
                      <AssistantMessage
                        key={scope.row.id}
                        text={getPureText(scope.row.apiResult)}
                      />
                      {getSpeechTag(scope.row.apiResult)}
                    </>
                  ) : (
                    getSpeechText(scope.row.apiResult).map((item) =>
                      item.startsWith('<player>') ? (
                        <div class="mt-2" key={item} style={{ overflowX: 'auto' }}>
                          <audio
                            class="w-full"
                            controls
                            src={item.replace('<player>', '').replace('</player>', '')}
                          />
                        </div>
                      ) : (
                        item
                      )
                    )
                  )}
                  <div class="flex-just-end ">
                    字数：
                    {scope.row.apiResult.length}
                  </div>
                  {(markTaskDetail.value?.isShowSource || isLock.value) && (
                    <div class="flex-just-end ">模型：{scope.row.source}</div>
                  )}
                </div>
              )
            }
          },
      isGsb
        ? {
            prop: 'gsb',
            label: 'GSB评分',
            resizable: true,
            width: '250',
            slots: (scope) => {
              const gsbListItem = getGsbListItem(scope.row.id)
              if (!gsbListItem) return
              return gsbListItem.map(({ detailId, anotherDetailId, gsb, markTaskId }) => (
                <div key={anotherDetailId + detailId} style={{ position: 'sticky', top: '8px' }}>
                  <div>
                    {detailId} vs {anotherDetailId}
                  </div>
                  <div>
                    <ElRadioGroup
                      class="flex-wrap"
                      disabled={isDisabled.value.mark}
                      modelValue={gsb ?? ''}
                      onChange={(gsb) =>
                        updateGsbMutate({
                          gsb: Number(gsb),
                          detailId,
                          anotherDetailId,
                          markTaskId
                        })
                      }>
                      {gsbOption.map((option) => (
                        <ElRadio key={option.value} value={option.value}>
                          {option.label}
                        </ElRadio>
                      ))}
                    </ElRadioGroup>
                  </div>
                </div>
              ))
            }
          }
        : isNil || isCaptions || markTaskDetail.value?.scoreType === ScoreType['MarkSession']
        ? null
        : isWritingEval
        ? {
            prop: 'score',
            label: '得分',
            resizable: true,
            width: '140',
            slots: (scope) => {
              const add = (scope.row.result[1] as MultiChoice[]).reduce(
                (pre, now) => (pre += +(now.score || 0)),
                0
              )
              const minus = (scope.row.result[2] as MultiChoice[]).reduce(
                (pre, now) => (pre += +(now.score || 0)),
                0
              )
              return <div style={{ position: 'sticky', top: '8px' }}>{63 + add - minus}</div>
            }
          }
        : isSort
        ? {
            prop: 'sort',
            label: '排序',
            resizable: true,
            width: '140',
            slots: () => (
              <ElButton
                disabled={isDisabled.value.mark}
                onClick={() => openSortModal(listParams.list)}
                style={{ position: 'sticky', top: '8px' }}>
                排序
              </ElButton>
            )
          }
        : {
            prop: 'rate',
            label: '评分',
            resizable: true,
            width: '140',
            slots: (scope) => (
              <ElRate
                allowHalf={markTaskDetail.value?.halfScore}
                disabled={isDisabled.value.mark}
                max={markTaskScoreTypeToggle(scope.row.markScoreType)}
                modelValue={scope.row.score}
                onChange={(score) =>
                  updateResult({ key: 'score', value: score, markTaskDetailId: scope.row.id })
                }
                style={{ position: 'sticky', top: '8px' }}
              />
            )
          },
      {
        prop: 'remark',
        label: '备注',
        resizable: true,
        width: '100',
        slots: (scope) => (
          <ElInput
            class={cx(style['sticky'], style['text-area'])}
            disabled={isDisabled.value.mark}
            onChange={(remark) =>
              updateResult({ key: 'remark', value: remark, markTaskDetailId: scope.row.id })
            }
            type="textarea"
            v-model={scope.row.remark}
          />
        )
      }
    ]
    const finalTableColumnList: GenerateTableListParams<MarktaskdetailItem> = [
      ...commonTableColumnList,
      ...questionTableColumnList
    ]
    if (isWritingEval) {
      if (!combination.value) {
        const index = finalTableColumnList.findIndex((item) => item?.label === '诉求满足度')
        const index1 = finalTableColumnList.findIndex((item) => item?.prop === 'remark')
        const index2 = finalTableColumnList.findIndex((item) => item?.prop === 'score')
        const index3 = finalTableColumnList.findIndex((item) => item?.label === '扣分点')
        const index4 = finalTableColumnList.findIndex((item) => item?.label === '加分点')
        // 正确的顺序
        const spliceIndexArr = [index, index1, index2, index3, index4]
        const start = Math.min(...spliceIndexArr)
        const symbolKey = Symbol()
        const sortTableColumnList = finalTableColumnList
          .splice(start, spliceIndexArr.length)
          .map((item, index) => ({ ...item, [symbolKey]: index + start }))
          .sort(
            (a, b) => spliceIndexArr.indexOf(a[symbolKey]) - spliceIndexArr.indexOf(b[symbolKey])
          )
          .map((item) => {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { [symbolKey]: symbolValue, ...rest } = item
            return { ...rest }
          })
        finalTableColumnList.splice(start, 0, ...sortTableColumnList)
      }
    }
    tableColumn.value = generateTableList<MarktaskdetailItem>(finalTableColumnList)
  }
  const questionTableColumnListHandle = (
    questions: Question[],
    name: string,
    type: keyof typeof isDisabled.value,
    prop: 'mark' | 'qi' | 'sessionMark' | 'sessionQi'
  ) => {
    const detail = {
      prop,
      header: () => <div>{name}</div>,
      slots: () => {}
    } as GenerateTableListParams<MarktaskdetailItem>[number]
    detail!.slots = (scope) => {
      let style: StyleValue = {}
      if (scope.column.property === 'sessionMark' || scope.column.property === 'sessionQi') {
        style = {
          maxHeight: `calc( 100vh -  ${totalHeight}px - ${tableHeaderHeight}px - ${paginationHeight}px - ${cellPY}px)`,
          overflow: 'auto'
        }
      }
      return (
        <div style={{ position: 'sticky', top: '8px', ...style }}>
          {questions.map((item) => (
            <div key={item.id}>{getSlotElementSlots(scope.row, item, true, type)}</div>
          ))}
        </div>
      )
    }
    return detail
  }
  const changeMarktaskdetailListHandle = () => {
    if (!marktaskdetailListData.value) return
    marktaskdetailListHandle(marktaskdetailListData.value)
  }
  const finishStatus = (row: MarktaskdetailItem) => {
    let res = true
    res = computedFinishStatus(row)
    if (row.preSessionId !== row.id) {
      row = listParams.list.find((item) => item.id === row.preSessionId)!
      if (!row) return true
      res = res && computedFinishStatus(row)
    }
    return res
  }
  const computedFinishStatus = (row: MarktaskdetailItem) => {
    const { questions } = listParams
    const is_result = Object.entries(row.result).every(([key, value]) => {
      const question = questions.find((question) => key === question.id + '')
      if (isDisabled.value.qi && !isDisabled.value.mark) {
        if (question?.annotationRole === AnnotationRole['PolyQualityInspector']) {
          return true
        }
      }
      if (!isDisabled.value.qi && isDisabled.value.mark) {
        if (question?.annotationRole === AnnotationRole['Annotationer']) {
          return true
        }
      }
      const type = question?.type
      const mustAnswer = question?.mustAnswer
      if (!mustAnswer) return true
      switch (type) {
        case QuestionType.single:
        case QuestionType.multiple:
          return !!(value as number[]).length
        case QuestionType.number:
          return !!(value as string)
        case QuestionType.subjective:
          return !!(value as string)
        case QuestionType.captions:
          return (value as Caption).confirm
        case QuestionType.multipleChoice:
        default:
          return true
      }
    })
    const gsb = marktaskdetailListData.value?.isGsb
    const isCaptions = marktaskdetailListData.value?.isCaptions
    const isWritingEval = marktaskdetailListData.value?.isWritingEval
    let is_finish: boolean
    if (gsb) {
      const gsbListItem = getGsbListItem(row.id)
      const is_gsb = !!gsbListItem?.every((item) => item.gsb)
      is_finish = is_result && is_gsb
    } else if (isWritingEval) {
      const is_writing_eval = !!(row.result[3] as number[]).length
      is_finish = is_result && is_writing_eval
    } else if (isCaptions || markTaskDetail.value?.scoreType === ScoreType['MarkSession']) {
      is_finish = is_result
    } else {
      const is_score = !!row.score || row.markScoreType === ScoreType.MarkScoreTypeNil
      is_finish = is_result && is_score
    }
    return is_finish
  }

  /** 重新计算 filterList，此处按组作为划分page，size的纬度，并非是常规分页 */
  const listHandle = () => {
    const filterList = filterCheck.value
      ? listParams.allList.reduce<MarktaskdetailRet['list']>((pre, now) => {
          const promptList = now.promptList.reduce<PromptListItem[]>((pre, now) => {
            const scoreItem = now.detailList.filter(flow([finishStatus, getInverseValue]))
            if (scoreItem.length) pre.push({ ...now, detailList: scoreItem })
            return pre
          }, [])
          if (promptList.length) pre.push({ session: now.session, promptList })
          return pre
        }, [])
      : listParams.allList
    filterList.forEach((item) => {
      item.promptList.forEach((prompt) =>
        prompt.detailList.forEach((detail) => {
          detail.totalPromptCount = void 0
          detail.totalCount = void 0
          detail.preSessionId = item.promptList[0].detailList[0].id
        })
      )
      item.promptList.forEach((prompt) => {
        prompt.detailList[0].totalCount = prompt.detailList.length
      })
      item.promptList[0].detailList[0].totalPromptCount = item.promptList.reduce(
        (pre, now) => ((pre += now.detailList.length), pre),
        0
      )
    })
    listParams.filterList = filterList
    listParams.pageNum = 1
    getMarktaskdetailList()
  }

  const { mutate: resultupdateMutate } = useResultUpdate({
    onSuccess(_, params) {
      const detailListItem = listParams.allList.reduce<MarktaskdetailItem | undefined>(
        (pre, now) => {
          const detailList = now.promptList.reduce<MarktaskdetailItem[]>(
            (pre, now) => (pre.push(...now.detailList), pre),
            []
          )
          return detailList.find((item) => item.id === params.markTaskDetailId) || pre
        },
        undefined
      )
      if (!detailListItem) return
      let listParamsKey: keyof MarktaskdetailItem | undefined
      let paramsKey: keyof ResultUpdateParams | undefined
      let value: ValueOf<ResultUpdateParams> | undefined
      if (Reflect.has(params, 'remark')) {
        listParamsKey = 'remark'
        paramsKey = 'remark'
        value = params[paramsKey]
      } else if (Reflect.has(params, 'score')) {
        listParamsKey = 'score'
        paramsKey = 'score'
        value = params[paramsKey]
      } else if (Reflect.has(params, 'question')) {
        listParamsKey = 'result'
        paramsKey = 'question'
        const previousData: ValueOf<MarktaskdetailItem> = detailListItem[listParamsKey]
        value = { ...previousData, ...params[paramsKey] }
      } else if (Reflect.has(params, 'commentLabelContent')) {
        listParamsKey = 'commentLabelContent'
        paramsKey = 'commentLabelContent'
        value = params[paramsKey]
      }
      if (!listParamsKey || !paramsKey) return
      Reflect.set(detailListItem, listParamsKey, value)
    }
  })
  const updateResult = ({
    key,
    value,
    markTaskDetailId
  }: {
    key: keyof ResultUpdateParams
    value: ValueOf<ResultUpdateParams>
    markTaskDetailId: number
  }) => {
    const params: ResultUpdateParams = { markTaskDetailId }
    Reflect.set(params, key, value)
    if (uname) params.ownerUname = uname
    resultupdateMutate(params)
  }
  const tableColumn: Ref<JSX.Element[]> = ref([])
  /** 计算当前的  pageCount,list,total */
  const getMarktaskdetailList = scrollInit(() => {
    const { pageNum, pageSize, filterList } = listParams
    listParams.pageCount = Math.ceil(filterList.length / pageSize)
    listParams.total = filterList.length
    const list = []
    for (let i = (pageNum - 1) * pageSize; i < pageNum * pageSize; i++) {
      const filterItem = filterList[i]
      filterItem && list.push(filterItem)
    }
    listParams.list = list.reduce<MarktaskdetailItem[]>(
      (pre = [], now) => pre.concat(...now.promptList.map((item) => item.detailList)),
      []
    )
    listParams.gsbList = list.map((item) => item.promptList.map((item) => item.gsbList)).flat()
  })
  /** 合并单元格 */
  const objectSpanMethod = ({
    column,
    row
  }: {
    column: TableColumnCtx<MarktaskdetailItem>
    row: MarktaskdetailItem
  }) => {
    const emptyRow = {
      rowspan: 0,
      colspan: 0
    }
    switch (column.property) {
      case 'session':
      case 'sessionMark':
      case 'sessionQi':
        if (row.totalPromptCount) {
          return {
            rowspan: row.totalPromptCount,
            colspan: 1
          }
        }
        return emptyRow
      case 'apiUser':
      case 'gsb':
      case 'sort':
        if (row.totalCount) {
          return {
            rowspan: row.totalCount,
            colspan: 1
          }
        }
        return emptyRow
      default:
        return {
          rowspan: 1,
          colspan: 1
        }
    }
  }
  return {
    tableColumn,
    listParams,
    getMarktaskdetailList,
    objectSpanMethod,
    tableRef,
    filterCheck,
    isDisabled,
    isLock,
    sortApiResultList,
    sortModalVisible,
    sortScore,
    updateResult,
    isHistory,
    audioVisible,
    questionCaption,
    audioSrc,
    updateQuestionResultFn,
    changeMarktaskdetailListHandle,
    combination,
    audioId,
    marktaskdetailListData
  }
}

export const useDetail = () => {
  const { firstAppear } = useMessage()
  const {
    mutate: getMarkTaskDetail,
    data: markTaskDetail,
    isLoading: getMarkTaskDetailLoading
  } = useGetMarkTaskDetail({
    onSuccess: firstAppear
  })
  const id = useRouteId()
  onMounted(() => getMarkTaskDetail({ id }))
  return { markTaskDetail, getMarkTaskDetailLoading }
}

export const useTag = (markTaskDetail: Ref<MarkTaskListItem | undefined>) => {
  const [enabled, setEnabled] = useToggle(false)
  invoke(async () => {
    await until(markTaskDetail).toMatch((value) => !!value?.isMarkTag)
    setEnabled()
  })
  const { data: markTagList, isLoading: markTagListLoading } = useQuery(
    ['getMarkTagList'],
    getMarkTagList,
    { enabled }
  )
  const { checkIdList, checkId } = useCheckBoxSingle()
  const checkTag = computed(() => markTagList.value?.list.find((item) => item.id === checkId.value))
  return { markTagList, markTagListLoading, checkIdList, checkTag }
}

export const useMessage = () => {
  const firstAppear = (markTaskDetail: MarkTaskListItem) => {
    const { id, note } = markTaskDetail
    if (!note) return
    const state = useStorage(`showContent${id}`, false)
    if (state.value) return
    state.value = true
    openMessage(note)
  }

  const openMessage = (note: string) => {
    ElMessageBox({
      title: '标注标准',
      customStyle: { width: '100%', maxWidth: '100%' },
      message: () => <QuillEditor edit={false} modelValue={note} />
    }).catch((err) => err)
  }
  return {
    openMessage,
    firstAppear
  }
}

const useSortModal = () => {
  const { visible, openModal } = useElModal()
  const sortApiResultList = ref<MarktaskdetailItem[]>([])
  const openSortModal = (markTaskList: MarktaskdetailItem[]) => {
    sortApiResultList.value = markTaskList
    openModal()
  }
  const sortScore = ref(0)
  return {
    sortModalVisible: visible,
    openSortModal,
    sortApiResultList,
    sortScore
  }
}
export type UpdateQuestionResultFn = (value: ValueOf<ResultType>) => void
export const useAudioModal = () => {
  const { visible: audioVisible, openModal } = useElModal()
  const questionCaption = ref<ValueOf<ResultType>>()
  const updateQuestionResultFn = ref<UpdateQuestionResultFn>()
  const openAudioModal = (
    updateQuestionResult: UpdateQuestionResultFn,
    questionDetail: ValueOf<ResultType>,
    src: string,
    id: number
  ) => {
    updateQuestionResultFn.value = updateQuestionResult
    questionCaption.value = questionDetail
    audioSrc.value = src
    audioId.value = id.toString()
    openModal()
  }
  const audioSrc = ref('')
  const audioId = ref('')
  return {
    audioVisible,
    openAudioModal,
    questionCaption,
    audioSrc,
    updateQuestionResultFn,
    audioId
  }
}
export const useHistoryModal = () => {
  const { visible, openModal } = useElModal()
  return {
    historyModalVisible: visible,
    openHistoryModal: openModal
  }
}
