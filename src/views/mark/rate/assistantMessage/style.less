.markdown-body {
  background-color: transparent;
  font-size: 14px;
  line-height: 32px;
  padding-left: 8px;

  p {
    white-space: pre-wrap;
    margin-top: 0 !important;
  }

  ol {
    list-style-type: decimal;
  }

  ul {
    list-style-type: disc;
  }

  pre code,
  pre tt {
    line-height: 1.65;
  }

  .highlight pre,
  pre {
    background-color: #fff;
  }

  code.hljs {
    padding: 0;
  }

  .code-block {
    &-wrapper {
      position: relative;
      padding-top: 24px;
    }

    &-header {
      position: absolute;
      top: 5px;
      right: 0;
      width: 100%;
      padding: 0 1rem;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #b3b3b3;

      &__copy {
        cursor: pointer;
        margin-left: 0.5rem;
        user-select: none;
        &:hover {
          color: #65a665;
        }
      }
    }
  }
}

html.dark {
  .highlight pre,
  pre {
    background-color: #282c34;
  }
}

.text-wrap {
  min-height: unset;
  min-width: 28px;
}
.leading-relaxed {
  line-height: 32px;
}
