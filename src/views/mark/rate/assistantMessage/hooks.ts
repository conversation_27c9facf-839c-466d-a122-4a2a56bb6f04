import { copyText } from '@/utils/format'
import { ElMessage } from 'element-plus'
import MarkdownIt from 'markdown-it'
import mdKatex from '@traptitech/markdown-it-katex'
import hljs from 'highlight.js'
export function useCopyCode() {
  const bodyElement = document.querySelector('body')
  const clickEvent = (e: MouseEvent) => {
    const target = e.target as Element | null
    if (target?.className !== 'code-block-header__copy') {
      return
    }
    const block = target.parentElement?.nextElementSibling
    block && copy(block)
  }
  const copy = (codeBlock: Element) => {
    ElMessage.success('复制成功')
    const text = codeBlock.textContent?.replaceAll('\n', '')
    if (navigator.clipboard?.writeText) navigator.clipboard.writeText(text ?? '')
    else copyText({ text: text ?? '', origin: true })
  }
  const bindClick = () => {
    bodyElement?.addEventListener('click', clickEvent)
  }
  const unMountClick = () => bodyElement?.removeEventListener('click', clickEvent)

  onMounted(bindClick)
  onBeforeUnmount(unMountClick)
}

export const useMarkdown = (propsText: string | undefined, error: boolean | undefined) => {
  const textRef = ref<HTMLElement>()

  const mdi = new MarkdownIt({
    linkify: true,
    highlight(code, language) {
      const validLang = !!(language && hljs.getLanguage(language))
      if (validLang) {
        const lang = language ?? ''
        return highlightBlock(
          hljs.highlight(code, { language: lang, ignoreIllegals: true }).value,
          lang
        )
      }
      return highlightBlock(hljs.highlightAuto(code).value, '')
    }
  })

  mdi.use(mdKatex, { blockClass: 'katexmath-block rounded-md p-[10px]', errorColor: ' #cc0000' })

  const wrapClass = computed(() => {
    return ['text-wrap', 'rounded-md', { 'text-red-500': error }]
  })
  const text = computed(() => {
    const value = propsText ?? ''
    const replaceData = replacePairedBracketsWithDollar(value)
    let htmlText = mdi.render(replaceData)
    if (replaceData && !htmlText) htmlText = mdi.render(`\\` + replaceData)
    return htmlText
  })

  function replacePairedBracketsWithDollar(str: string) {
    const str1 = str.replace(/\\\[([\s\S]*?)\\\]/g, (match, content) => {
      if (!containsMathOrLatex(content)) {
        return match
      }
      const text = content.replace(/(\n)/g, '')
      const text1 = text.replace(/^\s*|\s*$/g, '')
      return `$${text1}$`
    })
    return str1.replace(/\\\(([\s\S]*?)\\\)/g, (match, content) => {
      if (!containsMathOrLatex(content)) {
        return match
      }
      const text = content.replace(/(\n)/g, '')
      const text1 = text.replace(/^\s*|\s*$/g, '')
      return `$${text1}$`
    })
  }
  function containsMathOrLatex(str: string) {
    // 检测是否包含 LaTeX 命令的正则表达式
    const latexRegex = /\\[a-zA-Z]+|\\[{}]|\\[^\w\s]/
    // 检测是否包含常见数学符号的正则表达式
    const mathSymbolRegex = /[+\-*/=^()[\]{}|<>∞π∑√]/

    return latexRegex.test(str) || mathSymbolRegex.test(str)
  }
  function highlightBlock(str: string, lang?: string) {
    return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy">复制代码</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`
  }
  return {
    textRef,
    text,
    wrapClass
  }
}
