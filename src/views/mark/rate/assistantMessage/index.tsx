import './style.less'
import './highlight.less'
import './github-markdown.less'
import { useMarkdown } from './hooks'

export default defineComponent({
  props: {
    error: { type: Boolean },
    text: { type: String },
    lineHeight: { type: Number, default: 1.45 }
  },
  setup(props) {
    const { wrapClass, textRef, text } = useMarkdown(props.text, props.error)
    return () => (
      <div class={cx(wrapClass.value, 'text-black')}>
        <div class="leading-relaxed" ref={textRef} style={{wordBreak:'break-word'}}>
          <div
            class="markdown-body"
            style={{ wordBreak: 'break-word', lineHeight: props.lineHeight }}
            v-html={text.value}
          />
        </div>
      </div>
    )
  }
})
