import { Caption } from '@/api/marktask/type'
import SearchContainer from '@/components/search-container'
import { Pagination } from '@znzt-fe/components'
import {
  ElButton,
  ElCheckbox,
  ElCheckboxGroup,
  ElInput,
  ElMessage,
  ElSpace,
  ElSwitch,
  ElTable,
  ElTag,
  ElText
} from 'element-plus'
import Back from '../components/back'
import { ScoreType } from '../util'
import AudioModal from './audioModal'
import DeadLine from './deadLine'
import HistoryModal from './historyModal'
import { useDetail, useHistoryModal, useMessage, useTable, useTag } from './hook'
import style from './index.module.less'
import SortModal from './sortModal'
export default defineComponent({
  setup() {
    const { markTaskDetail, getMarkTaskDetailLoading } = useDetail()
    const { markTagList, markTagListLoading, checkIdList, checkTag } = useTag(markTaskDetail)
    const { openMessage } = useMessage()
    const {
      tableColumn,
      listParams,
      getMarktaskdetailList,
      objectSpanMethod,
      tableRef,
      filterCheck,
      isDisabled,
      sortModalVisible,
      sortApiResultList,
      sortScore,
      updateResult,
      isHistory,
      audioVisible,
      questionCaption,
      audioSrc,
      updateQuestionResultFn,
      changeMarktaskdetailListHandle,
      combination,
      audioId,
      marktaskdetailListData
    } = useTable(markTaskDetail, checkTag)
    const sessionRef = ref('')
    const { openHistoryModal, historyModalVisible } = useHistoryModal()
    return () => (
      <>
        <SearchContainer>
          <div class={style?.['left-container']}>
            <ElSpace>
              <Back backText={markTaskDetail.value?.name} />
              <ElCheckbox size="small" v-model={filterCheck.value}>
                筛选未标注
              </ElCheckbox>
              {markTaskDetail.value?.note && (
                <ElButton
                  link
                  onClick={() => openMessage(markTaskDetail.value.note)}
                  size="small"
                  type="primary">
                  展示标注标准
                </ElButton>
              )}
              <DeadLine endTime={markTaskDetail.value?.deadLine} />
              {isHistory.value && (
                <ElButton link onClick={() => openHistoryModal()} size="small" type="primary">
                  历史记录
                </ElButton>
              )}
              <ElText>组合排列</ElText>
              <ElSwitch onChange={changeMarktaskdetailListHandle} v-model={combination.value} />
              {markTaskDetail.value?.scoreType === ScoreType['MarkSession'] && (
                <ElInput placeholder="请输入session" v-model={sessionRef.value}>
                  {{
                    append: () => (
                      <ElButton
                        icon="Search"
                        onClick={() => {
                          const sessionIndex = listParams.allList.findIndex(
                            (item) => item.session === sessionRef.value
                          )
                          if (!~sessionIndex) {
                            ElMessage.warning('session不存在')
                            return
                          }
                          const pageNum = Math.floor(sessionIndex / listParams.pageSize) + 1
                          listParams.pageNum = pageNum
                          getMarktaskdetailList()
                          setTimeout(async () => {
                            await nextTick()
                            const index = listParams.list.findIndex(
                              (item) => item.session === sessionRef.value
                            )
                            const offsetTop = (
                              document.querySelectorAll('.el-table__row')[index] as HTMLElement
                            ).offsetTop
                            // @ts-ignore
                            tableRef.value?.setScrollTop(offsetTop)
                          })
                        }}
                        type="primary"
                      />
                    )
                  }}
                </ElInput>
              )}
            </ElSpace>
          </div>
          <div class={style?.['right-container']}>
            <span style={{ overflow: 'auto' }}>
              {markTaskDetail.value?.isMarkTag && (
                <ElCheckboxGroup
                  class="ml-4"
                  disabled={isDisabled.value.mark}
                  size="small"
                  v-loading={markTagListLoading.value}
                  v-model={checkIdList.value}>
                  <div style={{ display: 'flex' }}>
                    {markTagList.value?.list?.map((item) => (
                      <ElCheckbox key={item.id} value={item.id}>
                        <ElTag
                          color={item.color}
                          disableTransitions
                          key={item.id}
                          size="small"
                          style={{ color: 'white', cursor: 'pointer' }}>
                          {item.name}
                        </ElTag>
                      </ElCheckbox>
                    ))}
                  </div>
                </ElCheckboxGroup>
              )}
            </span>
          </div>
        </SearchContainer>
        <ElTable
          border
          cellStyle={{ height: '100%' }}
          class={style?.['table']}
          data={listParams.list}
          height="100%"
          ref={tableRef}
          rowKey="id"
          spanMethod={objectSpanMethod}
          style={{ width: '100%', height: 'calc(100% - 100px)' }}
          v-loading={getMarkTaskDetailLoading.value}>
          {tableColumn.value}
        </ElTable>
        <Pagination
          onRefresh={getMarktaskdetailList}
          pageCount={listParams.pageCount}
          pageSizes={listParams.pageSizes}
          small
          total={listParams.total}
          v-model:pageNum={listParams.pageNum}
          v-model:pageSize={listParams.pageSize}
        />
        <SortModal
          apiResultList={sortApiResultList.value}
          isShowSource={markTaskDetail.value?.isShowSource}
          score={sortScore.value}
          updateResult={updateResult}
          v-model={sortModalVisible.value}
        />
        {audioVisible.value && (
          <AudioModal
            audioId={audioId.value}
            audioSrc={audioSrc.value}
            questionList={marktaskdetailListData.value?.questions}
            updateResult={updateQuestionResultFn.value}
            v-model={audioVisible.value}
            v-model:questionCaption={questionCaption.value as Caption}
          />
        )}
        {isHistory.value && <HistoryModal v-model={historyModalVisible.value} />}
      </>
    )
  }
})
