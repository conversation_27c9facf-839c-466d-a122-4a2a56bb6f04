import {
  AnnotationGranularity,
  Caption,
  CaptionLayer,
  MultiChoice,
  NumberConfigType,
  Question,
  QuestionType,
  ResultType,
  UserManipulateLog
} from '@/api/marktask/type'
import useUserStore from '@/store/user'
import { arrayToNumber, numberToArray, numberToString, stringToNumber } from '@/utils'
import {
  Bottom,
  Check,
  Close,
  CollectionTag,
  Delete,
  Plus,
  Scissor,
  Search,
  Top,
  VideoPlay
} from '@element-plus/icons-vue'
import { ValueOf } from '@znzt-fe/declare'
import { confirmFn } from '@znzt-fe/utils'
import {
  ElButton,
  ElButtonGroup,
  ElCard,
  ElCheckbox,
  ElCheckboxGroup,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElMessage,
  ElOption,
  ElPopover,
  ElRadio,
  ElRadioGroup,
  ElSelect,
  ElSpace,
  ElSwitch,
  ElTag
} from 'element-plus'
import { cloneDeep, flow, isEqual } from 'lodash-es'
import { v4 as uuidv4 } from 'uuid'
import { parseMultipleChoice, UpdateQuestionResultFn } from '../hook'
import {
  boardEvent,
  formatMilliseconds,
  formatNormalTime,
  Height,
  insertAtIndex,
  MarginBottom,
  Padding,
  TagType,
  useHeaderOperation,
  useMarkTag,
  useSearch,
  useVideo
} from './hooks'
import style from './index.module.less'
import WaveSurferMedia from './waveSurferMedia'
export default defineComponent({
  props: {
    modelValue: Boolean,
    updateResult: Function as PropType<UpdateQuestionResultFn>,
    questionCaption: Object as PropType<Caption>,
    audioSrc: String,
    audioId: String,
    questionList: Array as PropType<Question[]>
  },
  emits: ['update:modelValue', 'update:questionCaption'],
  setup(props) {
    const getSlotElementSlots = (item: Question, result: ResultType) => {
      const updateQuestionResult = (value: ValueOf<ResultType>) => {
        result[item.id] = value
        updateResult()
      }
      const updateQuestionMultipleChoice = (
        value: string | undefined,
        arr: MultiChoice[],
        index: number
      ) => {
        arr = arr || []
        const detail = arr.find((item) => item.index === index)
        if (detail) detail.score = value || '0'
        else arr.push({ index, score: value || '0' })
        result[item.id] = arr
        updateResult()
      }
      const slotElementSlots = {
        [QuestionType.single]: () => (
          <ElRadioGroup
            modelValue={arrayToNumber(result?.[item.id] as number[]) ?? ''}
            onChange={flow([numberToArray, updateQuestionResult])}>
            {item.options.map((option) => (
              <ElRadio key={item.id} label={option.name} value={option.id} />
            ))}
          </ElRadioGroup>
        ),
        [QuestionType.multiple]: () => {
          const val = result?.[item.id] as number[]
          const arr = Object.entries(
            item.options.reduce<Record<string, { name: string; id: number }[]>>((pre, now) => {
              const nameArr = now.name
                .replaceAll('//', '$$$')
                .split('/')
                .map((item) => item.replaceAll('$$', '/'))
              const key = nameArr.shift() as keyof typeof pre
              if (!pre[key]) {
                pre[key] = []
              }
              pre[key].push({ name: nameArr.join(','), id: now.id })
              return pre
            }, {})
          )
          const isNormal = arr.every((item) => item[1].length === 1)
          return isNormal ? (
            <ElCheckboxGroup
              modelValue={val}
              onChange={(value) => updateQuestionResult(value as number[])}>
              {item.options.map((option) => (
                <ElCheckbox key={item.id} value={option.id}>
                  {option.name}
                </ElCheckbox>
              ))}
            </ElCheckboxGroup>
          ) : (
            arr.map((itemArr) => (
              <div key={itemArr[0]}>
                <div>
                  {itemArr[0]}
                  {val.some((item) => itemArr[1].map((item) => item.id).includes(item)) ? (
                    <ElTag class="ml-2" disableTransitions>
                      已标注
                    </ElTag>
                  ) : (
                    <ElTag class="ml-2" disableTransitions>
                      未标注
                    </ElTag>
                  )}
                </div>
                {itemArr[1].map((items) => {
                  return (
                    <ElCheckbox
                      key={items.id}
                      //  {...groupConfig}
                      modelValue={val.includes(items.id)}
                      onChange={(v) => {
                        v
                          ? val.push(items.id)
                          : val.splice(
                              val.findIndex((item) => item === items.id),
                              1
                            )
                        updateQuestionResult(val)
                      }}
                      value={items.id}>
                      {items.name}
                    </ElCheckbox>
                  )
                })}
              </div>
            ))
          )
        },
        [QuestionType.number]: () => {
          const { type, max, min, percision } = item.numberConfig
          const numberConfigElement = {
            [NumberConfigType.input]: () => (
              <ElInputNumber
                class="input-number"
                controls={false}
                max={max}
                min={min}
                modelValue={stringToNumber(result[item.id] as string)}
                onChange={flow([numberToString, updateQuestionResult])}
                precision={percision}
              />
            ),
            [NumberConfigType.select]: () => {
              const options = Array.from({ length: max - min + 1 }, (_, i) => i + min + '')
              return (
                <ElSelect modelValue={result[item.id]} onChange={updateQuestionResult}>
                  {options.map((option) => (
                    <ElOption key={option} label={option} value={option} />
                  ))}
                </ElSelect>
              )
            }
          }
          return numberConfigElement[type]()
        },
        [QuestionType.subjective]: () => {
          return (
            <ElInput
              modelValue={result?.[item.id] as string}
              onInput={updateQuestionResult}
              type="textarea"
            />
          )
        },
        [QuestionType.multipleChoice]: () => {
          const { percision, min, max } = item.numberConfig
          return (
            <>
              {item.options.map((option, index) => {
                return (
                  <div class="flex items-center" key={item.id}>
                    <ElInputNumber
                      class="shrink-0"
                      controls={false}
                      max={max}
                      min={min}
                      modelValue={parseMultipleChoice(
                        (result[item.id] as MultiChoice[])?.find((item) => option.id === item.index)
                          ?.score
                      )}
                      onChange={(v) =>
                        updateQuestionMultipleChoice(
                          v?.toString(),
                          result[item.id] as MultiChoice[],
                          index + 1
                        )
                      }
                      precision={percision}
                      style={{
                        width: '60px',
                        marginRight: '6px',
                        marginTop: index === 0 ? '0px' : '6px'
                      }}
                    />
                    <span class="grow overflow-auto">{option.name}</span>
                  </div>
                )
              })}
            </>
          )
        }
      }
      return (
        <div>
          {<div class={item.mustAnswer && style['required']}>{item.name}</div>}
          {slotElementSlots[item.type as keyof typeof slotElementSlots]?.()}
        </div>
      )
    }
    const userStore = useUserStore()
    const { audioSrc, audioId } = toRefs(props)
    const questionCaption = useModel(props, 'questionCaption')
    const visible = useModel(props, 'modelValue') as Ref<boolean>
    const confirmMark = ref(false)
    const { autoScrollSwitch, readOnly } = useHeaderOperation()
    const getLines = () =>
      content.value.map((item) => ({
        start: item.start,
        end: item.end,
        content: item.content,
        userManipulateLog: item.userManipulateLog,
        deleted: item.deleted,
        questionResult: item.questionResult ?? {}
      }))
    const addLayer = async () => {
      const layer: CaptionLayer = {
        lines: [],
        name: '',
        canDel: true
      }
      const layers = questionCaption.value?.layers
      if (!layers) return
      layers.push(layer)
      selectLayer.value = layers.length - 1
      setContent(layers[selectLayer.value].lines)
      updateResult(true)
      await nextTick()
      waveSurferRef.value.initRegions()
    }
    // validate用于跳过验证，在添加层，删除层时，无需验证
    const updateResult = (skipValidate = false) => {
      const layers = questionCaption.value?.layers
      if (!layers) return
      const lines = getLines()
      if (
        !skipValidate &&
        questionCaption.value?.confirm === confirmMark.value &&
        isEqual(layers[selectLayer.value].lines, lines)
      ) {
        return
      }
      layers[selectLayer.value].lines = lines.concat(deletedLines.value)
      const caption: Caption = {
        layers,
        confirm: confirmMark.value
      }
      questionCaption.value = cloneDeep(caption)
      props.updateResult?.(caption)
    }
    const awaitTimeOut = () => new Promise((resolve) => setTimeout(resolve))
    const deleteLayer = confirmFn(async () => {
      const index = selectLayer.value
      const layers = questionCaption.value?.layers
      if (!layers) return
      layers.splice(index, 1)
      selectLayer.value = layers.length - 1
      setContent(layers[selectLayer.value].lines)
      await nextTick()
      waveSurferRef.value.initRegions()
      updateResult(true)
    })
    const {
      selectionRange,
      popoverRef,
      tagList,
      selectTag,
      popoverVisible,
      tagType,
      selectContent
    } = useMarkTag()
    const {
      inputFocus,
      playVideo,
      onBlur,
      resume,
      pause,
      inputRef,
      videoRef,
      scrollRef,
      content,
      active,
      scrollChange,
      setContent,
      contentListRef,
      waveSurferRef,
      selectLayer,
      resetActiveInput,
      deletedLines
    } = useVideo(autoScrollSwitch, updateResult)
    const setActiveInput = async (index: number) => {
      if (readOnly.value) return
      const instance = inputRef.value
      if (instance) {
        instance.blur()
        await awaitTimeOut()
      }
      active.value.activeInput = index
      inputFocus()
    }
    const {
      searchText,
      replaceText,
      totalSearchNum,
      currentSearchNum,
      searchRef,
      clearInputFn,
      changeSearchText,
      replaceAllTextFn,
      replaceTextFn,
      nextSearchNum,
      preSearchNum,
      focusSearch
    } = useSearch(content, scrollChange, updateResult)

    invoke(async () => {
      until(visible).toBeTruthy()
      setContent(questionCaption.value?.layers[0].lines || [])
      confirmMark.value = questionCaption.value!.confirm
    })
    const popoverClick = async () => {
      await awaitTimeOut()
      if (!~selectionRange.value.start || !~selectionRange.value.end) {
        ElMessage.warning('请先聚焦输入框后再点击')
        return
      }
      const detail = content.value.find((item) => active.value.activeId === item.id)!
      if (selectionRange.value.start === selectionRange.value.end) {
        const after = detail.content.slice(
          selectionRange.value.start,
          selectionRange.value.start + 1
        )
        const before = detail.content.slice(
          selectionRange.value.start - 1,
          selectionRange.value.start
        )
        if (after !== ' ' && after && before !== ' ' && before) {
          setActiveInput(2)
          setTimeout(() => {
            inputRef.value?.focus()
            ElMessage.warning('单词中间不可插入标签')
            inputRef.value?.$el.children[0].setSelectionRange(
              selectionRange.value.start,
              selectionRange.value.end
            )
          })
          return
        }
        selectContent.value.startAfter = after
        selectContent.value.startBefore = before
        selectContent.value.endAfter = undefined
        selectContent.value.endBefore = undefined
      } else {
        const startAfter = detail.content.slice(
          selectionRange.value.start,
          selectionRange.value.start + 1
        )
        const startBefore = detail.content.slice(
          selectionRange.value.start - 1,
          selectionRange.value.start
        )
        const endAfter = detail.content.slice(
          selectionRange.value.end,
          selectionRange.value.end + 1
        )
        const endBefore = detail.content.slice(
          selectionRange.value.end - 1,
          selectionRange.value.end
        )
        if (
          (startAfter !== ' ' && startAfter && startBefore !== ' ' && startBefore) ||
          (endAfter !== ' ' && endAfter && endBefore !== ' ' && endBefore)
        ) {
          setActiveInput(2)
          setTimeout(() => {
            inputRef.value?.focus()
            ElMessage.warning('单词中间不可插入标签')
            inputRef.value?.$el.children[0].setSelectionRange(
              selectionRange.value.start,
              selectionRange.value.end
            )
          })
          return
        }
        selectContent.value.startAfter = startAfter
        selectContent.value.startBefore = startBefore
        selectContent.value.endAfter = endAfter
        selectContent.value.endBefore = endBefore
      }
      popoverVisible.value = true
    }
    const confirmSelectTag = () => {
      const detail = content.value.find((item) => active.value.activeId === item.id)!
      setActiveInput(2)
      popoverVisible.value = false
      const tagDetail = tagList.find((item) => selectTag.value === item.value)!
      let data = ''
      if (tagDetail.type === TagType['Token']) {
        let startBeforeStr = ''
        let startAfterStr = ''
        let startOffset = 0
        let endOffset = 0
        // 说明是开头，则无需处理，交给后边处理
        if (selectContent.value.startBefore !== ' ' && selectionRange.value.start !== 0) {
          startBeforeStr = ' '
          startOffset++
          endOffset++
        }
        if (
          selectContent.value.startAfter !== ' ' &&
          selectionRange.value.end !== detail.content.length
        ) {
          startAfterStr = ' '
        }
        data = insertAtIndex(
          detail.content,
          selectionRange.value.start,
          startBeforeStr + tagDetail.start + startAfterStr
        )
        selectionRange.value.start += startOffset
        selectionRange.value.end += endOffset
        detail.content = data
        setTimeout(() => {
          inputRef.value?.focus()
          inputRef.value?.$el.children[0].setSelectionRange(
            selectionRange.value.start,
            selectionRange.value.end + tagDetail.start.length + tagDetail.end.length
          )
          selectionRange.value.start = -1
          selectionRange.value.end = -1
        })
      } else {
        let startBeforeStr = ''
        let startAfterStr = ''
        let endBeforeStr = ''
        let endAfterStr = ''
        let startOffset = 0
        let endOffset = 0
        // 说明是开头，则无需处理，交给后边处理
        if (selectContent.value.startBefore !== ' ' && selectionRange.value.start !== 0) {
          startBeforeStr = ' '
          startOffset++
          endOffset++
        }
        if (selectContent.value.startAfter !== ' ') {
          startAfterStr = ' '
          endOffset++
        }
        if (selectContent.value.endBefore !== ' ') {
          endBeforeStr = ' '
          endOffset++
        }
        if (
          selectContent.value.endAfter !== ' ' &&
          selectionRange.value.end !== detail.content.length
        ) {
          endAfterStr = ' '
          endOffset++
        }

        data = insertAtIndex(
          detail.content,
          selectionRange.value.start,
          startBeforeStr + tagDetail.start + startAfterStr
        )
        data = insertAtIndex(
          data,
          selectionRange.value.end +
            tagDetail.start.length +
            startBeforeStr.length +
            startAfterStr.length,
          endBeforeStr + tagDetail.end + endAfterStr
        )
        selectionRange.value.start += startOffset
        selectionRange.value.end += endOffset
        detail.content = data
        setTimeout(() => {
          inputRef.value?.focus()
          inputRef.value?.$el.children[0].setSelectionRange(
            selectionRange.value.start,
            selectionRange.value.end + tagDetail.start.length + tagDetail.end.length
          )
          selectionRange.value.start = -1
          selectionRange.value.end = -1
        })
      }
    }
    function isIncrementingByOne(arr: number[]) {
      for (let i = 1; i < arr.length; i++) {
        if (arr[i] !== arr[i - 1] + 1) {
          return false
        }
      }
      return true
    }
    const confirmMerge = () => {
      const sortArr = [...checkMap.value.values()].sort((a, b) => a - b)
      if (!sortArr.length) {
        ElMessage.warning('请选择至少一个进行合并')
        return
      }
      if (!isIncrementingByOne(sortArr)) {
        ElMessage.warning('请选择连续的区域进行合并')
        return
      }
      const startIndex = sortArr[0]
      const endIndex = sortArr[sortArr.length - 1]
      const start = content.value[startIndex].start
      const startTime = content.value[startIndex].startTime
      const end = content.value[endIndex].end
      const endTime = content.value[endIndex].endTime
      const contentInner = sortArr.reduce(
        (pre, now, index) =>
          !index ? pre + content.value[now].content : pre + ' ' + content.value[now].content,
        ''
      )
      const contentHtml = sortArr.reduce(
        (pre, now, index) =>
          !index
            ? pre + content.value[now].contentHtml
            : pre + ' ' + content.value[now].contentHtml,
        ''
      )
      const contentDetail = {
        start,
        startTime,
        tempStart: startTime,
        end,
        endTime,
        tempEnd: endTime,
        content: contentInner,
        id: uuidv4(),
        contentHtml,
        userManipulateLog: [
          {
            user: userStore.name,
            datetime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            type: 'merge' as UserManipulateLog['type']
          }
        ],
        deleted: false,
        questionResult: {}
      }
      content.value.splice(startIndex, endIndex - startIndex + 1, contentDetail)
      checkMap.value.clear()
      showMergeCheck.value = false
      updateResult()
      waveSurferRef.value.initRegions()
    }
    const showMergeCheck = ref(false)
    const checkMap = ref<Map<string, number>>(new Map())
    return () => (
      <ElDialog
        class="dialog-column !min-w-[1000px]"
        destroyOnClose
        fullscreen
        lockScroll
        onClose={() => {
          waveSurferRef.value?.pause?.()
          videoRef.value?.pause?.()
        }}
        title="音视频标注"
        v-model={visible.value}>
        <div class="flex h-full">
          <ElCard style={{ flex: '50 1 0px', overflow: 'auto' }}>
            <WaveSurferMedia
              addRegion={async (id: string) => {
                setActiveInput(2)
                const detail = content.value.find((item) => item.id === id)
                if (!detail) return
                active.value.activeId = detail.id
                scrollChange(detail.id)
                inputRef.value?.focus()
              }}
              audioId={audioId.value}
              onPause={pause}
              onPlay={resume}
              ref={waveSurferRef}
              src={audioSrc.value}
              updateResult={updateResult}
              v-model={videoRef.value}
              v-model:content={content.value}
            />
          </ElCard>
          <ElCard class="card-column" style={{ flex: '50 1 0px' }}>
            {{
              header: () => (
                <div>
                  <ElForm inline labelPosition="left">
                    <div>
                      <ElFormItem class="!mb-0" label="自动滚动">
                        <ElSwitch v-model={autoScrollSwitch.value} />
                      </ElFormItem>
                      <ElFormItem class="!mb-0" label="阅读模式">
                        <ElSwitch v-model={readOnly.value} />
                      </ElFormItem>
                      <ElFormItem class="!mb-0" label="标注完成">
                        <ElSwitch onChange={() => updateResult()} v-model={confirmMark.value} />
                      </ElFormItem>
                    </div>
                    <div>
                      {questionCaption.value?.layers.length === 1 ? (
                        <ElButtonGroup>
                          <ElButton icon={<Plus />} onClick={addLayer}>
                            添加层
                          </ElButton>
                        </ElButtonGroup>
                      ) : (
                        <>
                          <ElSelect
                            class="w-[200px]"
                            onChange={async (v) => {
                              setContent(questionCaption.value!.layers[v].lines)
                              await nextTick()
                              setTimeout(() => {
                                waveSurferRef.value.initRegions()
                              })
                            }}
                            v-model={selectLayer.value}>
                            {questionCaption.value?.layers.map((item, index) => (
                              <ElOption
                                key={item.name + index}
                                label={index === 0 ? '字幕层' : index === 1 ? '副语言层' : ''}
                                value={index}
                              />
                            ))}
                          </ElSelect>
                          {questionCaption.value?.layers[selectLayer.value].canDel && (
                            <ElButton icon={Delete} link onClick={deleteLayer} type="danger" />
                          )}
                        </>
                      )}
                    </div>
                    <div class="mt-1">
                      <ElButtonGroup class="mr-2">
                        <ElPopover
                          onAfter-enter={focusSearch}
                          placement="left"
                          trigger="click"
                          width="400">
                          {{
                            reference: () => <ElButton icon={<Search />}>搜索</ElButton>,
                            default: () => (
                              <div class="flex">
                                <div class="grow mr-3">
                                  <div class="h-10 flex items-center">
                                    <ElInput
                                      onInput={changeSearchText}
                                      onKeydown={(e) =>
                                        boardEvent(e, {
                                          enter: nextSearchNum,
                                          shiftEnter: preSearchNum
                                        })
                                      }
                                      placeholder="搜索"
                                      ref={searchRef}
                                      v-model={searchText.value}>
                                      {{
                                        suffix: () =>
                                          `${currentSearchNum.value}/${totalSearchNum.value}`
                                      }}
                                    </ElInput>
                                  </div>
                                  <div class="h-10 flex items-center">
                                    <ElInput placeholder="替换为" v-model={replaceText.value} />
                                  </div>
                                </div>
                                <div class="w-[132px] shrink-0">
                                  <div class="h-10 flex items-center">
                                    <ElButton icon={<Top />} link onClick={nextSearchNum} />
                                    <ElButton icon={<Bottom />} link onClick={preSearchNum} />
                                    <ElButton icon={<Close />} link onClick={clearInputFn} />
                                  </div>
                                  <div class="h-10 flex items-center">
                                    <ElButton onClick={replaceTextFn} size="small" type="primary">
                                      替换
                                    </ElButton>
                                    <ElButton
                                      onClick={replaceAllTextFn}
                                      size="small"
                                      type="primary">
                                      全部替换
                                    </ElButton>
                                  </div>
                                </div>
                              </div>
                            )
                          }}
                        </ElPopover>
                        <ElButton
                          icon={<Plus />}
                          onClick={async () => {
                            const lines = getLines()
                            const length = lines.length
                            const start = lines[length - 1]?.end || 0
                            lines.push({
                              start,
                              content: '',
                              end: start,
                              userManipulateLog: [
                                {
                                  user: userStore.name,
                                  datetime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                                  type: 'add' as UserManipulateLog['type']
                                }
                              ],
                              deleted: false,
                              questionResult: {}
                            })
                            content.value.push({
                              start,
                              tempStart: formatMilliseconds(start),
                              startTime: formatMilliseconds(start),
                              end: start,
                              tempEnd: formatMilliseconds(start),
                              endTime: formatMilliseconds(start),
                              content: '',
                              id: uuidv4(),
                              contentHtml: '',
                              userManipulateLog: [
                                {
                                  user: userStore.name,
                                  datetime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                                  type: 'add' as UserManipulateLog['type']
                                }
                              ],
                              deleted: false,
                              questionResult: {}
                            })
                            updateResult()
                            await nextTick()
                            setActiveInput(2)
                            const detail = content.value[content.value.length - 1]
                            active.value.activeId = detail.id
                            scrollChange(detail.id)
                            waveSurferRef.value.addRegion(detail.id)
                          }}>
                          添加
                        </ElButton>
                        <ElPopover
                          onAfter-leave={() => {
                            selectTag.value = ''
                          }}
                          ref={popoverRef}
                          trigger="click"
                          visible={popoverVisible.value}
                          width={200}>
                          {{
                            reference: () => (
                              <ElButton icon={<CollectionTag />} onClick={popoverClick}>
                                插入标签
                              </ElButton>
                            ),
                            default: () => (
                              <>
                                <ElSelect
                                  onChange={(v) => v && confirmSelectTag()}
                                  v-model={selectTag.value}>
                                  {tagList
                                    .filter((item) => item.type === tagType.value)
                                    .map((item) => (
                                      <ElOption
                                        key={item.start + item.end}
                                        label={`${item.label}:${item.start}${item.end}`}
                                        value={item.value}
                                      />
                                    ))}
                                </ElSelect>
                                <div class="mt-2 flex justify-end">
                                  <ElButton
                                    onClick={() => {
                                      setActiveInput(2)
                                      popoverVisible.value = false
                                      setTimeout(() => {
                                        inputRef.value?.focus()
                                        inputRef.value?.$el.children[0].setSelectionRange(
                                          selectionRange.value.start,
                                          selectionRange.value.end
                                        )
                                      })
                                    }}
                                    size="small">
                                    关闭
                                  </ElButton>
                                </div>
                              </>
                            )
                          }}
                        </ElPopover>
                      </ElButtonGroup>
                      <ElButtonGroup>
                        {!showMergeCheck.value ? (
                          <ElButton
                            icon={<Scissor />}
                            onClick={() => (showMergeCheck.value = true)}>
                            选择合并
                          </ElButton>
                        ) : (
                          <>
                            <ElButton icon={<Check />} onClick={() => confirmMerge()}>
                              确认合并
                            </ElButton>
                            <ElButton
                              icon={<Close />}
                              onClick={() => {
                                showMergeCheck.value = false
                                checkMap.value.clear()
                              }}>
                              取消合并
                            </ElButton>
                          </>
                        )}
                      </ElButtonGroup>
                    </div>
                  </ElForm>
                </div>
              ),
              default: () => (
                <div class="h-full overflow-auto w-full" ref={scrollRef}>
                  {content.value.map((item, index) => (
                    <div key={item.id}>
                      <div
                        class="flex justify-between items-start rounded"
                        onMouseenter={() => {
                          if (popoverVisible.value) return
                          active.value.hoverId = item.id
                        }}
                        onMouseleave={() => {
                          if (popoverVisible.value) return
                          active.value.hoverId = ''
                        }}
                        ref={(ref) => (contentListRef.value[index] = ref as Element)}
                        style={{
                          marginBottom: `${MarginBottom}px`,
                          padding: `${Padding}px`,
                          backgroundColor:
                            active.value.hoverId === item.id ? 'var(--el-border-color)' : 'unset'
                        }}>
                        {showMergeCheck.value && (
                          <div class="mr-2">
                            <ElCheckbox
                              onChange={(v) =>
                                v
                                  ? checkMap.value.set(item.id, index)
                                  : checkMap.value.delete(item.id)
                              }
                            />
                          </div>
                        )}
                        <div
                          class="flex items-start grow"
                          onClick={() =>
                            !readOnly.value
                              ? (active.value.activeId = item.id)
                              : (active.value.activeId = '')
                          }>
                          <ElSpace>
                            <ElButton icon={<VideoPlay />} link onClick={() => playVideo(item)} />
                            <div class="w-[90px]">
                              {active.value.activeInput === 0 &&
                              active.value.activeId === item.id ? (
                                <ElInput
                                  onBlur={() =>
                                    onBlur(
                                      item.tempStart,
                                      () => {
                                        const end = item.end
                                        const now = formatNormalTime(item.tempStart)
                                        if (now > end) {
                                          ElMessage.warning('开始时间不可大于当前结束时间')
                                          return false
                                        }
                                        const preEnd = content.value[index - 1]?.end
                                        if (preEnd && now < preEnd) {
                                          ElMessage.warning('开始时间不可小于下一条结束时间')
                                          return false
                                        }
                                        item.startTime = item.tempStart
                                        item.start = now
                                        waveSurferRef.value.updateRegion(item.id)
                                      },
                                      true
                                    )
                                  }
                                  ref={inputRef}
                                  v-model={item.tempStart}
                                />
                              ) : (
                                <div
                                  class="flex items-center"
                                  onClick={() => {
                                    item.tempStart = item.startTime
                                    setActiveInput(0)
                                  }}
                                  style={{ padding: '1px 11px', height: `${Height}px` }}>
                                  {item.startTime}
                                </div>
                              )}
                            </div>
                            <div>-</div>
                            <div class="w-[90px]">
                              {active.value.activeInput === 1 &&
                              active.value.activeId === item.id ? (
                                <ElInput
                                  onBlur={() =>
                                    onBlur(
                                      item.tempEnd,
                                      () => {
                                        const now = formatNormalTime(item.tempEnd)
                                        const nextStart = content.value[index + 1]?.start
                                        if (nextStart && now > nextStart) {
                                          ElMessage.warning('结束时间不可小于下一条的开始时间')
                                          return false
                                        }
                                        const start = item.start
                                        if (start > now) {
                                          ElMessage.warning('结束时间不可小于当前开始时间')
                                          return false
                                        }
                                        item.endTime = item.tempEnd
                                        item.end = now
                                        const editLog = item.userManipulateLog.find(
                                          (item) =>
                                            item.user === userStore.name && item.type === 'edit'
                                        )
                                        if (editLog) {
                                          editLog.datetime = dayjs().format('YYYY-MM-DD HH:mm:ss')
                                        } else {
                                          item.userManipulateLog.push({
                                            type: 'edit',
                                            user: userStore.name,
                                            datetime: dayjs().format('YYYY-MM-DD HH:mm:ss')
                                          })
                                        }
                                        waveSurferRef.value.updateRegion(item.id)
                                      },
                                      true
                                    )
                                  }
                                  ref={inputRef}
                                  v-model={item.tempEnd}
                                />
                              ) : (
                                <div
                                  class="flex items-center "
                                  onClick={() => {
                                    item.tempEnd = item.endTime
                                    setActiveInput(1)
                                  }}
                                  style={{ padding: '1px 11px', height: `${Height}px` }}>
                                  {item.endTime}
                                </div>
                              )}
                            </div>
                          </ElSpace>
                          <div
                            class={cx(
                              active.value.activeContentId === item.id && style['content-active'],
                              'grow mx-2 min-h-[32px]'
                            )}>
                            {active.value.activeInput === 2 && active.value.activeId === item.id ? (
                              <ElInput
                                autosize
                                onBlur={() => {
                                  if (inputRef.value) {
                                    const { selectionStart, selectionEnd } =
                                      inputRef.value.$el.children[0]
                                    selectionRange.value.start = selectionStart
                                    selectionRange.value.end = selectionEnd
                                    resetActiveInput()
                                  }
                                  clearInputFn()
                                  updateResult()
                                  const editLog = item.userManipulateLog.find(
                                    (item) => item.user === userStore.name && item.type === 'edit'
                                  )
                                  if (editLog) {
                                    editLog.datetime = dayjs().format('YYYY-MM-DD HH:mm:ss')
                                  } else {
                                    item.userManipulateLog.push({
                                      type: 'edit',
                                      user: userStore.name,
                                      datetime: dayjs().format('YYYY-MM-DD HH:mm:ss')
                                    })
                                  }
                                }}
                                ref={inputRef}
                                resize="none"
                                type="textarea"
                                v-model={item.content}
                              />
                            ) : (
                              <div
                                class="box-border min-h-[32px]"
                                onClick={() => {
                                  setActiveInput(2)
                                  popoverVisible.value = false
                                }}
                                style={{ padding: '5px 11px', whiteSpace: 'pre-wrap' }}>
                                {<span class="break-words " v-html={item.contentHtml} />}
                              </div>
                            )}
                          </div>
                        </div>
                        <div
                          class="w-[48px] flex items-center shrink-0"
                          style={{ height: `${Height}px` }}>
                          {active.value.hoverId === item.id && !readOnly.value && (
                            <ElButtonGroup>
                              <ElButton
                                icon={<Plus />}
                                link
                                onClick={async () => {
                                  const lines = getLines()
                                  const start = item.end
                                  const end = content.value[index + 1]?.start || item.end
                                  lines.splice(index + 1, 0, {
                                    start: item.end,
                                    content: '',
                                    end: content.value[index + 1]?.start || item.end,
                                    userManipulateLog: [
                                      {
                                        user: userStore.name,
                                        datetime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                                        type: 'add' as UserManipulateLog['type']
                                      }
                                    ],
                                    deleted: false,
                                    questionResult: {}
                                  })
                                  content.value.splice(index + 1, 0, {
                                    start,
                                    content: '',
                                    end,
                                    tempStart: formatMilliseconds(start),
                                    startTime: formatMilliseconds(start),
                                    tempEnd: formatMilliseconds(end),
                                    endTime: formatMilliseconds(end),
                                    contentHtml: '',
                                    id: uuidv4(),
                                    userManipulateLog: [
                                      {
                                        user: userStore.name,
                                        datetime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                                        type: 'add' as UserManipulateLog['type']
                                      }
                                    ],
                                    deleted: false,
                                    questionResult: {}
                                  })
                                  updateResult()
                                  await nextTick()
                                  setActiveInput(2)
                                  const detail = content.value[index + 1]
                                  active.value.activeId = detail.id
                                  scrollChange(detail.id)
                                  waveSurferRef.value.addRegion(detail.id)
                                }}
                                type="primary"
                              />
                              <ElButton
                                disabled={content.value.length === 1}
                                icon={<Delete />}
                                link
                                onClick={() => {
                                  content.value[index].deleted = true
                                  content.value[index].userManipulateLog.push({
                                    type: 'deleted',
                                    user: userStore.name,
                                    datetime: dayjs().format('YYYY-MM-DD HH:mm:ss')
                                  })
                                  const deletedLine = content.value.splice(index, 1)[0]
                                  deletedLines.value.push({
                                    start: deletedLine.start,
                                    end: deletedLine.end,
                                    content: deletedLine.content,
                                    userManipulateLog: deletedLine.userManipulateLog,
                                    deleted: deletedLine.deleted,
                                    questionResult: deletedLine.questionResult ?? {}
                                  })
                                  updateResult()
                                  waveSurferRef.value.removeRegion(item.id)
                                }}
                                type="danger"
                              />
                            </ElButtonGroup>
                          )}
                        </div>
                      </div>
                      <div>
                        {props.questionList
                          ?.filter(
                            (question) =>
                              question.annotationGranularity === AnnotationGranularity['Captions']
                          )
                          .map((question) => (
                            <div class="mt-2" key={question.id}>
                              {getSlotElementSlots(question, item.questionResult)}
                            </div>
                          ))}
                      </div>
                    </div>
                  ))}
                </div>
              )
            }}
          </ElCard>
        </div>
      </ElDialog>
    )
  }
})
