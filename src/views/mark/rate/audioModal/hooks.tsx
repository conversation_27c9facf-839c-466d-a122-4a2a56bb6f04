import { CaptionLine, ResultType, UserManipulateLog } from '@/api/marktask/type'
import { ElMessage, InputInstance, PopoverInstance } from 'element-plus'
import { escape, flow } from 'lodash-es'
import { v4 as uuidv4 } from 'uuid'
export const Height = 32
export const Padding = 4
export const MarginBottom = 8
export const ScrollStep = Height + Padding * 2 + MarginBottom

/** 秒转换为展示时间格式 */
export const formatMilliseconds = (ms: number) => {
  // 计算分钟
  const minutes = Math.floor(ms / 60000)
  // 计算剩余的秒数
  const seconds = Math.floor((ms % 60000) / 1000)
  // 计算剩余的毫秒数
  const milliseconds = ms % 1000
  // 格式化为两位数
  const formattedMinutes = String(minutes).padStart(2, '0')
  const formattedSeconds = String(seconds).padStart(2, '0')
  const formattedMilliseconds = String(milliseconds).padStart(3, '0')

  return `${formattedMinutes}:${formattedSeconds}:${formattedMilliseconds}`
}

export const formatNormalTime = (timeString: string) => {
  const [minutes, seconds, ms] = timeString.split(':').map(Number)
  const milliseconds = new Decimal(minutes)
    .times(60)
    .times(1000)
    .plus(new Decimal(seconds).times(1000))
    .plus(ms)
    .toNumber()
  return milliseconds
}
const timeRegex = /^([0-9]+):([0-5][0-9]):([0-9]{3})$/
export const validateTimeFormat = (timeString: string) => {
  const result = timeRegex.test(timeString)
  !result && ElMessage.warning('不符合时间格式，请按照 分钟:秒:毫秒的格式填写')
  return result
}

export const useHeaderOperation = () => {
  const autoScrollSwitch = ref(true)
  const readOnly = ref(false)
  return {
    autoScrollSwitch,
    readOnly
  }
}
const useScrollRef = () => {
  const topRef = ref<Element>()
  const scrollRef = ref<HTMLElement>()
  const scrolling = ref(false)
  const scrollTo = (el: Element, force?: boolean) => {
    if (el === topRef.value || (scrolling.value && !force)) return
    if (!el?.scrollIntoView) return
    topRef.value = el
    el.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
  useEventListener(scrollRef, 'scroll', () => {
    scrolling.value = true
    start()
  })
  const { start } = useTimeoutFn(() => (scrolling.value = false), 200)
  return {
    scrollRef,
    scrollTo
  }
}
const useInputRef = () => {
  const inputRef = ref<InputInstance>()
  const inputFocus = () => {
    inFocus.value = true
    setTimeout(() => {
      if (!inputRef.value) return
      inputRef.value.focus()
    })
  }
  const inFocus = ref(false)
  return {
    inputRef,
    inputFocus,
    inFocus
  }
}
const useVideoRef = (fn: () => (date: number) => void) => {
  const waveSurferRef = ref()
  const videoRef = ref<HTMLVideoElement | HTMLAudioElement>()
  const playVideo = (res: ContentItem) => {
    setTimeout(() => {
      waveSurferRef.value.play(res)
      fn()(res.start)
    })
  }
  const getVideoCurrentSeconds = () => {
    return new Decimal(waveSurferRef.value.getCurrentTime() || 0).times(1000).toNumber()
  }
  return {
    videoRef,
    playVideo,
    getVideoCurrentSeconds,
    waveSurferRef
  }
}

const useActive = () => {
  const active = ref({
    activeId: '',
    activeInput: -1,
    hoverId: '',
    activeContentId: ''
  })
  const resetActiveInput = () => (active.value.activeInput = -1)

  return {
    active,
    resetActiveInput
  }
}
export function insertAtIndex(str: string, index: number, content: string) {
  return str.slice(0, index) + content + str.slice(index)
}
export interface ContentItem {
  userManipulateLog: UserManipulateLog[]
  deleted: boolean
  start: number
  tempStart: string
  end: number
  tempEnd: string
  startTime: string
  endTime: string
  contentHtml: string
  id: string
  content: string
  questionResult: ResultType
}
export const useVideo = (autoScrollSwitch: Ref<boolean>, updateResult: () => void) => {
  const { scrollRef, scrollTo } = useScrollRef()
  const { videoRef, playVideo, getVideoCurrentSeconds, waveSurferRef } = useVideoRef(() =>
    flow([activeChange, scrollChange])
  )
  const { inputRef, inputFocus, inFocus } = useInputRef()
  const { active, resetActiveInput: resetActiveInputOrigin } = useActive()
  const temporaryContent = ref('')
  const resetActiveInput = () => {
    resetActiveInputOrigin()
    inFocus.value = false
  }
  const content = ref<ContentItem[]>([])
  const deletedLines = ref<CaptionLine[]>([])
  const selectLayer = ref(0)
  const setContent = (lines: CaptionLine[]) => {
    content.value = lines
      .filter((item) => !item.deleted)
      .map((item) => ({
        ...item,
        start: item.start,
        end: item.end,
        tempStart: formatMilliseconds(item.start),
        startTime: formatMilliseconds(item.start),
        endTime: formatMilliseconds(item.end),
        tempEnd: formatMilliseconds(item.end),
        contentHtml: escape(item.content),
        userManipulateLog: item.userManipulateLog || [],
        deleted: item.deleted || false,
        id: uuidv4(),
        questionResult: item.questionResult ?? {}
      }))
    deletedLines.value = lines.filter((item) => item.deleted)
  }
  const autScroll = computed(() => !inFocus.value && autoScrollSwitch.value)
  /** 修改对应文字颜色 */
  const activeChange = (date = getVideoCurrentSeconds()) => {
    let id = content.value.find((item) => item.start <= date && item.end > date)?.id
    if (!id) {
      id = content.value.reduce((pre, now) => (now.start < date ? now.id : pre), '')
    }
    active.value.activeContentId = id
    return id
  }
  /** 滚动到对应位置 */
  const scrollChange = (id?: string, force?: boolean) => {
    if (!id || !autScroll.value || !scrollRef.value) return
    const index = content.value.findIndex((item) => item.id === id)
    const num = index
    const el = contentListRef.value[num]
    scrollTo(el, force)
  }
  const contentListRef = ref<Element[]>([])
  const { resume, pause } = useIntervalFn(flow([activeChange, scrollChange]), 200, {
    immediate: false
  })
  const onBlur = async (data: string, fn: () => boolean | void, validate?: boolean) => {
    resetActiveInput()
    if (validate && !validateTimeFormat(data)) return
    const res = fn()
    if (res === false) return
    updateResult()
  }
  return {
    contentListRef,
    resetActiveInput,
    inputFocus,
    playVideo,
    onBlur,
    resume,
    pause,
    inputRef,
    videoRef,
    scrollRef,
    content,
    active,
    inFocus,
    scrollChange,
    setContent,
    temporaryContent,
    waveSurferRef,
    selectLayer,
    deletedLines
  }
}
export const enum TagType {
  Token,
  Group
}
export interface TagListDataItem {
  label: string
  value: string
  type: TagType
}
export interface TagListItem {
  label: string
  type: TagType
  start: string
  end: string
  value: string
}
export const useMarkTag = () => {
  const selectionRange = ref({
    start: -1,
    end: -1
  })
  const selectionChoice = ref(false)
  const popoverRef = ref<PopoverInstance>()
  const selectTag = ref('')
  const tagType = computed<TagType>(() =>
    selectionRange.value.start === selectionRange.value.end ? TagType['Token'] : TagType['Group']
  )
  const tagListData: TagListDataItem[] = [
    {
      label: '笑声',
      value: '<laughter>',
      type: TagType.Token
    },
    { label: '停顿', value: '<block>', type: TagType.Token },
    { label: '呼吸', value: '<breathing>', type: TagType.Token },
    { label: '娇喘', value: '<panting>', type: TagType.Token },
    { label: '叹气', value: '<sigh>', type: TagType.Token },
    { label: '特殊声音', value: '<passion>', type: TagType.Token },
    { label: '重音', value: '<strong>', type: TagType.Group },
    { label: '拖音', value: '<prolong>', type: TagType.Group },
    { label: '耳语', value: '<whisper>', type: TagType.Group },
    { label: '重复', value: '<repeat>', type: TagType.Group },
    { label: '笑腔', value: '<laughspeak>', type: TagType.Group },
    { label: '娇喘说话', value: '<pantspeak>', type: TagType.Group },
    { label: '颤抖说话', value: '<trembling>', type: TagType.Group },
    { label: '亲吻', value: '<kiss>', type: TagType.Token }
  ]
  const tagReg = /([<])([a-zA-Z]+)([>])/
  const tagList = tagListData.map<TagListItem>((item) => {
    const start = item.value.replace(tagReg, '$1$2$3')
    const end = item.type === TagType['Group'] ? item.value.replace(tagReg, '$1/$2$3') : ''
    return {
      label: item.label,
      type: item.type,
      start,
      end,
      value: start + end
    }
  })

  const selectContent = ref<{
    startAfter?: string
    startBefore?: string
    endAfter?: string
    endBefore?: string
  }>({
    startAfter: undefined,
    startBefore: undefined,
    endAfter: undefined,
    endBefore: undefined
  })

  const popoverVisible = ref(false)
  return {
    selectionRange,
    selectionChoice,
    popoverRef,
    tagList,
    selectTag,
    popoverVisible,
    tagType,
    selectContent
  }
}
export const useSearch = (
  content: Ref<ContentItem[]>,
  scrollChange: (id?: string, force?: boolean) => void,
  updateResult: () => void
) => {
  const searchRef = ref<InputInstance>()
  const searchText = ref('')
  const replaceText = ref('')
  const totalSearchNum = ref(0)
  const currentSearchNum = ref(0)
  const searchIdList = ref<string[]>([])
  const initData = () => {
    searchIdList.value = []
    content.value.forEach((item) => (item.contentHtml = escape(item.content)))
    totalSearchNum.value = 0
    totalSearchNum.value = 0
    currentSearchNum.value = 0
  }
  const focusSearch = () => searchRef.value?.focus()
  const changeSearchText = () => {
    initData()
    if (!searchText.value) return
    const num = content.value.reduce((pre, now) => {
      const length = [...now.content.matchAll(new RegExp(searchText.value, 'g'))].length
      pre += length
      if (length) {
        Array.from({ length }).forEach(() => searchIdList.value.push(now.id))
        now.contentHtml = escape(now.content).replaceAll(
          searchText.value,
          `<span class='bg-search'>${searchText.value}</span>`
        )
      }
      return pre
    }, 0)
    totalSearchNum.value = num
    if (!totalSearchNum.value) {
      return
    }
    currentSearchNum.value = 1
    scrollChange(searchIdList.value[0])
    changeCurrentEl()
  }
  const changeCurrentSearchNum = (num: number) => {
    const min = 1
    const max = searchIdList.value.length
    num = num < min ? max : num
    num = num > max ? min : num
    currentSearchNum.value = num
    scrollChange(searchIdList.value[num - 1], true)
    changeCurrentEl()
  }
  const nextSearchNum = () => changeCurrentSearchNum(currentSearchNum.value + 1)
  const preSearchNum = () => changeCurrentSearchNum(currentSearchNum.value - 1)
  const changeCurrentEl = () => {
    let matchCount = 0
    content.value.forEach((item) => {
      // 删除旧的带border节点
      item.contentHtml = item.contentHtml.replaceAll(new RegExp('current-search', 'g'), '')
      // 添加新的带border元素
      if (matchCount >= currentSearchNum.value) return
      item.contentHtml = item.contentHtml.replace(new RegExp('bg-search', 'g'), (match) => {
        matchCount++
        return matchCount === currentSearchNum.value ? `${match} current-search` : match
      })
    })
  }
  const replaceAllTextFn = () => {
    content.value.forEach((item) => {
      item.content = item.content.replaceAll(new RegExp(searchText.value, 'g'), replaceText.value)
    })
    changeSearchText()
    updateResult()
  }
  const replaceTextFn = () => {
    let matchCount = 0
    content.value.forEach(
      (item) =>
        (item.content = item.content.replace(new RegExp(searchText.value, 'g'), (match) => {
          matchCount++
          return matchCount === currentSearchNum.value ? replaceText.value : match
        }))
    )
    changeSearchText()
    updateResult()
  }
  const clearInputFn = () => {
    searchText.value = ''
    replaceText.value = ''
    initData()
  }
  return {
    searchText,
    replaceText,
    totalSearchNum,
    currentSearchNum,
    searchIdList,
    searchRef,
    changeSearchText,
    changeCurrentSearchNum,
    replaceAllTextFn,
    replaceTextFn,
    clearInputFn,
    nextSearchNum,
    preSearchNum,
    focusSearch
  }
}
interface FnOptions {
  enter?: () => void
  shiftEnter?: () => void
}

export const boardEvent = (e: any, fnOptions: FnOptions) => {
  const event = e as KeyboardEvent
  if (event.key === 'Enter' && !event.shiftKey && event.keyCode === 13) {
    fnOptions.enter?.()
  } else if (event.key === 'Enter' && event.shiftKey && event.keyCode === 13) {
    fnOptions.shiftEnter?.()
  }
}
