import { Headset, VideoPause, VideoPlay } from '@element-plus/icons-vue'
import { ElButton, ElPopover, ElProgress, ElSlider } from 'element-plus'
import { defineComponent } from 'vue'

export default defineComponent({
  props: {
    duration: {
      type: Number,
      default: 0
    },
    currentTime: {
      type: Number,
      default: 0
    },
    modelValue: {
      type: String
    },
    volume: {
      type: Number,
      default: 0
    },
    src: {
      type: String
    },
    mediaRef: {
      type: Object as PropType<HTMLAudioElement>
    }
  },
  emits: ['play', 'pause', 'update:modelValue', 'update:volume', 'update:mediaRef'],
  setup(props, { emit }) {
    const audioState = useModel(props, 'modelValue')
    const mediaRef = useModel(props, 'mediaRef')
    const volume = useModel(props, 'volume')
    const formatSeconds = (seconds: number) => {
      // 计算分钟
      const minutes = Math.floor(seconds / 60)
      // 计算剩余的秒数
      const remainingSeconds = Math.floor(seconds % 60)
      // 格式化为两位数
      const formattedMinutes = String(minutes).padStart(2, '0')
      const formattedSeconds = String(remainingSeconds).padStart(2, '0')

      return `${formattedMinutes}:${formattedSeconds}`
    }
    const currentTime = computed(() => {
      return formatSeconds(props.currentTime)
    })
    const duration = computed(() => {
      return formatSeconds(props.duration)
    })
    const percentage = computed(() =>
      isNaN(props.currentTime / props.duration)
        ? 0
        : Math.floor((props.currentTime / props.duration) * 100)
    )
    const play = () => {
      audioState.value = 'play'
      emit('play')
    }
    const pause = () => {
      audioState.value = 'pause'
      emit('pause')
    }
    return () => (
      <>
        <video class="w-full" muted ref={mediaRef} src={props.src} />
        <div class="flex items-center">
          {audioState.value === 'pause' ? (
            <ElButton icon={VideoPlay} link onClick={play} size="large" type="primary">
              播放
            </ElButton>
          ) : (
            <ElButton icon={VideoPause} link onClick={pause} size="large" type="primary">
              暂停
            </ElButton>
          )}
          <ElProgress class="grow" duration={0} percentage={percentage.value} strokeWidth={14}>
            <div class="flex items-center">
              <div class="w-[110px]">
                {currentTime.value} / {duration.value}
              </div>
              <ElPopover popper-class="popoverMinWidth" trigger="click" width={'auto'}>
                {{
                  reference: () => <ElButton class="m-2" icon={Headset} link />,
                  default: () => <ElSlider height="100px" v-model={volume.value} vertical />
                }}
              </ElPopover>
            </div>
          </ElProgress>
        </div>
      </>
    )
  }
})
