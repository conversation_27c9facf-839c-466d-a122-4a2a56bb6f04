import useUserStore from '@/store/user'
// @ts-ignore next-line
import { useIDBKeyval } from '@vueuse/integrations/useIDBKeyval'
import { useRouteId } from '@znzt-fe/hooks'
import { beforeNextTick } from '@znzt-fe/utils'
import { ElMessage } from 'element-plus'
import { ModelRef } from 'vue'
import WaveSurfer from 'wavesurfer.js'
import RegionsPlugin, { Region } from 'wavesurfer.js/dist/plugins/regions'
import TimelinePlugin from 'wavesurfer.js/dist/plugins/timeline'
import { VideoReg } from '../../hook'
import { ContentItem, formatMilliseconds } from '../hooks'
const primaryColor = 'rgba(64,158,255,0.2)'
const topTimeline = TimelinePlugin.create({
  height: 18,
  insertPosition: 'beforebegin',
  timeInterval: 0.5,
  primaryLabelInterval: 1,
  secondaryLabelInterval: 1,
  style: {
    fontSize: '14px',
    color: '#f56c6c'
  }
})

export const useWaveSurfer = (
  audioSrc: Ref<string>,
  mediaRef: ModelRef<HTMLAudioElement, string | number | symbol>,
  content: Ref<ContentItem[] | undefined>,
  propsAddRegion: (id: string) => void,
  updateResult: () => void,
  audioId: string
) => {
  const playMediaType = computed(() => (VideoReg[0].test(audioSrc.value) ? 'video' : 'audio'))
  const playMode = ref<'click' | 'delay'>('click')
  const playModeOptions = [
    {
      label: '点击直接播放',
      value: 'click'
    },
    {
      label: '点击仅跳转位置',
      value: 'delay'
    }
  ]
  const playType = ref<'region' | 'global'>('region')
  const playTypeOptions = [
    {
      label: '点击区域重头播放',
      value: 'region'
    },
    {
      label: '点击任意位置播放',
      value: 'global'
    }
  ]
  const playRegionMode = ref<'regionStop' | 'global'>('regionStop')
  const playRegionModeOptions = [
    {
      label: '区域播放完毕停止',
      value: 'regionStop'
    },
    {
      label: '区域播放完毕继续播放',
      value: 'global'
    }
  ]
  const userStore = useUserStore()
  const regions = RegionsPlugin.create()
  const volume = ref(0)
  useEventListener(document, 'keydown', (event) => {
    if (event.key === 'Tab') {
      event.preventDefault() // 阻止默认的 Tab 键行为
      if (isLoading.value) return
      // 这里可以添加你自己的 Tab 键事件处理逻辑
      if (!pause()) {
        play()
      }
    }
  })
  const pause = () => {
    if (waveSurfer.value?.isPlaying()) {
      waveSurfer.value?.pause()
      if (playMediaType.value === 'video') {
        if (mediaRef.value?.pause) {
          mediaRef.value.pause()
        }
      }
      return true
    }
  }
  const audioState = ref<'play' | 'pause'>('pause')
  const play = async (data?: ContentItem) => {
    audioState.value = 'play'
    if (data) {
      const region = regions.getRegions().find((item) => item.id === data.id)
      activeRegion = region || void 0
      if (mediaRef.value?.pause) {
        mediaRef.value.pause()
        mediaRef.value.currentTime = region?.start || 0
      }
      waveSurfer.value?.pause()
      waveSurfer.value?.setTime(activeRegion?.start || 0)
    }
    let start = 0
    let end: number | undefined = 0
    if (playType.value === 'region') {
      start = waveSurfer.value?.getCurrentTime() || activeRegion?.start || 0
    } else if (playType.value === 'global') {
      start = waveSurfer.value?.getCurrentTime() || 0
    }
    if (playRegionMode.value === 'global') {
      end = void 0
    } else if (playRegionMode.value === 'regionStop') {
      if (!activeRegion) {
        const detail = content.value?.find((item) => item.end > start * 1000)
        if (detail) {
          activeRegion = regions.getRegions().find((item) => item.id === detail.id)
        }
      }
      end = activeRegion?.end || waveSurfer.value?.getDuration() || 0
      if (start > end) {
        const detail = content.value?.find((item) => item.end > start * 1000)
        if (detail) {
          activeRegion = regions.getRegions().find((item) => item.id === detail.id)
        }
      }
      end = activeRegion?.end || waveSurfer.value?.getDuration() || 0
    }
    end ? mediaPlay(start, end) : mediaPlay(start)
  }
  const mediaPlay = (start: number, end?: number) => {
    audioState.value = 'play'
    switch (playMediaType.value) {
      case 'video':
        end
          ? mediaRef.value.play().then(() => {
              waveSurfer.value?.play(start, end)
            })
          : mediaRef.value.play().then(() => {
              waveSurfer.value?.play(start)
            })
        break
      case 'audio':
        end ? waveSurfer.value?.play(start, end) : waveSurfer.value?.play(start)
        break
    }
  }
  const waveSurfer = ref<WaveSurfer>()
  const zoomRef = ref({
    min: 10,
    max: 10,
    waveZoom: 10
  })

  const baseId = useRouteId()
  const defaultValue = {
    id: `${baseId}-${audioId}`,
    loop: true,
    rangeStop: true,
    height: 400,
    sampleRate: 48000
  }
  const { data: form } = useIDBKeyval(`${baseId}-${audioId}`, defaultValue)
  const updateForm = () => {
    waveSurfer.value?.setOptions({
      height: form.value.height,
      sampleRate: form.value.sampleRate
    })
  }
  let activeRegion: Region | undefined
  const initRegions = async () => {
    regions.clearRegions()
    content.value?.forEach((item) => {
      regions.addRegion({
        start: item.start / 1000,
        end: item.end / 1000,
        content: '',
        color: primaryColor,
        drag: false,
        resize: true,
        id: item.id
      })
    })
    return true
  }
  let removeDetail = {} as ContentItem
  watchOnce(form, () => {
    if (form.value !== defaultValue) {
      waveSurfer.value?.setOptions({
        height: form.value.height,
        sampleRate: form.value.sampleRate
      })
    }
  })
  const removeData = (region: Region, start: number, end: number, id: string, splice = false) => {
    setTimeout(() => {
      region?.remove()
      // 目前 splice 为 true 的情况在拖动更新的时候，由于需要重新创建，则需要删除源数据，再进行计算边界，等created的时候再往content中重新填充
      if (splice) {
        const index = content.value!.findIndex((item) => item.id === id)
        // 如果移动的是当前region，则需要清空 activeRegion
        if (activeRegion === region) activeRegion = void 0
        const detailList = content.value!.splice(index, 1)
        removeDetail = detailList[0]
      }
      regions.addRegion({
        start: start! / 1000,
        end: end! / 1000,
        content: '',
        color: primaryColor,
        drag: false,
        resize: true,
        id
      })
    })
  }
  const getCurrentTime = () => waveSurfer.value?.getCurrentTime() || 0
  const initData = () => {
    regions.on('region-updated', async (region) => {
      if (!content.value) return
      const detail = content.value?.find((item) => region.id === item.id)
      if (!detail) return
      const regionStart = new Decimal(region.start).times(1000).toNumber()
      const regionEnd = new Decimal(region.end).times(1000).toNumber()
      const ceilStart = Math.ceil(regionStart)
      const floorEnd = Math.floor(regionEnd)
      // 修正拖拽生成时间区域时间保留小数位过多的问题
      if (regionStart !== ceilStart || regionEnd !== floorEnd) {
        removeData(region, ceilStart, floorEnd, detail.id, true)
        return
      }
      // 下边的逻辑大概率不会触发，由于修正时候会remove重新created，极小概率拖动的时间不需要修正
      for (let i = 0; i < content.value.length; i++) {
        if (content.value[i].id === region.id) continue
        if (
          regionStart < content.value[i].start &&
          regionEnd < content.value[i].end &&
          regionEnd > content.value[i].start
        ) {
          // 右侧溢出
          ElMessage.warning('数据区域不可产生重叠')
          removeData(region, detail.start, detail.end, detail.id, true)
          return
        }
        // 左侧溢出
        if (
          regionStart > content.value[i].start &&
          regionStart < content.value[i].end &&
          regionEnd > content.value[i].end
        ) {
          ElMessage.warning('数据区域不可产生重叠')
          removeData(region, detail.start, detail.end, detail.id, true)
          return
        }
        // 全部溢出
        if (regionStart < content.value[i].start && regionEnd > content.value[i].end) {
          ElMessage.warning('数据区域不可产生重叠')
          removeData(region, detail.start, detail.end, detail.id, true)
          return
        }
        if (regionStart > content.value[i].start && regionEnd < content.value[i].end) {
          ElMessage.warning('数据区域不可产生重叠')
          removeData(region, detail.start, detail.end, detail.id, true)
          return
        }
      }
      detail.start = new Decimal(region.start).times(1000).toNumber()
      detail.end = new Decimal(region.end).times(1000).toNumber()
      detail.startTime = formatMilliseconds(detail.start)
      detail.endTime = formatMilliseconds(detail.end)
      content.value = content.value.sort((a, b) => a.start - b.start)
      const id = region.id
      await nextTick()
      propsAddRegion(id)
      updateResult()
    })
    regions.on('region-created', async (region) => {
      const regionStart = new Decimal(region.start).times(1000).toNumber()
      const regionEnd = new Decimal(region.end).times(1000).toNumber()
      const ceilStart = Math.ceil(regionStart)
      const floorEnd = Math.floor(regionEnd)
      if (!content.value) {
        setTimeout(() => region.remove())
        return
      }
      // 修正拖拽生成时间区域时间精度过大的问题
      if (regionStart !== ceilStart || regionEnd !== floorEnd) {
        if (region.start === waveSurfer.value?.getDuration()) {
          return
        }
        removeData(region, ceilStart, floorEnd, region.id)
        return
      }
      if (content.value.find((item) => item.start === regionStart && item.end === regionEnd)) return
      let start: number | undefined, end: number | undefined, index: number | undefined
      const last = content.value[content.value.length - 1]
      const first = content.value[0]
      if (!last || !first) {
        index = 0
        start = regionStart
        end = regionStart
      }
      // 直接添加在最后
      else if (regionStart >= last.end) {
        index = content.value.length
        start = regionStart
        end = regionStart
      }
      // 直接添加在开头
      else if (regionEnd <= first.start) {
        index = 0
        start = regionStart
        end = regionStart
      }
      // 在中间需要判断是否越界
      else {
        let flag = true
        for (let i = 0; i < content.value.length; i++) {
          if (
            regionStart < content.value[i].start &&
            regionEnd < content.value[i].end &&
            regionEnd > content.value[i].start
          ) {
            start = regionStart
            end = content.value[i].start
            removeData(region, start, end, region.id)
            flag = false
            break
          }
          // 左侧溢出
          if (
            regionStart > content.value[i].start &&
            regionStart < content.value[i].end &&
            regionEnd > content.value[i].end
          ) {
            start = content.value[i].end
            end = regionEnd
            removeData(region, start, end, region.id)
            flag = false
            break
          }
          // 全部溢出
          if (regionStart < content.value[i].start && regionEnd > content.value[i].end) {
            start = regionStart
            end = content.value[i].start
            removeData(region, start, end, region.id)
            flag = false
            break
          }
        }
        // 不越界则添加
        if (flag) {
          for (let i = 0; i < content.value.length; i++) {
            if (regionStart >= content.value[i].end && regionEnd <= content.value?.[i + 1].start) {
              index = i + 1
              start = regionStart
              end = regionStart
              break
            }
          }
        } else {
          return
        }
      }
      if (typeof index === 'number' && typeof start === 'number' && typeof end === 'number') {
        if (!removeDetail.userManipulateLog?.length) {
          removeDetail.userManipulateLog = [
            {
              type: 'add',
              user: userStore.name,
              datetime: dayjs().format('YYYY-MM-DD HH:mm:ss')
            }
          ]
        } else {
          const editLog = removeDetail.userManipulateLog?.find(
            (item) => item.user === userStore.name && item.type === 'edit'
          )
          if (editLog) {
            editLog.datetime = dayjs().format('YYYY-MM-DD HH:mm:ss')
          } else {
            removeDetail.userManipulateLog.push({
              type: 'edit',
              user: userStore.name,
              datetime: dayjs().format('YYYY-MM-DD HH:mm:ss')
            })
          }
        }
        if (region.id === removeDetail?.id) {
          const contentInner = removeDetail.content
          const contentHtml = removeDetail.content
          content.value.splice(index, 0, {
            start: regionStart,
            startTime: formatMilliseconds(regionStart),
            tempStart: formatMilliseconds(regionStart),
            end: regionEnd,
            endTime: formatMilliseconds(regionEnd),
            tempEnd: formatMilliseconds(regionEnd),
            content: contentInner,
            contentHtml,
            id: region.id,
            deleted: false,
            userManipulateLog: removeDetail.userManipulateLog,
            questionResult: removeDetail.questionResult || {}
          })
          removeDetail = {} as ContentItem
        } else {
          content.value.splice(index, 0, {
            start: regionStart,
            startTime: formatMilliseconds(regionStart),
            tempStart: formatMilliseconds(regionStart),
            end: regionEnd,
            endTime: formatMilliseconds(regionEnd),
            tempEnd: formatMilliseconds(regionEnd),
            content: '',
            contentHtml: '',
            id: region.id,
            deleted: false,
            userManipulateLog: removeDetail.userManipulateLog,
            questionResult: removeDetail.questionResult || {}
          })
        }
        propsAddRegion(region.id)
      }
    })
    regions.on('region-clicked', (region, e) => {
      activeRegion = region
      if (playType.value === 'global') return
      e.stopPropagation()
      if (playMediaType.value === 'video') {
        if (mediaRef.value?.pause) {
          mediaRef.value.pause()
          mediaRef.value.currentTime = region.start
        }
      }
      waveSurfer.value?.pause()
      waveSurfer.value?.setTime(region.start)
      if (playMode.value === 'click') {
        setTimeout(() => {
          play()
        })
      }
    })
    waveSurfer.value!.on('interaction', () => {
      const now = waveSurfer.value!.getCurrentTime() * 1000
      const region = content.value?.find((item) => now >= item.start && now <= item.end)
      if (region) {
        activeRegion = regions.getRegions().find((item) => item.id === region.id)
      } else {
        activeRegion = void 0
      }
      if (playMediaType.value === 'video') {
        if (mediaRef.value?.pause) {
          mediaRef.value.pause()
          mediaRef.value.currentTime = waveSurfer.value!.getCurrentTime()
        }
      }
      if (playMode.value === 'delay') {
        waveSurfer.value?.pause()
        return
      }
      play()
    })
    waveSurfer.value!.on('timeupdate', (currentTime) => {
      currentTimeRef.value = currentTime
    })
  }
  const [isLoading, setIsLoading] = useToggle(true)
  onBeforeUnmount(() => {
    waveSurfer.value?.destroy()
  })
  watch(volume, (v) => {
    waveSurfer.value?.setVolume(v / 100)
  })
  const currentTimeRef = ref(0)
  const durationRef = ref(0)
  const createWaveSurfer = async () => {
    waveSurfer.value?.destroy()
    await nextTick()
    waveSurfer.value = WaveSurfer.create({
      container: '#waveform',
      minPxPerSec: 1000,
      height: form.value.height,
      sampleRate: form.value.sampleRate,
      normalize: true,
      waveColor: 'rgb(200, 0, 200)',
      progressColor: 'rgb(100, 0, 100)',
      url: audioSrc.value,
      backend: 'WebAudio',
      // mediaControls: true,
      plugins: [regions, topTimeline]
    })
    volume.value = waveSurfer.value.getVolume() * 100
    waveSurfer.value?.on('pause', () => {
      audioState.value = 'pause'
      if (activeRegion) {
        if (
          playRegionMode.value === 'regionStop' &&
          +waveSurfer.value!.getCurrentTime().toFixed(3) >= +activeRegion!.end.toFixed(3)
        ) {
          waveSurfer.value?.setTime(activeRegion!.start)
          if (playMediaType.value === 'video') {
            if (mediaRef.value?.pause) {
              mediaRef.value.pause()
              if (mediaRef.value.currentTime !== activeRegion!.start) {
                mediaRef.value.currentTime = activeRegion!.start
              }
            }
          }
        }
      }
    })
    waveSurfer.value.on('decode', () => {
      durationRef.value = waveSurfer.value?.getDuration() || 0
      initData()
      initRegions()
      setIsLoading(false)
      const duration = mediaRef.value.duration / 10
      zoomRef.value.max = duration > 100 ? duration : 100
      changeZoom(zoomRef.value.waveZoom)
      regions.enableDragSelection({
        color: primaryColor
      })
    })
  }
  whenever(audioSrc, beforeNextTick(createWaveSurfer), {
    immediate: true
  })
  const changeZoom = (zoom: number) => {
    waveSurfer.value!.zoom(zoom)
  }
  const removeRegion = (id: string) => {
    const region = regions.getRegions().find((item) => item.id === id)
    if (!region) {
      return
    }
    region.remove()
  }
  const updateRegion = (id: string) => {
    const region = regions.getRegions().find((item) => item.id === id)
    setTimeout(() => {
      region?.remove()
      const detail = content.value?.find((item) => id === item.id)
      if (!detail) return
      regions.addRegion({
        start: detail.start! / 1000,
        end: detail.end! / 1000,
        content: '',
        color: primaryColor,
        drag: false,
        resize: true,
        id
      })
    })
  }
  const addRegion = (id: string) => {
    const detail = content.value?.find((item) => id === item.id)
    if (!detail) return
    regions.addRegion({
      start: detail.start! / 1000,
      end: detail.end! / 1000,
      content: '',
      color: primaryColor,
      drag: false,
      resize: true,
      id: detail.id
    })
  }
  return {
    createWaveSurfer,
    zoomRef,
    changeZoom,
    initRegions,
    isLoading,
    form,
    play,
    updateRegion,
    removeRegion,
    addRegion,
    updateForm,
    currentTimeRef,
    durationRef,
    waveSurfer,
    playType,
    playTypeOptions,
    playRegionMode,
    playRegionModeOptions,
    playMode,
    playModeOptions,
    audioState,
    pause,
    volume,
    getCurrentTime
  }
}
