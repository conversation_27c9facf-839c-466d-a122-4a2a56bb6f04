import SingleCheckbox from '@/components/single-checkbox'
import { Fn } from '@znzt-fe/declare'
import { ElForm, ElFormItem, ElInputNumber, ElSlider } from 'element-plus'
import { PropType } from 'vue'
import { VideoReg } from '../../hook'
import { ContentItem } from '../hooks'
import AudioPlayer from './audioPlayer'
import { useWaveSurfer } from './hooks'
import VideoPlayer from './videoPlayer'
export default defineComponent({
  props: {
    audioId: {
      type: String,
      default: ''
    },
    src: {
      type: String,
      default: ''
    },
    onPause: {
      type: Function as PropType<Fn>
    },
    onPlay: {
      type: Function as PropType<Fn>
    },
    modelValue: {
      type: Object as PropType<HTMLAudioElement>,
      default: () => ({})
    },
    content: Array as PropType<ContentItem[]>,
    addRegion: {
      type: Function as PropType<(index: string) => void>
    },
    updateResult: {
      type: Function as PropType<() => void>
    }
  },
  emits: ['update:modelValue', 'update:questionCaption'],
  setup(props, { expose }) {
    expose({
      initRegions: () => initRegions(),
      removeRegion: (id: string) => removeRegion(id),
      updateRegion: (id: string) => updateRegion(id),
      addRegion: (id: string) => addRegion(id),
      play: (data: ContentItem) => play(data),
      getCurrentTime: () => getCurrentTime(),
      pause: () => pause()
    })
    const mediaRef = useModel(props, 'modelValue')
    const content = useModel(props, 'content')
    const { src } = toRefs(props)
    const {
      zoomRef,
      changeZoom,
      initRegions,
      isLoading,
      form,
      play,
      removeRegion,
      updateRegion,
      addRegion,
      updateForm,
      currentTimeRef,
      durationRef,
      playType,
      playTypeOptions,
      playRegionMode,
      playRegionModeOptions,
      playMode,
      playModeOptions,
      audioState,
      pause,
      volume,
      getCurrentTime
    } = useWaveSurfer(src, mediaRef, content, props.addRegion!, props.updateResult!, props.audioId)
    watch(audioState, (v) => {
      if (v === 'play') {
        props.onPlay?.()
      } else {
        props.onPause?.()
      }
    })
    return () => (
      <div v-loading={isLoading.value}>
        <div id="waveform" />
        <div class="px-3">
          <ElSlider
            max={zoomRef.value.max}
            min={zoomRef.value.min}
            onInput={(v) => changeZoom(v as number)}
            showTooltip={false}
            v-model={zoomRef.value.waveZoom}
          />
        </div>
        {VideoReg[0].test(src.value) ? (
          <VideoPlayer
            currentTime={currentTimeRef.value}
            duration={durationRef.value}
            onPause={() => {
              props.onPause?.()
              pause()
            }}
            onPlay={() => {
              props.onPlay?.()
              play()
            }}
            src={src.value}
            v-model={audioState.value}
            v-model:mediaRef={mediaRef.value}
            v-model:volume={volume.value}
          />
        ) : (
          <AudioPlayer
            currentTime={currentTimeRef.value}
            duration={durationRef.value}
            onPause={() => {
              props.onPause?.()
              pause()
            }}
            onPlay={() => {
              props.onPlay?.()
              play()
            }}
            v-model={audioState.value}
            v-model:volume={volume.value}
          />
        )}
        <ElForm>
          <ElFormItem label="播放方式">
            <SingleCheckbox
              default
              disabled={audioState.value === 'play'}
              options={playModeOptions}
              v-model={playMode.value}
            />
          </ElFormItem>
          <ElFormItem label="播放类型">
            <SingleCheckbox
              default
              disabled={audioState.value === 'play'}
              options={playTypeOptions}
              v-model={playType.value}
            />
          </ElFormItem>
          <ElFormItem label="区域播放方式">
            <SingleCheckbox
              default
              disabled={audioState.value === 'play'}
              options={playRegionModeOptions}
              v-model={playRegionMode.value}
            />
          </ElFormItem>
          <ElFormItem label="波形图整体高度（px）">
            <ElInputNumber
              max={1000}
              min={200}
              onChange={updateForm}
              precision={0}
              v-model={form.value.height}
            />
          </ElFormItem>
          <ElFormItem label="采样率">
            <ElInputNumber
              max={48000}
              min={8000}
              onChange={updateForm}
              precision={0}
              step={1000}
              step-strictly
              v-model={form.value.sampleRate}
            />
          </ElFormItem>
        </ElForm>
      </div>
    )
  }
})
