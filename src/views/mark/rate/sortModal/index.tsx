import { MarktaskdetailItem } from '@/api/marktask/type'
import { ElCard, ElDialog } from 'element-plus'
import Draggable from 'vuedraggable'
import style from './index.module.less'
import { DragableItem, UpdateResult, useDrag, useList } from './hooks'
import DetailModal from './components/detailModal'
export default defineComponent({
  props: {
    apiResultList: {
      type: Array as PropType<MarktaskdetailItem[]>,
      default: () => []
    },
    score: {
      type: Number,
      default: 3
    },
    updateResult: Function as PropType<UpdateResult>,
    modelValue: Boolean,
    isShowSource: Boolean
  },
  emits: ['update:modelValue'],
  setup(props) {
    const { score, isShowSource, apiResultList: apiResultListProps } = toRefs(props)
    const visible = useModel(props, 'modelValue')
    const { ratedList, unratedList } = useList(score, visible, apiResultListProps)
    const { config, MoveCard, detailVisible, apiResult } = useDrag(
      isShowSource,
      props.updateResult!
    )
    return () => (
      <ElDialog class={style['container']} fullscreen v-model={visible.value}>
        <div class={style['delete-padding']}>
          <ElCard>
            {{
              default: () => {
                const { onChange, ...rest } = config.value
                const attr = { ...rest, onChange: onChange(0) }
                return (
                  <Draggable
                    {...attr}
                    style={{
                      display: 'flex',
                      height: '150px'
                    }}
                    v-model={unratedList.value}>
                    {{
                      item: ({ element: row }: DragableItem) => (
                        <div>
                          <MoveCard row={row} style={{ marginRight: '6px' }} />
                        </div>
                      )
                    }}
                  </Draggable>
                )
              },
              header: () => '待评分'
            }}
          </ElCard>
          <div class={style['card-list']}>
            {ratedList.value.map((_item, index) => (
              <ElCard key={index}>
                {{
                  header: () => `${index + 1}分`,
                  default: () => {
                    const { onChange, ...rest } = config.value
                    const attr = { ...rest, onChange: onChange(index + 1) }
                    return (
                      <Draggable
                        {...attr}
                        style={{ minHeight: '240px', width: '200px' }}
                        v-model={ratedList.value[index]}>
                        {{
                          item: ({ element: row }: DragableItem) => (
                            <div>
                              <MoveCard row={row} style={{ marginBottom: '8px' }} />
                            </div>
                          )
                        }}
                      </Draggable>
                    )
                  }
                }}
              </ElCard>
            ))}
          </div>
        </div>
        <DetailModal apiResult={apiResult.value} v-model={detailVisible.value} />
      </ElDialog>
    )
  }
})
