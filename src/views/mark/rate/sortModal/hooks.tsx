import { MarktaskdetailItem, ResultUpdateParams } from '@/api/marktask/type'
import style from './index.module.less'
import { cloneDeep } from 'lodash-es'
import { getSpeechText } from '../hook'
import { ElButton, ElCard, ElDivider } from 'element-plus'
import { ValueOf } from '@znzt-fe/declare'
import { useElModal } from '@znzt-fe/hooks'
import { CSSProperties } from 'vue'
export type UpdateResult = ({
  key,
  value,
  markTaskDetailId
}: {
  key: keyof ResultUpdateParams
  value: ValueOf<ResultUpdateParams>
  markTaskDetailId: number
}) => void

export interface DragableItem {
  element: MarktaskdetailItem
}
interface CardDetailProps {
  row: MarktaskdetailItem
}
export const useList = (
  score: Ref<number>,
  visible: Ref<boolean>,
  apiResultListProps: Ref<MarktaskdetailItem[]>
) => {
  const ratedList = ref<MarktaskdetailItem[][]>([])
  const unratedList = ref<MarktaskdetailItem[]>([])
  whenever(visible, () => {
    const ratedArr: MarktaskdetailItem[][] = Array.from({ length: score.value }).map(() => [])
    const unratedArr: MarktaskdetailItem[] = []
    apiResultListProps.value.forEach((item) =>
      !item.score ? unratedArr.push(item) : ratedArr[item.score - 1].push(item)
    )
    ratedList.value = cloneDeep(ratedArr)
    unratedList.value = cloneDeep(unratedArr)
  })

  return {
    ratedList,
    unratedList
  }
}

export const useDrag = (isShowSource: Ref<boolean>, updateResult: UpdateResult) => {
  const [disable, setDisable] = useToggle(false)
  const { openModal, detailVisible, apiResult } = useModal()
  const CardDetail = ({ row }: CardDetailProps) => (
    <>
      <div>id:{row.id}</div>
      <ElDivider style={{ margin: '2px 0' }} />
      <div
        class={style.text}
        onMouseenter={() => setDisable(true)}
        onMouseleave={() => setDisable(false)}>
        <div class={style['text-clamp']}>{getSpeechText(row.apiResult)}</div>
        <div class="flex-just-end">
          <ElButton link onClick={() => openModal(row.apiResult)} type="primary">
            详情
          </ElButton>
        </div>
      </div>

      <div class="flex-just-end ">字数：{row.apiResult.length}</div>
      {isShowSource.value && <div class="flex-just-end ">模型：{row.source}</div>}
    </>
  )
  const config = computed(() => ({
    class: style['padding'],
    disabled: disable.value,
    forceFallback: true,
    ghostClass: style.ghost,
    group: 'group',
    'item-key': 'id',
    onChange: (score: number) => (event: { added?: DragableItem }) =>
      event.added &&
      updateResult({ key: 'score', value: score, markTaskDetailId: event.added.element.id })
  }))
  const MoveCard = ({ row, style: styleAttr }: CardDetailProps & { style: CSSProperties }) => (
    <ElCard class={style['card-move']} style={styleAttr}>
      <CardDetail row={row} />
    </ElCard>
  )
  return {
    config,
    detailVisible,
    apiResult,
    MoveCard
  }
}

const useModal = () => {
  const { visible, openModal: openElModal } = useElModal()
  const apiResult = ref('')
  const openModal = (text: string) => {
    apiResult.value = text
    openElModal()
  }
  return {
    detailVisible: visible,
    openModal,
    apiResult
  }
}
