import { ElDialog } from 'element-plus'
import AssistantMessage from '../../assistantMessage'
import { getSpeechTag } from '../../hook'
export default defineComponent({
  props: {
    modelValue: Boolean,
    apiResult: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue'],
  setup(props) {
    const visible = useModel(props, 'modelValue')
    const text = toRef(props, 'apiResult')
    return () => (
      <ElDialog v-model={visible.value}>
        {visible.value && (
          <>
            <AssistantMessage text={text.value} />
            {getSpeechTag(text.value)}
          </>
        )}
      </ElDialog>
    )
  }
})
