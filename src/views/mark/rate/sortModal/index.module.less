.padding {
  padding: 10px 14px;
}
.container {
  min-width: 800px;
  .delete-padding {
    :global {
      .el-card__body {
        padding: 0px;
      }
    }
  }
  :global {
    .el-card {
      transition: none !important;
    }
  }
  .card-move {
    width: 200px;
    height: 150px;
    overflow: auto;
    cursor: move;
    .padding();
    box-sizing: border-box;
  }
  .text {
    cursor: text;
  }
  .ghost {
    opacity: 0.5;
  }
  .card-list {
    margin-top: 10px;
    .flex(flex-start,flex-start);
    :global {
      .el-card {
        flex-shrink: 0;
        &:not(:last-child) {
          margin-right: 8px;
        }
      }
    }
    white-space: nowrap;
    overflow: auto;
  }
}

.text-clamp {
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  overflow: hidden;
  -webkit-box-orient: vertical;
}
