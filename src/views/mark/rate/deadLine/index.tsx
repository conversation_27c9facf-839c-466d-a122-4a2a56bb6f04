import { ElTag } from 'element-plus'
import { useTickTime } from './hooks'

export default defineComponent({
  props: {
    endTime: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const { endTime } = toRefs(props)
    const { time } = useTickTime(endTime)
    return () =>
      endTime.value === '0' ? null : time.value ? (
        <ElTag
          disableTransitions
          effect="plain"
          style={{ width: '250px' }}
          type={time.value.totalHours < 1 ? 'warning' : 'success'}>
          {`剩余时间：${time.value?.days} 天 ${time.value?.hours} 时 ${time.value?.minutes} 分 ${time.value?.seconds} 秒`}
        </ElTag>
      ) : (
        <ElTag disableTransitions type="danger">
          已结束
        </ElTag>
      )
  }
})
