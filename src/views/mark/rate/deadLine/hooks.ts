import duration from 'dayjs/plugin/duration'
dayjs.extend(duration)
export const useTickTime = (endTime: Ref<string | number | undefined>) => {
  const time = ref<{
    days: number
    hours: number
    minutes: number
    seconds: number
    totalHours: number
  } | null>(null)
  const [flag, setFlag] = useToggle(true)
  const start = () => {
    if (!flag.value) return
    requestAnimationFrame(() => {
      const date = computedTime()
      if (!date) {
        time.value = null
        return
      }
      time.value = date
      start()
    })
  }
  invoke(async () => {
    await until(endTime).toBeTruthy()
    start()
  })
  onBeforeUnmount(() => setFlag(false))
  const computedTime = () => {
    const duration = dayjs.duration(dayjs(endTime.value, 'X').diff(dayjs()))
    const days = Math.floor(duration.asDays())
    if (days < 0) return
    const hours = duration.hours()
    if (hours < 0) return
    const minutes = duration.minutes()
    if (minutes < 0) return
    const seconds = duration.seconds()
    if (seconds < 0) return
    const totalHours = Math.floor(duration.asHours())
    return {
      days,
      hours,
      minutes,
      seconds,
      totalHours
    }
  }
  return {
    start,
    time
  }
}
