import { getMarkEditHistory } from '@/api/marktask'
import {
  Capt<PERSON>,
  HistoryField,
  MarkEditHistoryItem,
  MarkEditHistoryRet,
  MultiChoice,
  NumberConfigType,
  Question,
  QuestionType
} from '@/api/marktask/type'
import { arrayToNumber, stringToNumber } from '@/utils'
import { useList, useRouteId } from '@znzt-fe/hooks'
import { generateTableList } from '@znzt-fe/utils'
import {
  ElCheckbox,
  ElCheckboxGroup,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElOption,
  ElRadio,
  ElRadioGroup,
  ElRate,
  ElSelect,
  ElSwitch,
  ElTag,
  RenderRowData
} from 'element-plus'
import { JSX } from 'vue/jsx-runtime'
import { gsbOption, markTaskScoreTypeToggle } from '../../util'
import { parseMultipleChoice } from '../hook'
import style from '../index.module.less'
import MarkMessage from '../markMessage'
const groupConfig = {
  class: 'flex-wrap'
}
export const useTable = () => {
  const markTaskId = useRouteId()
  const {
    listParams,
    isLoading,
    refetchData,
    mutate: getMarkEditHistoryMutate
  } = useList<MarkEditHistoryItem, MarkEditHistoryRet>({
    getList: getMarkEditHistory,
    query: {
      markTaskId
    },
    mountedQuery: false
  })
  const tableColumn: Ref<JSX.Element[]> = ref(
    generateTableList<MarkEditHistoryItem>([
      {
        prop: 'markTaskDetailId',
        label: 'id',
        width: 100
      },
      {
        prop: 'editUname',
        label: '编辑者',
        width: 120
      },
      {
        prop: 'id',
        label: '题目名称',
        slots: (scope) => {
          switch (scope.row.field) {
            case HistoryField.Score:
              return listParams.other.config.isSort ? '排序' : '评分'
            case HistoryField.Remark:
              return '备注'
            case HistoryField.GSB:
              return 'gsb评分'
            case HistoryField.CommentLabelContent:
              return '标注标签'
            case HistoryField.Question:
              return getQuestionDetail(listParams.other.config.questions, scope)?.name
            default:
              return '——'
          }
        }
      },
      {
        prop: 'oldValue',
        label: '旧版本',
        resizable: true,
        slots: (scope) => {
          const value = scope.row.oldValue
          switch (scope.row.field) {
            case HistoryField.CommentLabelContent:
              return (
                <MarkMessage
                  commentLabelContent={value}
                  disabled={true}
                  text={listParams.other.config.apiResultMap[scope.row.markTaskDetailId]}
                />
              )
            case HistoryField.Score:
              return listParams.other.config.isSort ? (
                <ElRate
                  allowHalf
                  class={style['sticky']}
                  disabled={true}
                  max={listParams.other.config.score}
                  modelValue={+value}
                />
              ) : (
                <ElRate
                  allowHalf
                  class={style['sticky']}
                  disabled={true}
                  max={markTaskScoreTypeToggle(listParams.other.config.scoreType)}
                  modelValue={+value}
                />
              )
            case HistoryField.GSB:
              return (
                <>
                  {value.name}
                  <ElRadioGroup {...groupConfig} modelValue={value.value ?? ''}>
                    {gsbOption.map((option) => (
                      <ElRadio key={option.value} value={option.value}>
                        {option.label}
                      </ElRadio>
                    ))}
                  </ElRadioGroup>
                </>
              )
            case HistoryField.Remark:
              return (
                <ElInput
                  class={cx(style['sticky'], style['text-area'])}
                  modelValue={value}
                  readonly
                  type="textarea"
                />
              )
            case HistoryField.Question:
              return getSlotsElement(listParams.other.config.questions, scope, 'oldValue')
            default:
              return '——'
          }
        }
      },
      {
        prop: 'newValue',
        label: '新版本',
        resizable: true,
        slots: (scope) => {
          const value = scope.row.newValue
          switch (scope.row.field) {
            case HistoryField.CommentLabelContent:
              return (
                <MarkMessage
                  commentLabelContent={value}
                  disabled={true}
                  text={listParams.other.config.apiResultMap[scope.row.markTaskDetailId]}
                />
              )
            case HistoryField.Score:
              return listParams.other.config.isSort ? (
                <ElRate
                  allowHalf
                  class={style['sticky']}
                  disabled={true}
                  max={listParams.other.config.score}
                  modelValue={+value}
                />
              ) : (
                <ElRate
                  allowHalf
                  class={style['sticky']}
                  disabled={true}
                  max={markTaskScoreTypeToggle(listParams.other.config.scoreType)}
                  modelValue={+value}
                />
              )
            case HistoryField.GSB:
              return (
                <>
                  {value.name}
                  <ElRadioGroup {...groupConfig} modelValue={value.value ?? ''}>
                    {gsbOption.map((option) => (
                      <ElRadio key={option.value} value={option.value}>
                        {option.label}
                      </ElRadio>
                    ))}
                  </ElRadioGroup>
                </>
              )
            case HistoryField.Remark:
              return (
                <ElInput
                  class={cx(style['sticky'], style['text-area'])}
                  modelValue={value}
                  readonly
                  type="textarea"
                />
              )
            case HistoryField.Question:
              return getSlotsElement(listParams.other.config.questions, scope, 'newValue')
            default:
              return '——'
          }
        }
      },
      {
        prop: 'updateTime',
        label: '更新时间',
        width: 170,
        slots: (scope) => {
          return dayjs.unix(scope.row.updateTime).format('YYYY-MM-DD HH:mm:ss')
        }
      }
    ])
  )
  return {
    listParams,
    isLoading,
    refetchData,
    getMarkEditHistoryMutate,
    tableColumn
  }
}

const getQuestionDetail = (
  questions: Question[],
  scope: RenderRowData<MarkEditHistoryItem>,
  key: keyof MarkEditHistoryItem = 'oldValue'
) => {
  const questionDetail = questions.find((item) => +item.id === +Object.keys(scope.row[key])[0])
  return questionDetail
}
const getSlotsElement = (
  questions: Question[],
  scope: RenderRowData<MarkEditHistoryItem>,
  key: keyof MarkEditHistoryItem
) => {
  const questionDetail = getQuestionDetail(questions, scope, key)
  const slotElementSlots = {
    [QuestionType.single]: () => (
      <ElRadioGroup
        {...groupConfig}
        modelValue={arrayToNumber(scope.row[key][questionDetail!.id]) ?? ''}>
        {questionDetail?.options.map((option) => (
          <ElRadio key={option.id} value={option.id}>
            {option.name}
          </ElRadio>
        ))}
      </ElRadioGroup>
    ),
    [QuestionType.multiple]: () => {
      const val = scope.row[key][questionDetail!.id] as number[]
      const arr = Object.entries(
        questionDetail!.options.reduce<Record<string, { name: string; id: number }[]>>(
          (pre, now) => {
            const nameArr = now.name
              .replaceAll('//', '$$$')
              .split('/')
              .map((item) => item.replaceAll('$$', '/'))
            const key = nameArr.shift() as keyof typeof pre
            if (!pre[key]) {
              pre[key] = []
            }
            pre[key].push({ name: nameArr.join(','), id: now.id })
            return pre
          },
          {}
        )
      )
      const isNormal = arr.every((item) => item[1].length === 1)
      return isNormal ? (
        <ElCheckboxGroup {...groupConfig} modelValue={val}>
          {questionDetail?.options.map((option) => (
            <ElCheckbox key={option.id} value={option.id}>
              {option.name}
            </ElCheckbox>
          ))}
        </ElCheckboxGroup>
      ) : (
        arr.map((itemArr) => (
          <div key={itemArr[0]}>
            <div>
              {itemArr[0]}
              {val.some((item) => itemArr[1].map((item) => item.id).includes(item)) ? (
                <ElTag class="ml-2" disableTransitions>
                  已标注
                </ElTag>
              ) : (
                <ElTag class="ml-2" disableTransitions>
                  未标注
                </ElTag>
              )}
            </div>
            {itemArr[1].map((items) => {
              return (
                <ElCheckbox key={items.id} modelValue={val.includes(items.id)} value={items.id}>
                  {items.name}
                </ElCheckbox>
              )
            })}
          </div>
        ))
      )
    },
    // (
    //   <ElCheckboxGroup {...groupConfig} modelValue={scope.row[key][questionDetail!.id]}>
    //     {questionDetail?.options.map((option) => (
    //       <ElCheckbox key={option.id} value={option.id}>
    //         {option.name}
    //       </ElCheckbox>
    //     ))}
    //   </ElCheckboxGroup>
    // )
    [QuestionType.number]: () => {
      const { type, max, min, percision } = questionDetail!.numberConfig
      const numberConfigElement = {
        [NumberConfigType.input]: () => (
          <ElInputNumber
            class="input-number"
            controls={false}
            max={max}
            min={min}
            modelValue={stringToNumber(scope.row[key][questionDetail!.id])}
            precision={percision}
            readonly
          />
        ),
        [NumberConfigType.select]: () => {
          const options = Array.from({ length: max - min + 1 }, (_, i) => i + min + '')
          return (
            <ElSelect modelValue={scope.row[key][questionDetail!.id]}>
              {options.map((option) => (
                <ElOption key={option} label={option} value={option} />
              ))}
            </ElSelect>
          )
        }
      }
      return numberConfigElement[type]()
    },
    [QuestionType.subjective]: () => {
      const { class: className } = groupConfig
      return (
        <ElInput
          class={cx(className, style['text-area'])}
          readonly
          type="textarea"
          v-model={scope.row[key][questionDetail!.id]}
        />
      )
    },
    [QuestionType.multipleChoice]: () => {
      const { max, min, percision } = questionDetail!.numberConfig
      return (
        <>
          {questionDetail!.options.map((option, index) => (
            <div class="flex items-center" key={questionDetail!.id}>
              <ElInputNumber
                class="shrink-0"
                controls={false}
                max={max}
                min={min}
                modelValue={parseMultipleChoice(
                  (scope.row[key][questionDetail!.id] as MultiChoice[])?.find(
                    (item) => option.id === item.index
                  )?.score
                )}
                precision={percision}
                readonly
                style={{
                  width: '60px',
                  marginRight: '6px',
                  marginTop: index === 0 ? '0px' : '6px'
                }}
              />
              <span class="grow overflow-auto">{option.name}</span>
            </div>
          ))}
        </>
      )
    },
    [QuestionType.captions]: () => {
      const result = scope.row[key][questionDetail!.id] as Caption
      return (
        <div>
          <ElFormItem class="!mb-0" label="标注完成">
            <ElSwitch modelValue={result.confirm} />
          </ElFormItem>
          <ElFormItem class="!mb-0" label="字幕JSON">
            {result.layers.map((item, index) => (
              <div class="overflow-auto" key={index}>
                {JSON.stringify(item.lines)}
              </div>
            ))}
          </ElFormItem>
        </div>
      )
    }
  }

  return <div class={style['sticky']}>{slotElementSlots[questionDetail!.type]?.()}</div>
}
