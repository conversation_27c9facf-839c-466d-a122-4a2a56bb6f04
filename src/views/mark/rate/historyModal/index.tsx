import { ElDialog, ElTable } from 'element-plus'
import { useTable } from './hooks'
import style from '../index.module.less'
import { Pagination } from '@znzt-fe/components'
export default defineComponent({
  props: {
    modelValue: Boolean
  },
  emits: ['update:modelValue'],
  setup(props) {
    const visible = useModel(props, 'modelValue')
    const { listParams, tableColumn, refetchData, getMarkEditHistoryMutate } = useTable()
    whenever(visible, refetchData)
    return () => (
      <ElDialog fullscreen lockScroll title="历史记录" v-model={visible.value} width={810}>
        <ElTable
          border
          cellStyle={{ height: '100%' }}
          class={style['table']}
          data={listParams.list}
          height="100%"
          // ref={tableRef}
          rowKey="id"
          // spanMethod={objectSpanMethod}
          style={{ width: '100%', height: '100%' }}>
          {tableColumn.value}
        </ElTable>
        <Pagination
          onRefresh={getMarkEditHistoryMutate}
          small
          total={listParams.pageInfo.total}
          v-model:pageNum={listParams.pageInfo.pageNum}
          v-model:pageSize={listParams.pageInfo.pageSize}
        />
      </ElDialog>
    )
  }
})
