import { useGetChartsSelfOptions, useGetMarkSelfChart } from '@/api/marktask'
import { MarkSelfChartListItem, MarkTaskChartMode, MarktaskChartRet } from '@/api/marktask/type'

export const useForm = () => {
  const { ids } = useIds()
  const filter = {
    startCorrectRate: 0,
    endCorrectRate: 100,
    startHitRate: 0,
    endHitRate: 100
  }

  const state = reactive({
    mode: MarkTaskChartMode.DataSetId,
    dataSets: []
  })
  const averageData = ref<
    Array<{
      chartData: Pick<MarktaskChartRet, 'category' | 'series'>
      list: MarkSelfChartListItem[]
    }>
  >([])

  const getAllChartsData = () => {
    const data = {
      ...filter,
      mode: state.mode,
      dataSets: state.dataSets,
      markTaskIds: ids
    }
    mutate(data)
  }
  const { mutate } = useGetMarkSelfChart({
    onSuccess(data) {
      const { list = [] } = data[0]
      averageData.value = data.map((item) => ({
        chartData: {
          category: item.category || [],
          series: item.series || []
        },
        list: list
      }))
      const series = averageData.value[1].chartData.series.reverse()
      averageData.value[1].chartData.series = series
    }
  })
  onMounted(getAllChartsData)
  return {
    getAllChartsData,
    state,
    averageData
  }
}

export const useOptions = () => {
  const { ids } = useIds()
  const { mutate: getOptions, data: chartsSelfOptions } = useGetChartsSelfOptions()
  onMounted(() => getOptions({ markTaskIds: ids }))
  const mode = ref(Mode.Chart)
  return {
    chartsSelfOptions,
    mode
  }
}
export const enum Mode {
  Chart = 1,
  Table
}
export const useIds = () => {
  const route = useRoute()
  const params = route.params
  const ids = params.id ? [+params.id] : (route.query.ids as string[]).map((item) => +item)
  return { ids }
}
