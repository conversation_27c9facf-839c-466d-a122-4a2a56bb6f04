import ResultBack from '../components/result-back'
import ChartOrList from '../outer-result/chart-or-list.vue'
import { ChartType } from '../outer-result/type'
import { ElCard, ElForm, ElFormItem, ElOption, ElSelect } from 'element-plus'
import { Mode, useForm, useIds, useOptions } from './hooks'
export default defineComponent({
  setup() {
    const { averageData, getAllChartsData, state } = useForm()
    const { mode, chartsSelfOptions } = useOptions()
    const { ids } = useIds()
    return () => (
      <div>
        <ResultBack v-model={mode.value} />
        <ElCard>
          <div style={{ fontSize: '16px' }}>
            {mode.value === Mode.Table ? '结果统计' : '全量结果统计'}
          </div>
          {!!averageData.value.length &&
            (mode.value === Mode.Table ? (
              <div class="flex-just-space">
                <ChartOrList ids={ids} mode={mode.value} type={4} value={averageData.value[0]} />
                <ChartOrList ids={ids} mode={mode.value} type={5} value={averageData.value[0]} />
              </div>
            ) : (
              <>
                <ChartOrList
                  chartType={ChartType.Double}
                  ids={ids}
                  mode={mode.value}
                  type={4}
                  value={averageData.value[0]}
                />
              </>
            ))}
        </ElCard>
        {mode.value === Mode.Chart && (
          <ElCard style={{ marginTop: '10px' }}>
            <div style={{ fontSize: '16px' }}>子任务统计</div>
            <ElForm inline label-width="70px" model={state} style={{ marginTop: '24px' }}>
              <ElFormItem class="!w-[240px]" label="聚合" prop="mode">
                <ElSelect onChange={getAllChartsData} v-model={state.mode}>
                  {chartsSelfOptions.value?.modes.map((item) => (
                    <ElOption key={item.id} label={item.name} value={item.id} />
                  ))}
                </ElSelect>
              </ElFormItem>
              <ElFormItem class="!w-[240px]" label="数据集" prop="dataSets">
                <ElSelect filterable multiple onChange={getAllChartsData} v-model={state.dataSets}>
                  {chartsSelfOptions.value?.dataSets.map((item) => (
                    <ElOption key={item.id} label={item.name} value={item.id} />
                  ))}
                </ElSelect>
              </ElFormItem>
            </ElForm>
            {!!averageData.value.length && (
              <ChartOrList
                chartType={ChartType.Radar}
                ids={ids}
                mode={Mode.Chart}
                type={4}
                value={averageData.value[1]}
              />
            )}
          </ElCard>
        )}
      </div>
    )
  }
})
