<template>
  <section class="page-header">
    <Back></Back>
    <el-switch
      v-model="state.mode"
      size="large"
      :active-value="1"
      :inactive-value="2"
      active-text="图模式"
      inactive-text="表格模式" />
  </section>
  <el-main class="content-container">
    <el-form :model="state.filter" label-width="70px" inline v-if="isCommonMode">
      <el-form-item label="聚合" prop="mode">
        <el-select v-model="state.filter.mode" filterable>
          <el-option
            v-for="mode in state.options.modes"
            :key="mode.id"
            :label="mode.name"
            :value="mode.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="模型" prop="models">
        <el-select v-model="state.filter.models" multiple :filterable="true">
          <el-option
            v-for="model in state.options.models"
            :key="model"
            :label="model"
            :value="model" />
        </el-select>
      </el-form-item>
      <el-form-item label="数据集" prop="dataSets">
        <el-select v-model="state.filter.dataSets" multiple :filterable="true">
          <el-option
            v-for="item in state.options.dataSets"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="命中率" prop="dataSets">
        <div class="!flex-nowrap">
          <el-input-number
            v-model="state.filter.startHitRate"
            :max="state.filter.endHitRate || 100"
            :controls="false"
            :min="0" />
          <span>-</span>
          <el-input-number
            v-model="state.filter.endHitRate"
            :min="state.filter.startHitRate || 0"
            :controls="false"
            :max="100" />
        </div>
      </el-form-item>
      <el-form-item class="!flex-nowrap" label="准确率" prop="dataSets">
        <el-input-number
          v-model="state.filter.startCorrectRate"
          :max="state.filter.endCorrectRate || 100"
          :controls="false"
          :min="0" />
        <span>-</span>
        <el-input-number
          v-model="state.filter.endCorrectRate"
          :min="state.filter.startCorrectRate || 0"
          :controls="false"
          :max="100" />
      </el-form-item>
    </el-form>
    <div v-show="state.mode === 1" ref="chat" class="chat"></div>
    <div v-show="state.mode === 2">
      <el-table :data="state.list">
        <template v-if="type === 2">
          <el-table-column prop="model" label="模型名称" />
          <el-table-column prop="dataSetName" label="数据集-分组名称" />
          <el-table-column prop="allNum" label="总数" />
          <el-table-column prop="hitNum" label="命中数" />
          <el-table-column prop="hitRate" label="命中率" />
          <el-table-column prop="correctNum" label="正确数" />
          <el-table-column prop="hitCorrectRate" label="命中准确率" />
          <el-table-column prop="correctRate" label="准确率" />
          <el-table-column prop="subCnt" label="科目数" />
        </template>
        <template v-if="type === 3">
          <el-table-column prop="name" label="名称" />
          <el-table-column prop="cnt" label="数量" />
          <el-table-column prop="good" label="Good" />
          <el-table-column prop="same" label="Same" />
          <el-table-column prop="bad" label="Bad" />
        </template>
        <template v-if="type === 4">
          <el-table-column prop="model" label="模型名称" />
          <el-table-column prop="dataSetName" label="数据集-分组名称" />
          <el-table-column prop="allNum" label="总数" />
          <el-table-column prop="hitNum" label="命中数" />
          <el-table-column prop="hitRate" label="命中率" />
        </template>
      </el-table>
    </div>
  </el-main>
</template>

<script lang="ts" setup>
import Back from '../components/back'
import { useGetMarktaskChart, useGetChartsOptions } from '@/api/marktask'
import { setBarChartOptions } from '../bar'
const route = useRoute()
const params = route.query
const type = params.type ? +params.type : 0
const ids = Array.isArray(params.ids) ? params.ids.map((id: any) => 1 * id) : []
const chat = ref()
const isCommonMode = type === 2
const state: any = reactive({
  mode: 1,
  list: [],
  filter: {
    startCorrectRate: 0,
    endCorrectRate: 100,
    startHitRate: 0,
    endHitRate: 100,
    mode: 2,
    models: [],
    dataSets: []
  },
  options: {}
})
const { mutate: getChart } = useGetMarktaskChart({
  async onSuccess(data: any) {
    setBarChartOptions(data, chat, {
      common: false,
      percentFormat: true,
      minWidth: 80
    })
    state.list = data?.list || []
  }
})
const { mutate: getOptions } = useGetChartsOptions({
  async onSuccess(data: any) {
    state.options = data || {}
  }
})
getChart({ markTaskIds: ids, ...state.filter })
getOptions({ markTaskIds: ids })
watch(
  () => state.filter,
  () => {
    getChart({ markTaskIds: ids, ...state.filter })
  },
  {
    deep: true
  }
)
</script>
<style scoped lang="less">
.page-header {
  display: flex;
  justify-content: space-between;
}

.content-container {
  display: flex;
  flex-direction: column;

  .chat {
    // height: 100%;
    flex: 1;
    overflow: auto;
  }

  .table-footer {
    text-align: right;
    margin-top: 16px;
  }

  .el-form {
    span {
      margin: 0 8px;
    }
  }
}
</style>
