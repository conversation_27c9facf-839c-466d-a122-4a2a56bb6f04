import { getStringLength } from '@/utils'
import * as echarts from 'echarts/core'
import { <PERSON><PERSON>hart, RadarChart } from 'echarts/charts'
import { TooltipComponent, GridComponent, LegendComponent } from 'echarts/components'
import { <PERSON>vasRenderer } from 'echarts/renderers'
echarts.use([
  TooltipComponent,
  GridComponent,
  LegendComponent,
  Bar<PERSON>hart,
  RadarChart,
  CanvasRenderer
])
const getTextFormatter = (length = 15, token?: string) => {
  const provideNumber = length // 单行显示文字个数
  return (params: any) => {
    if (typeof params === 'number' && !token) {
      return params
    }
    if (token) {
      return params * 10 + '%'
    }
    let newParamsName = ''
    const textArr = params.split('')
    let maxLength = provideNumber
    for (let i = 0; i < textArr.length; i++) {
      const str = textArr[i]
      if (getStringLength(newParamsName + str) > maxLength) {
        newParamsName += '\n' + str
        maxLength += provideNumber
      } else {
        newParamsName += str
      }
    }
    return newParamsName
  }
}
const setChartHeight = (chartContainer: Element, barWidth: number, myChart: any, category: any) => {
  const clientHeight = chartContainer.clientHeight
  const categoryHeight = category.length * barWidth
  const height = Math.max(categoryHeight, clientHeight)
  myChart.resize({
    height: height
  })
}
const setChartWidth = (chartContainer: Element, minWidth: number, myChart: any, category: any) => {
  const clientWidth = chartContainer.clientWidth
  const categoryWidth = category.length * minWidth
  const width = Math.max(categoryWidth, clientWidth)
  myChart.resize({
    width: width
  })
}

export const initChart = (chart: Ref<any>) => {
  const myChart = echarts.init(chart.value)
  return myChart
}
type Option = {
  common?: boolean
  percentFormat?: boolean
  stack?: boolean
  minWidth?: number
}
type RadarOption = {
  max?: number
}
export const setBarChartOptions = (data: any, chart: any, option: Option = {}) => {
  const myChart = initChart(chart)
  const { common, percentFormat, stack, minWidth } = Object.assign(
    {
      common: true,
      percentFormat: false,
      stack: false,
      minWidth: 0
    },
    option
  )
  const { category = [], series = [] } = data
  if (minWidth) {
    const chartContainer = chart.value
    if (common) {
      setChartWidth(chartContainer, minWidth, myChart, category)
    } else {
      setChartHeight(chartContainer, minWidth, myChart, category)
    }
  }

  const legend = series.map((item: { name: string }) => item.name)
  const seriesOptions = series.map((item: any) => {
    return {
      type: 'bar',
      stack: stack ? 'total' : undefined,
      emphasis: {
        focus: 'series'
      },
      label: {
        show: true
      },
      itemStyle: {
        normal: {
          label: {
            show: true,
            formatter: percentFormat ? '{c}%' : '{c}'
          }
        }
      },
      ...item
    }
  })
  const options = {
    legend: {
      data: legend,
      left: 15,
      top: 0,
      fixed: true
    },
    grid: {
      left: common ? '50px' : '120px',
      right: '50px'
    },
    xAxis: {
      type: common ? 'category' : 'value',
      data: common ? category : undefined,
      axisLabel: {
        interval: 0,
        formatter: getTextFormatter(15)
      }
    },
    yAxis: {
      type: common ? 'value' : 'category',
      data: common ? undefined : category,
      name: common && percentFormat ? '单位（%）' : '',
      axisLabel: {
        interval: 0,
        formatter: getTextFormatter(15)
      }
    },
    series: seriesOptions
  }
  myChart.setOption(options, {
    notMerge: true
  })
  return myChart
}

export const setRadarChartOptions = (data: any, chart: any, option: RadarOption = {}) => {
  const { category = [], series = [] } = data
  const myChart = initChart(chart)
  const legend = series.map((item: { name: string }) => item.name)
  let index: number | undefined = undefined
  const COLOR = [
    '#8DD3C7',
    '#BEBADA',
    '#FB8072',
    '#80B1D3',
    '#B3DE69',
    '#BC80BD',
    '#FFFFB3',
    '#FDB462',
    '#FCCDE5',
    '#D9D9D9'
  ]
  const options = {
    legend: {
      orient: 'vertical',
      data: legend,
      right: 15,
      top: 0,
      fixed: true
    },
    tooltip: {
      show: true,
      formatter: function (params: any) {
        const indicator = options.radar.indicator
        const seriesDataArr = options.series.data
        if (index !== undefined) {
          return (
            indicator[index].name +
            '<br>' +
            seriesDataArr
              .map((item: any, seriesDataArrIndex: number) => ({
                value: item.value[index!],
                index: seriesDataArrIndex,
                name: item.name
              }))
              .sort((a: any, b: any) => {
                const bValue = b.value === '-' ? 0 : b.value
                const aValue = a.value === '-' ? 0 : a.value
                return bValue - aValue
              })
              .map(
                (item: any) =>
                  `<div class="flex-just-space" style="color:${
                    params.name === item.name ? '' : '#909399'
                  };">
                  <div class="flex-just-start">
                  <div style="margin-right:4px;width:20px;height:10px;border-radius:2px;background:${
                    COLOR[item.index % seriesDataArr.length]
                  }"></div> ${item.name}：</div>
                  <div>
                  ${item.value}
                  </div>
                  </div>`
              )
              .join('')
          )
        }
        return params.name
        // const seriesData = seriesDataArr.find((item: any) => item.name === params.name)
        // const text = seriesData.value.map((item: any, index: number) => ({
        //   value: item,
        //   name: indicator[index].name
        // }))
        // const seriesDataIndex = seriesDataArr.findIndex((item: any) => item.name === params.name)
        // let map = text
        //   .map(
        //     (item: any) =>
        //       `<div class='flex-just-space'><div>${item.name}：</div><div>${item.value}</div></div>`
        //   )
        //   .reduce((pre: string, now: string, index: number) => {
        //     switch (index % 3) {
        //       case 0:
        //         pre += '<div class="flex-just-start">' + now
        //         break
        //       case 1:
        //         pre += '，' + now
        //         break
        //       case 2:
        //         pre += '，' + now + '</div>'
        //         break
        //     }
        //     return pre
        //   }, '')
        // if (map.length % 3 !== 0) {
        //   map += '</div>'
        // }
        // return `<div>
        // <div class='flex-just-start'>
        //  <div style="margin-right:4px;width:20px;height:10px;border-radius:2px;background:${
        //    COLOR[seriesDataIndex % seriesDataArr.length]
        //  }"></div> <div>${params.name}</div>
        // </div>
        // <div>${map}</div>
        // </div>`
      }
    },
    radar: {
      shape: 'circle',
      indicator: category.map((name: string) => {
        return {
          name,
          max: option.max || 100
        }
      })
    },
    color: COLOR,
    series: {
      type: 'radar',
      data: series.map((item: any) => {
        return {
          value: item.data,
          name: item.name
        }
      }),
      areaStyle: {
        opacity: 0.4
      }
    }
  }
  myChart.resize({
    width: 'auto'
  })
  myChart.setOption(options, {
    notMerge: true
  })

  myChart.on('mouseover', { seriesIndex: 0 }, function (params: any) {
    index = params.event.target.__dimIdx
  })

  myChart.on('mouseout', { seriesIndex: 0 }, function (params) {
    myChart.dispatchAction({
      type: 'normal',
      seriesIndex: params.seriesIndex,
      dataIndex: params.dataIndex
    })
  })
  return myChart
}

export const setDoubleAxisChartOptions = (data: any, chart: any, option: Option = {}) => {
  const myChart = initChart(chart)
  const { common, percentFormat, stack, minWidth } = Object.assign(
    {
      common: true,
      percentFormat: false,
      stack: false,
      minWidth: 0
    },
    option
  )
  const { category = [], series = [] } = data
  if (minWidth) {
    const chartContainer = chart.value
    if (common) {
      setChartWidth(chartContainer, minWidth, myChart, category)
    } else {
      setChartHeight(chartContainer, minWidth, myChart, category)
    }
  }

  const legend = series.map((item: { name: string }) => item.name)
  const seriesOptions = series.map((item: any, index: number) => {
    return {
      type: 'bar',
      stack: stack ? 'total' : undefined,
      emphasis: {
        focus: 'series'
      },
      label: {
        show: true,
        formatter: index === 2 ? (params: any) => `${params.data * 10}%` : '{c}'
      },
      // itemStyle: {
      //   normal: {
      //     label: {
      //       show: true,
      //       formatter: index === 2 ? (params: any) => `${params.data * 10}%` : '{c}'
      //     }
      //   }
      // },
      ...item,
      data: index === 2 ? item.data.map((item: number) => item / 10) : item.data
    }
  })
  myChart.on('legendselectchanged', function (params: any) {
    const selected = params.selected
    let xAxis: any = []
    if (selected['命中率'] && (selected['平均分'] || selected['标准差'])) {
      xAxis = [xAxiosDefault, xAxios]
    } else if (!selected['命中率']) {
      xAxis = { ...xAxiosDefault }
    } else {
      xAxis = { ...xAxios }
    }
    myChart.setOption(
      {
        xAxis
      },
      { replaceMerge: ['xAxis'] }
    )
  })
  const xAxiosDefault = {
    type: common ? 'category' : 'value',
    min: 0,
    max: 10,
    axisLabel: {
      interval: 0,
      formatter: getTextFormatter(15)
    }
  }
  const xAxios = {
    type: 'value',
    min: 0,
    max: 10,
    axisLabel: {
      interval: 0,
      formatter: getTextFormatter(15, '%')
    }
  }
  const options = {
    legend: {
      data: legend,
      left: 15,
      top: 0,
      fixed: true,
      selected: {
        平均分: true,
        标准差: false,
        命中率: false
      }
    },
    grid: {
      left: common ? '50px' : '120px',
      right: '50px'
    },
    xAxis: [xAxiosDefault],
    yAxis: {
      type: common ? 'value' : 'category',
      data: common ? undefined : category,
      name: common && percentFormat ? '单位（%）' : '',
      axisLabel: {
        margin: 20,
        interval: 0,
        formatter: getTextFormatter(15)
      }
    },
    series: seriesOptions
  }
  myChart.setOption(options)
  return myChart
}
