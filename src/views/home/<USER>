<template>
  <section class="home-container">
    <div class="home-container-banner">
      <div class="home-container-banner__font">
        <div class="title">作业帮大语言模型应用平台</div>
        <div class="desc">
          本平台致力于为公司内部各业务线提供目前可用的，多种大语言模型的体验、提示词工程开发、数据评测对比评估以及API无缝接入。包装统一的API接口，屏蔽线路、IP、账号等细节，并持续优化成本与可用性。
          <!-- <br /> -->
          <!-- 面向公司内部各业务线，提供国内外多种大语言模型的提示词工程调研，数据生产，与API无缝接入服务 -->
        </div>
      </div>
    </div>
    <section class="desc-block">
      <p class="title">产品优势</p>
      <div class="desc-list">
        <div class="item" v-for="item in advantage" :key="item.name">
          <img :src="item.icon" />
          <div class="item-title">{{ item.name }}</div>
          <div class="item-content">{{ item.content }}</div>
        </div>
      </div>
    </section>
    <section class="desc-block">
      <p class="title">接入流程</p>
      <div class="process-list">
        <div v-for="item in process" :key="item">
          <div class="item-content">{{ item }}</div>
        </div>
      </div>
    </section>
  </section>
</template>

<script lang="ts" setup>
import vision from '@/assets/icon-vision.png'
import assets from '@/assets/icon-assets.png'
import integrate from '@/assets/icon-integrate.png'
import development from '@/assets/icon-development.png'

const advantage: any = [
  {
    name: '便捷接入',
    icon: vision,
    content: '封装所有接入细节，解决网络链路问题，统一多种大语言模型接口，自动记账。'
  },
  {
    name: '高可用性',
    icon: assets,
    content: '按业务线配置资源池，自动调度检查资源可用性，配置多层兜底服务，确保接入服务稳定可靠。'
  },
  {
    name: '高并发低延时',
    icon: integrate,
    content:
      '可通过增加资源账号数量线性扩展业务线整体并发能力，响应时延与原生接口直接调用基本持平，支持流式数据返回。'
  },
  {
    name: '生产提效',
    icon: development,
    content: '持续迭代UI交互界面，面向提示词调试、对比、共享、生产的流程提效。'
  }
]
const process: string[] = [
  '1. 使用内网IPS登录，对话实验室默认支持每天5条信息供体验。可点右上角的“申请试用权限”提高到每天100条。',
  '2. 对已立项或明确在预研阶段的项目，建议申请独立业务线(右上角)，闭环管理资源。和组内leader沟通确认管理员身份后，与平台管理员沟通，确定接入方式，人数，QPS，完善需求文档，走完业务方自身提需流程。如为API或SDK调用方式，需在业务方自身需求评审过后，拉平台方研发评审，确定接入细节。',
  '3. 对业务所需的底层账号资源，支持两种方式。一是业务自行采购，自助在业务线管理界面中添加。二是平台代采按需分配记账。',
  '4. 请先阅读FAQ再使用'
]
</script>
<style scoped lang="less">
.home-container {
  overflow: auto;
  padding-bottom: 24px;

  .home-container-banner {
    background-image: url('@/assets/banner.png');
    height: 340px;
    background-size: 100% 100%;
    overflow: hidden;
    margin-bottom: 16px;

    .home-container-banner__font {
      margin-left: 160px;
      width: 620px;
      margin-top: 70px;
    }

    .title {
      color: #0a0a27;
      font-size: 46px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 56px;
    }

    .desc {
      color: #0a0a27;
      font-size: 16px;
      font-weight: 400;
      height: 60px;
      letter-spacing: 0;
      line-height: 30px;
      margin-top: 12px;
      opacity: 0.8;
      width: 555px;
    }
  }

  .desc-block {
    .title {
      color: #0a0a27;
      font-size: 28px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 56px;
      text-align: center;
    }

    .desc-list {
      margin-top: 8px;
      margin-bottom: 18px;
      padding: 12px 56px;
      text-align: center;
      display: flex;
      justify-content: space-around;

      .item {
        display: inline-block;
        width: 240px;
        height: 302px;
        margin-right: 24px;
        padding: 24px;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        color: #333333;
        cursor: pointer;
        background: linear-gradient(180deg, #f5f8fc 0%, #ffffff 100%);
        box-shadow: 10px 10px 16px 0px rgba(22, 30, 87, 0.06), 4px 4px 8px 0px rgba(0, 0, 0, 0.02);
        border-radius: 8px;
        border: 2px solid #ffffff;

        &.last-item {
          margin-right: 0;
        }

        img {
          width: 60px;
          height: 60px;
        }

        &-title {
          margin-top: 16px;
          display: inline-block;
          flex-direction: column;
          font-size: 24px;
          font-weight: 600;
          line-height: 24px;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;

          &::after {
            content: '';
            width: 32px;
            height: 2px;
            background-color: #2372f6;
            border: #2372f6;
            border-radius: 1px;
            display: inline-block;
            margin-top: 24px;
          }
        }

        &-line {
          margin-top: 24px;
          width: 32px;
          height: 2px;
          background-color: #2372f6;
          border: #2372f6;
          border-radius: 1px;
        }

        &-content {
          margin-top: 22px;
          opacity: 0.6;
          text-align: left;
          width: 226px;
          height: 150px;
          font-size: 15px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          line-height: 25px;
          letter-spacing: 1px;
        }

        &:hover {
          .item-title {
            color: #2372f6;

            &::after {
              width: 100%;
              transition: width 0.5s;
            }
          }
        }
      }
    }

    .process-list {
      margin-top: 10px;
      padding: 0 32px;

      & > div {
        padding: 8px 36px;
        line-height: 24px;
        color: rgba(0, 0, 0, 0.78);
      }
    }
  }

  .tips-text {
    text-align: right;
    padding-right: 72px;
  }
}
</style>
