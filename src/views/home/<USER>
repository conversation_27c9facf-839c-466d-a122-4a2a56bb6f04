<template>
  <SearchContainer>
    <el-form-item>
      <el-input v-model.trim="state.query.keyword" placeholder="根据任务名称搜索" clearable>
        <template #append>
          <el-button type="primary" @click="queryList">搜索</el-button>
        </template>
      </el-input>
    </el-form-item>
    <section class="">
      <el-button type="primary">新建</el-button>
    </section>
  </SearchContainer>
  <section class="table-container">
    <el-table :data="state.list" style="width: 100%" :show-overflow-tooltip="true">
      <el-table-column prop="id" label="ID" />
      <el-table-column prop="displayName" label="任务名称" />
      <el-table-column prop="status" label="状态" />
      <el-table-column fixed="right" label="Operations" width="250">
        <template #default>
          <el-button text type="primary" size="small">查看</el-button>
          <el-button text type="primary" size="small">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-model:pageNum="state.pageInfo.pageNum"
      v-model:page-size="state.pageInfo.pageSize"
      :total="state.pageInfo.total"
      @refresh="getList"></Pagination>
  </section>
</template>

<script lang="ts" setup>
import { Pagination } from '@znzt-fe/components'
import SearchContainer from '@/components/search-container'
const state: any = reactive({
  query: {
    keyword: ''
  },
  list: [],
  pageInfo: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  dialog: false,
  detail: {},
  drawer: false,
  readonly: false
})
const queryList = () => {
  state.pageInfo.pageNum = 1
  getList()
}

const getList = async () => {
  state.list = [
    {
      id: 2,
      taskDisplayName: '任务名称',
      displayName: '实例名称',
      status: '运行中',
      runStartTime: 1532323323232, //执行开始时间
      runEndTime: 1532323323232, //执行结束时间
      processInstanceId: 12, //返回的调度实例ID
      taskInstanceId: 0 // 返回的调度实例ID
    }
  ]
  state.pageInfo.total = 40
}
queryList()
</script>
<style scoped lang="less">
.table-container {
  border-radius: 4px;
  background: white;
  padding: 12px;
}
</style>
