<template>
  <section>
    <el-text v-for="item in diffs" :key="item" :tag="getTextTag(item)">{{ item.value }}</el-text>
  </section>
</template>
<script lang="ts" setup>
import { diffWords } from 'diff'
const props = defineProps({
  baseStr: {
    type: String,
    default: ''
  },
  compareStr: {
    type: String,
    default: ''
  }
})
const getTextTag = (item: any) => {
  if (item.added) {
    return 'mark'
  }
  if (item.removed) {
    return 'del'
  }
  return 'default'
}
const diffs = diffWords(props.baseStr, props.compareStr)
</script>

<style scoped lang="less">
section {
  padding-bottom: 0 !important;
}

del {
  background-color: var(--el-color-danger);
}
</style>
