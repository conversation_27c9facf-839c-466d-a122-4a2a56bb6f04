<template>
  <section>
    <div class="slider-demo-block">
      <span class="demonstration">Temperature ({{ state.temperature }})</span>
      <el-slider v-model="state.temperature" :step="0.01" :max="1" :min="0" />
    </div>
    <div class="slider-demo-block">
      <span class="demonstration">Maximum length ({{ state.max_tokens }})</span>
      <el-slider v-model="state.max_tokens" :step="1" :max="2048" :min="1" />
    </div>
    <div class="slider-demo-block">
      <span class="demonstration">Top P ({{ state.top_p }})</span>
      <el-slider v-model="state.top_p" :step="0.01" :max="1" :min="0" />
    </div>
    <div class="slider-demo-block">
      <span class="demonstration">Frequency penalty ({{ state.frequency_penalty }})</span>
      <el-slider v-model="state.frequency_penalty" :step="0.01" :max="2" :min="0" />
    </div>
    <div class="slider-demo-block">
      <span class="demonstration">Presence penalty ({{ state.presence_penalty }})</span>
      <el-slider v-model="state.presence_penalty" :step="0.01" :max="2" :min="0" />
    </div>
  </section>
</template>
<script lang="ts" setup>
import { cloneDeep } from 'lodash-es'

const props = defineProps({
  value: {
    type: Object,
    default: () => ({})
  }
})
const emits = defineEmits(['update:value'])
const state = reactive({
  ...props.value
})
watch(state, (val: any) => {
  emits('update:value', cloneDeep(val))
})
</script>

<style scoped lang="less">
.slider-demo-block {
  display: flex;
  align-items: center;
}

.slider-demo-block .el-slider {
  margin-top: 0;
  margin-left: 12px;
  margin-right: 16px;
}

.slider-demo-block .demonstration {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  line-height: 44px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 0;
}

.slider-demo-block .demonstration + .el-slider {
  flex: 0 0 75%;
}
</style>
