<template>
  <section class="page-container">
    <el-container>
      <el-aside :width="collapse ? '0px' : '280px'">
        <el-container>
          <el-header class="flex flex-col gap-3 !h-auto">
            <el-button @click="createTask(TaskTypeEnum.Task)">{{
              CreateTaskMap[TaskTypeEnum.Task].name
            }}</el-button>
            <el-button @click="createTask(TaskTypeEnum.Batch)">{{
              CreateTaskMap[TaskTypeEnum.Batch].name
            }}</el-button>
            <el-button @click="createTask(TaskTypeEnum.ApiBatch)">{{
              CreateTaskMap[TaskTypeEnum.ApiBatch].name
            }}</el-button>
          </el-header>
          <el-main>
            <el-collapse
              v-model="state.activeFolder"
              v-if="taskStore.list.length"
              @change="collapseChange">
              <el-collapse-item
                v-for="item in taskStore.list"
                :key="item.id"
                :title="item.name"
                :name="item.id"
                @click="handleItemExpand(item.id)">
                <el-tooltip
                  effect="dark"
                  :content="task.name"
                  placement="top"
                  v-for="task in getCurrenFolderList(item.id)"
                  :key="task.taskId">
                  <section
                    class="box-item"
                    @click="selectItem(task)"
                    :class="{ 'active-item': isActive(task.taskId) }">
                    <p v-if="!task.edit" class="break-all">
                      {{ task.name }}
                    </p>
                    <el-input v-if="task.edit" v-model="task.name"></el-input>
                    <p class="flex" v-if="isActive(task.taskId)">
                      <el-icon @click="editTitle(task)" v-if="!task.edit">
                        <Edit />
                      </el-icon>
                      <el-icon v-if="!task.edit" @click="deleteTask(task)">
                        <Delete />
                      </el-icon>
                      <el-icon v-if="task.edit" @click="updateTitle(task)">
                        <Check />
                      </el-icon>
                    </p>
                  </section>
                </el-tooltip>
              </el-collapse-item>
            </el-collapse>
          </el-main>
        </el-container>
      </el-aside>
      <section class="divider">
        <el-icon v-if="!collapse" @click="collapse = true">
          <ArrowLeft />
        </el-icon>
        <el-icon v-else @click="collapse = false">
          <ArrowRight />
        </el-icon>
      </section>
      <el-main>
        <component v-if="taskStore.active" :is="getComponent()" :key="taskStore.active" />
        <!-- <Detail></Detail> -->
      </el-main>
    </el-container>
  </section>
</template>
<script lang="ts" setup>
import useUserStore from '@/store/user'
import useTaskStore, { FolderMapValue } from '@/store/task'
import { ElMessageBox } from 'element-plus'
import { TaskTypeEnum } from '@/api/task/type'
import { CreateTaskMap } from './constant'
interface IState {
  activeFolder: number[]
}
const userStore = useUserStore()
const taskStore = useTaskStore()
const collapse = ref(false)
const taskType = ref(TaskTypeEnum.Task)
const getComponent = () => CreateTaskMap[taskType.value].component

onUnmounted(() => {
  taskStore.active = void 0
})
const { current } = userStore
invoke(async () => {
  await until(current).toMatch((value) => !!value.businessId)
  taskStore.getTaskFolder()
  taskStore.getModelParams({ business: current.businessCode })
})

const state = reactive<IState>({
  activeFolder: []
})
const createTask = async (type: TaskTypeEnum) => {
  const data = await taskStore.createTask({
    name: CreateTaskMap[type]['name'],
    taskType: type
  })
  const id = data.taskId
  taskStore.reloadRoute(id)
  taskType.value = type
  taskStore.active = id
  if (!state.activeFolder.includes(1)) {
    state.activeFolder.push(1)
  }
  await taskStore.getTaskListByFolderId({
    folderId: 1
  })
}
let collapseState = false
const collapseChange = () => {
  collapseState = true
}
const handleItemExpand = (id: number) => {
  const folders: number[] = state.activeFolder
  if (!collapseState) {
    return
  }
  collapseState = false
  // 展开状态 获取当前的文件夹下的数据
  if (folders.includes(id)) {
    taskStore.getTaskListByFolderId({
      folderId: id
    })
  }
}
const getCurrenFolderList = (id: number) => {
  return taskStore.folderMap.get(id)
}
const editTitle = (task: FolderMapValue) => {
  task.edit = true
}
const isActive = (id: number) => {
  return taskStore.active === id
}
const updateTitle = async (task: FolderMapValue) => {
  await taskStore.updateTitle({
    taskId: task.taskId,
    name: task.name
  })
  task.edit = false
}
const selectItem = async (task: FolderMapValue) => {
  const id = task.taskId
  if (taskStore.active === id) {
    return
  }
  if (taskStore.active) {
    const activeTask = taskStore.getTaskById(taskStore.active)
    activeTask.edit = false
  }
  taskStore.active = id
  taskStore.reloadRoute(id)
  taskType.value = task.taskType || TaskTypeEnum.Task
}
const deleteTask = async (task: FolderMapValue) => {
  const action = await ElMessageBox.confirm('确认删除此任务？', '删除提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  if (action === 'confirm') {
    await taskStore.deleteTask(task.taskId)
    taskStore.active = void 0
    taskStore.reloadRoute()
    await taskStore.getTaskListByFolderId({
      folderId: task.folderId
    })
  }
}
</script>

<style scoped lang="less">
.page-container {
  @apply h-full w-full box-border;
  padding: 16px;

  .el-container {
    @apply h-full bg-white;
    border-radius: 3px;

    .el-aside {
      padding: 16px 0;

      .el-header {
        // height: 32px;

        .el-button {
          @apply w-full ml-0 border-dashed;
        }
      }

      .el-main {
        padding-left: 32px;

        .el-collapse {
          border: none;

          .el-collapse-item {
            @apply relative;

            :deep(.el-collapse-item__arrow) {
              @apply absolute;
              left: -16px;
            }

            :deep(div) {
              border: none !important;
            }
          }
        }

        .box-item {
          @apply flex justify-between items-center cursor-pointer;
          padding: 8px 16px;
          text-align: left;
          border: 1px solid #e1e3ea;
          border-radius: 8px;
          margin: 8px 0;

          &.active-item {
            --tw-text-opacity: 1;
            color: rgb(75 158 95 / var(--tw-text-opacity));
            --tw-bg-opacity: 1;
            background-color: rgb(245 245 245 / var(--tw-bg-opacity));
            --tw-border-opacity: 1;
            border-color: rgb(75 158 95 / var(--tw-border-opacity));
          }

          &:hover {
            background-color: rgb(245 245 245);
          }

          p {
            line-height: 24px;
            padding: 0;

            .el-icon {
              margin-left: 8px;
            }
          }
        }

        :deep(.el-collapse-item__content) {
          padding-bottom: 10px;
        }
      }
    }
  }

  .divider {
    @apply h-full relative;
    width: 1px;
    background: var(--el-border-color);

    & > .el-icon {
      @apply absolute bg-white cursor-pointer;
      right: -8px;
      top: 50%;
      z-index: 200;
      border-radius: 50%;
    }
  }
}
</style>
