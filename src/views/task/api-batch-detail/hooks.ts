import { retryApiB<PERSON>, useCancelApiBatch, useGetTaskTpl } from '@/api/task'
import { ResponseData } from '@/plugin/axios/interceptors'
import useTaskStore from '@/store/task'
import { Fn } from '@znzt-fe/declare'
import { afterDecorator, confirmFn } from '@znzt-fe/utils'
import { ElMessage, TagProps, UploadFiles, UploadInstance } from 'element-plus'

export const useDownload = (waitInit: ComputedRef<boolean>) => {
  const {
    mutate: getTaskTplMutate,
    isLoading: getTaskTplLoading,
    data: taskTplData
  } = useGetTaskTpl()
  const downLoadTplJson = () => window.open(taskTplData.value?.newApiBatch.jsonlUrl)
  whenever(waitInit, getTaskTplMutate, { immediate: true })
  return {
    downLoadTplJson,
    getTaskTplLoading,
    getTaskTplMutate
  }
}

export enum Status {
  WaitToInit,
  Init,
  Failed,
  Success,
  Running,
  Cancel
}

export enum ActualStatus {
  DataCheck = 1,
  Running = 2,
  DataOutput = 3,
  Success = 4,
  Failed = 5,
  Expired = 6,
  Cancelling = 7,
  Cancelled = 8,
  Queueing = 9
}
export const statusMap: Record<ActualStatus, { text: string; type: TagProps['type'] }> = {
  1: { text: '数据检查', type: 'info' },
  2: { text: '执行中', type: 'primary' },
  3: { text: '数据输出中', type: 'primary' },
  4: { text: '执行成功', type: 'success' },
  5: { text: '执行失败', type: 'danger' },
  6: { text: '过期', type: 'danger' },
  7: { text: '取消中', type: 'warning' },
  8: { text: '取消完毕', type: 'info' },
  9: { text: '排队中', type: 'info' }
}

export const useUpload = () => {
  const fileList: Ref<UploadFiles> = ref([])
  const uploadRef: Ref<UploadInstance | null> = ref(null)
  const handleRemove = () => {
    fileList.value = []
  }
  return {
    fileList,
    uploadRef,
    handleRemove
  }
}

export const useTask = (uploadRef: Ref<UploadInstance | null>, fileList: Ref<UploadFiles>) => {
  const taskStore = useTaskStore()
  const [isLoading, setIsLoading] = useToggle(false)
  const getTaskDetail = afterDecorator(
    async () => await taskStore.getTaskDetail(taskStore.active),
    () => setIsLoading(false)
  )
  const afterGetDetail = <T extends Fn>(fn: T) => afterDecorator(fn, getTaskDetail)
  const uploadData = computed(() => ({
    taskId: taskStore.active
  }))
  const detail = computed(() => taskStore.getTaskById(taskStore.active))
  const apiBatch = computed(() => detail.value?.apiBatch)
  const taskInfo = computed(() => detail.value?.taskInfo)
  const status = computed(() => (detail.value?.apiBatch?.batchStatus || 0) as Status)
  const statusToType = computed(() => {
    const status = detail.value?.apiBatch?.actualStatus || 1
    return statusMap[status as ActualStatus].type
  })
  const batchId = computed<string>(() => {
    const batchId = detail.value.apiBatch?.batchId
    return batchId === '0' ? '' : batchId
  })

  const waitInit = computed(() => status.value === Status.WaitToInit)
  const inProgress = computed(() => status.value === Status.Running)
  const isFailed = computed(() => status.value === Status.Failed)
  const isSuccess = computed(() => status.value === Status.Success)
  const isCancel = computed(() => status.value === Status.Cancel)
  const { mutate: cancelApiBatchMutate } = useCancelApiBatch({
    onSuccess: afterGetDetail(() => ElMessage.success('取消成功'))
  })
  // 控制首次渲染不显示内容，防止过大的layout shift
  const [firstRender, setFirstRender] = useToggle(true)
  invoke(
    afterDecorator(
      async () => await getTaskDetail(),
      () => setFirstRender(false)
    )
  )
  const runApiBatchAgain = afterGetDetail(
    async () =>
      await retryApiBatch({
        taskId: taskStore.active!,
        batchId: batchId.value
      })
  )
  const downloadResult = (type: number) => {
    const batchId = detail.value.apiBatch.batchId
    const url = `/openmis/task/apibatch/export?batchId=${batchId}&type=${type}`
    window.open(url)
  }
  const runTask = afterGetDetail(async () => await taskStore.batchRunTask(batchRunTaskParams))
  const batchRunTaskParams = reactive({
    batchId: '',
    uploadId: ''
  })

  const uploadSuccess = async (res: ResponseData<{ batchId: string; uploadId: string }>) => {
    const { errNo = 0, errMsg = '', data } = res
    if (errNo !== 0) {
      ElMessage.error(errMsg || '上传失败！')
      setIsLoading(false)
      return
    }
    batchRunTaskParams.batchId = data.batchId
    batchRunTaskParams.uploadId = data.uploadId
    runTask()
  }
  const confirm = async () => {
    if (!fileList.value.length) {
      ElMessage.error('请先上传文件')
      return
    }
    setIsLoading(true)
    if (batchRunTaskParams.uploadId) {
      runTask()
    } else {
      uploadRef.value!.submit()
    }
  }
  const cancelApiBatch = confirmFn(
    () =>
      cancelApiBatchMutate({
        taskId: taskStore.active!,
        batchId: batchId.value
      }),
    '确认取消'
  )
  const { pause, resume } = useIntervalFn(() => getTaskDetail(), 1000 * 60 * 5, {
    immediate: false
  })
  watchImmediate(status, (val) => {
    switch (val) {
      case Status['Init']:
      case Status['Running']:
        resume()
        break
      default:
        pause()
        break
    }
  })
  return {
    isLoading,
    uploadData,
    cancelApiBatch,
    confirm,
    statusToType,
    runApiBatchAgain,
    downloadResult,
    waitInit,
    isCancel,
    inProgress,
    isFailed,
    isSuccess,
    firstRender,
    uploadSuccess,
    detail,
    status,
    apiBatch,
    taskInfo
  }
}
