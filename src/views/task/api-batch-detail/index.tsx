import { useDownload, useUpload, useTask, Status, ActualStatus } from './hooks'
import {
  ElButton,
  ElDescriptions,
  ElDescriptionsItem,
  ElForm,
  ElFormItem,
  ElTag,
  ElTooltip,
  ElUpload,
  ElCollapse,
  ElCollapseItem,
  ElDropdown, 
  ElDropdownMenu,
  ElDropdownItem, 
} from 'element-plus'
import { Download, Folder, QuestionFilled, Clock, ArrowDown } from '@element-plus/icons-vue'
import ModelQueue from './model-queue'
export default defineComponent({
  setup() {
    const { fileList, uploadRef, handleRemove } = useUpload()
    const {
      waitInit,
      inProgress,
      isFailed,
      isCancel,
      downloadResult,
      isSuccess,
      runApiBatchAgain,
      confirm,
      cancelApiBatch,
      uploadSuccess,
      isLoading,
      firstRender,
      uploadData,
      statusToType,
      taskInfo,
      status,
      apiBatch
    } = useTask(uploadRef, fileList)
    const { downLoadTplJson, getTaskTplLoading } = useDownload(waitInit)
    const modelQueueVisible = ref(false)
    const getModelQueue = ()=>{
      modelQueueVisible.value = true
    }
    const dropdownItems = [
      { name: '下载全部输出', type: 1 },
      { name: '仅下载成功输出', type: 2 },
      { name: '仅下载失败输出', type: 3 },
      { name: '下载原始输入', type: 4 },
      { name: '仅下载失败输入', type: 5 },
      { name: '仅下载可重试输入', type: 6 },
    ];
    return () => (
      <section
        class="page-container"
        element-loading-background="rgba(255, 255, 255, 0.7)"
        element-loading-text="任务执行中"
        v-loading={isLoading.value || firstRender.value}>
        {!firstRender.value && (
          <ElForm>
            {waitInit.value && (
              <>
                <ElFormItem class="flex items-start" label="上传文件">
                  <div>
                    <ElUpload
                      accept=".jsonl"
                      action={`/openmis/task/apibatch/import?${qs.stringify(uploadData.value)}`}
                      autoUpload={false}
                      limit={1}
                      onRemove={handleRemove}
                      onSuccess={uploadSuccess}
                      ref={uploadRef}
                      v-model:file-list={fileList.value}>
                      <ElButton
                        disabled={!!fileList.value.length}
                        icon={Folder}
                        size="small"
                        type="primary">
                        选择文件
                      </ElButton>
                      <ElButton
                        icon={Download}
                        loading={getTaskTplLoading.value}
                        onClick={withModifiers(downLoadTplJson, ['stop'])}
                        size="small"
                        type="success">
                        jsonl格式参考
                      </ElButton>
                      <ElButton
                        icon={Clock}
                        onClick={withModifiers(getModelQueue, ['stop'])}
                        size="small"
                        type="success">
                        获取模型排队情况
                      </ElButton>
                    </ElUpload>
                    <div>
                      <ElTag disableTransitions>
                        Tip:文心24小时内返回，豆包夜间运行，任务结束会有钉钉提醒
                      </ElTag>
                    </div>
                  </div>
                </ElFormItem>
                <div class="flex justify-end">
                  <ElButton onClick={confirm} type="primary">
                    执行
                  </ElButton>
                </div>
              </>
            )}
            {!waitInit.value && (
              <ElDescriptions border class="overflow-auto mb-4" column={2}>
                <ElDescriptionsItem label="任务状态">
                  <ElTag disable-transitions type={statusToType.value}>
                    {apiBatch.value?.actualStatusDesc}
                  </ElTag>
                  {status.value === Status.Failed && (
                    <ElTooltip content={apiBatch.value?.failReason} effect="dark">
                      <ElButton icon={QuestionFilled} link />
                    </ElTooltip>
                  )}
                </ElDescriptionsItem>
                <ElDescriptionsItem label="预估等待/执行时间">{apiBatch.value?.estimateExecTime || "-"}</ElDescriptionsItem>
                <ElDescriptionsItem label="创建时间">
                  {dayjs.unix(taskInfo.value?.createTime).format('YYYY-MM-DD HH:mm:ss')}
                </ElDescriptionsItem>
                <ElDescriptionsItem label="开始执行时间">
                  {apiBatch.value?.in_progress_at}
                </ElDescriptionsItem>
                {(isSuccess.value || isFailed.value) && (
                  <ElDescriptionsItem label="完成时间">
                    {apiBatch.value?.completed_at}
                  </ElDescriptionsItem>
                )}
                {isCancel.value && (
                  <>
                    <ElDescriptionsItem label="取消时间">
                      {apiBatch.value?.canceling_at}
                    </ElDescriptionsItem>
                    <ElDescriptionsItem label="实际取消时间">
                      {apiBatch.value?.canceled_at}
                    </ElDescriptionsItem>
                  </>
                )}
                {isSuccess.value && (
                  <ElDescriptionsItem label="过期时间">
                    {apiBatch.value?.expired_at}
                  </ElDescriptionsItem>
                )}
                {isFailed.value && (
                  <ElDescriptionsItem label="失败时间">
                    {apiBatch.value?.failed_at}
                  </ElDescriptionsItem>
                )}
                <ElDescriptionsItem label="请求总数">{apiBatch.value?.total}</ElDescriptionsItem>
                <ElDescriptionsItem label="请求成功数">
                  {apiBatch.value?.completed}
                </ElDescriptionsItem>
                <ElDescriptionsItem label="请求失败数">{apiBatch.value?.failed}</ElDescriptionsItem>
                <ElDescriptionsItem label="总消耗token">
                  {apiBatch.value?.total_tokens}
                </ElDescriptionsItem>
                <ElDescriptionsItem label="输出消耗token">
                  {apiBatch.value?.completion_tokens}
                </ElDescriptionsItem>
                <ElDescriptionsItem label="输入消耗token">
                  {apiBatch.value?.prompt_tokens}
                </ElDescriptionsItem>
              </ElDescriptions>
            )}
            {apiBatch.value?.outputFile && apiBatch.value.actualStatus !== ActualStatus.Expired && (
              <ElFormItem class="flex items-center" label="任务结果">
                {/* <ElButton onClick={downloadResult} size="small" type="primary">
                  下载jsonl文件
                </ElButton> */}
                <ElDropdown onCommand={downloadResult}>
                  {{
                    default: () => (
                      <ElButton
                        class="flex flex-row-reverse gap-2"
                        icon={ArrowDown}
                        size="small"
                        type="primary">
                        下载文件
                      </ElButton>
                    ),
                    dropdown: () => (
                      <ElDropdownMenu>
                        {dropdownItems.map((item) => (
                          <ElDropdownItem command={item.type} key={item.name}>
                            {item.name}
                          </ElDropdownItem>
                        ))}
                      </ElDropdownMenu>
                    ),
                  }}
                </ElDropdown>
              </ElFormItem>
            )}
            {apiBatch.value.subBatch && apiBatch.value.subBatch.length && (
              <ElCollapse>
                <ElCollapseItem name="1" title="子任务详情">
                  {apiBatch.value.subBatch &&
                    apiBatch.value.subBatch.map((item, index) => {
                      return (
                        <ElDescriptions border class="overflow-auto mb-4" column={2} key={index}>
                          <ElDescriptionsItem label="任务状态">
                            {item?.actualStatusDesc}
                          </ElDescriptionsItem>
                          <ElDescriptionsItem label="模型">{item.model}</ElDescriptionsItem>
                          <ElDescriptionsItem label="预估等待/执行时间">{item?.estimateExecTime || "-"}</ElDescriptionsItem>
                          <ElDescriptionsItem label="数据总数">{item?.total}</ElDescriptionsItem>
                          <ElDescriptionsItem label="请求成功数">
                            {item?.completed}
                          </ElDescriptionsItem>
                          <ElDescriptionsItem label="请求失败数">{item?.failed}</ElDescriptionsItem>
                          <ElDescriptionsItem label="总消耗token">
                            {item?.total_tokens}
                          </ElDescriptionsItem>
                          <ElDescriptionsItem label="输出消耗token">
                            {item?.completion_tokens}
                          </ElDescriptionsItem>
                          <ElDescriptionsItem label="输入消耗token">
                            {item?.prompt_tokens}
                          </ElDescriptionsItem>
                        </ElDescriptions>
                      )
                    })}
                </ElCollapseItem>
              </ElCollapse>
            )}

            {inProgress.value && (
              <div class="flex justify-end mt-2">
                <ElButton onClick={cancelApiBatch} type="primary">
                  取消执行
                </ElButton>
              </div>
            )}
            {isFailed.value && (
              <div class="flex justify-end">
                <ElButton onClick={runApiBatchAgain} type="primary">
                  再次执行
                </ElButton>
              </div>
            )}
          </ElForm>
        )}
        {modelQueueVisible.value && (<ModelQueue v-model:visible={modelQueueVisible.value}/>)}
      </section>
    )
  }
})
