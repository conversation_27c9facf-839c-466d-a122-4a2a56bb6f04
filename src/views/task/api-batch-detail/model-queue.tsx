import { useGetModelQueue } from '@/api/task'
import { useVModel } from '@vueuse/core'
import { ElDialog, ElTable, ElTableColumn } from 'element-plus'
export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const show = useVModel(props, 'visible', emit)
    const { mutate, data }: any = useGetModelQueue()
    mutate()
    const list = computed(() => data.value?.list || [])
    return () => (
      <ElDialog title="模型排队情况" v-model={show.value} width="50%">
        <ElTable data={list.value}>
          <ElTableColumn label="模型" prop="modelName" />
          <ElTableColumn label="待执行任务数量" prop="subBatchCount" />
          <ElTableColumn label="待执行请求数量" prop="requestCount" />
          <ElTableColumn label="预估等待时间" prop="waitTime" />
        </ElTable>
      </ElDialog>
    )
  }
})
