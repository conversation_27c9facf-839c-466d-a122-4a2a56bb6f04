<template>
  <section>
    <el-text v-for="item in diff" :key="item" :tag="getTextTag(item[0])">{{ item[1] }}</el-text>
  </section>
</template>
<script lang="ts" setup>
import DiffMatchPatch from 'diff-match-patch'
const dmp = new DiffMatchPatch()
const props = defineProps({
  baseStr: {
    type: String,
    default: ''
  },
  compareStr: {
    type: String,
    default: ''
  }
})
const getTextTag = (status: number) => {
  if (status === 1) {
    return 'mark'
  }
  if (status === -1) {
    return 'del'
  }
  return 'default'
}
const diff = dmp.diff_main(props.baseStr, props.compareStr)
dmp.diff_cleanupSemantic(diff)
</script>

<style scoped lang="less">
section {
  padding-bottom: 0 !important;
}

del {
  background-color: var(--el-color-danger);
}

.el-text {
  white-space: pre-line;
}
</style>
