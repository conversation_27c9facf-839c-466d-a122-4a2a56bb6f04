<script setup lang="ts">
import { AdvancedParameter } from '@/api/business/type'
import { ModelsItemParams } from '@/api/marktask/type'
import { ElInput, ElInputNumber, ElSlider, ElSwitch } from 'element-plus'
import JsonEditorVue from 'json-editor-vue'

type RenderItems = Array<
  AdvancedParameter & {
    name: keyof ModelsItemParams
  }
>
const props = defineProps({
  renderItems: {
    type: Array as PropType<RenderItems>,
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: Object as PropType<ModelsItemParams>,
    default: () => ({})
  },
  init: {
    type: <PERSON>olean,
    default: false
  }
})
const emits = defineEmits(['update'])
const state: any = reactive({
  ...props.modelValue
})
const selected: any = ref({})
if (props.init) {
  const keys = Object.keys(props.modelValue)
  keys.forEach((key) => {
    selected.value[key] = true
  })
}
watch(
  () => [selected.value, state],
  () => {
    const keys = Object.keys(selected.value)
    const data: any = {}
    keys.forEach((key) => {
      if (selected.value[key]) {
        data[key] = state[key]
      }
    })
    emits('update', data)
  },
  {
    deep: true,
    immediate: true
  }
)
const componentMap = {
  slider: ElSlider,
  input: ElInput,
  switch: ElSwitch,
  numberinput: ElInputNumber,
  json: JsonEditorVue
}
const getComponent = (type: AdvancedParameter['component']) => componentMap[type]
const jsonChange = (updatedContent: any, name: keyof ModelsItemParams) => {
  const { text = '' } = updatedContent
  try {
    const data = JSON.parse(text)
    state[name] = data
  } catch (e) {
    console.error(e)
  }
}
</script>

<template>
  <el-form label-width="190px" style="margin-top: 32px" label-placement="left" :disabled="disabled">
    <el-form-item v-for="item in renderItems" :key="item.name" :label="item.name">
      <template #label>
        <section class="custom-label">
          <span>{{ item.name }}</span>
          <span v-if="item.component === 'slider'">( {{ state[item.name] }} )</span>
        </section>
      </template>
      <JsonEditorVue
        class="json-editor"
        v-if="item.component === 'json'"
        :content="{ json: state[item.name] }"
        mode="text"
        :main-menu-bar="false"
        :navigation-bar="false"
        @change="
          (value) => {
            jsonChange(value, item.name)
          }
        " />
      <component
        :is="getComponent(item.component)"
        v-else
        v-model="state[item.name]"
        v-bind="item.params"
        :disabled="!selected[item.name]" />
      <el-checkbox v-model="selected[item.name]"></el-checkbox>
    </el-form-item>
  </el-form>
</template>

<style lang="less" scoped>
.el-form {
  :deep(.el-form-item__content) {
    padding: 0 12px;
    display: flex;
    flex-direction: row;

    & > div {
      flex: 1;
      max-width: 250px;
    }

    & > .el-checkbox {
      margin-left: 30px;
    }
  }

  .json-editor {
    width: 95%;
    min-width: 500px;
  }
}
</style>
