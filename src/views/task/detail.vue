<template>
  <section
    class="page-container"
    v-loading="loading"
    element-loading-text="任务执行中"
    element-loading-background="rgba(255, 255, 255, 0.7)">
    <p class="title">批量任务</p>
    <el-row :gutter="32">
      <el-col :span="12">
        <p>发送内容</p>
        <p>
          <el-input
            v-model="params.msg"
            :autosize="{ minRows: 8, maxRows: 12 }"
            type="textarea"
            placeholder="使用${变量名}插入变量"></el-input>
        </p>
      </el-col>
      <el-col :span="12">
        <p>变量列表</p>
        <el-form class="vars-list">
          <el-form-item
            v-for="item in params.vars"
            :key="item.name"
            :label="item.name"
            label-width="70px">
            <el-input
              v-model="item.content"
              placeholder="输入变量值"
              :autosize="{ minRows: 1, maxRows: 3 }"
              type="textarea"
              clearable>
            </el-input>
            <el-select v-model="item.split" placeholder="Select" style="width: 115px">
              <el-option
                v-for="item in splitOptions"
                :key="item.value"
                :label="item.name"
                :value="item.value" />
            </el-select>
            <span class="var-number">变量个数: {{ item.content.split(item.split).length }}</span>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-row :gutter="32" class="setting-params">
      <el-col :span="12">
        生成
        <el-input-number
          v-model="params.repeatTimes"
          :min="1"
          :max="30"
          :step="1"
          :precision="0"
          @input="repeatTimeChange" />
        组结果
      </el-col>
      <el-col :span="12" class="right-col">
        <el-button @click="resetToTask(true)">还原任务</el-button>
        <el-button @click="saveToDraft">存为草稿</el-button>
        <el-button type="danger" @click="clearMsg">清空</el-button>
        <el-button type="primary" @click="runTask">执行</el-button>
      </el-col>
    </el-row>
    <el-row v-if="showModel" class="model-params">
      <el-col :span="24">
        <el-form-item label="模型">
          <ModelTreeSelect
            multiple
            :data="taskStore.companyListId"
            :modelValue="params.models"
            @change="(e: number[]) => !e.length ? (params.models = [params.models[0]]) : (params.models = e)" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-tabs v-model="state.activeTab" class="demo-tabs">
          <el-tab-pane
            v-for="model in params.models"
            :key="model"
            :label="getModelNameById(model)"
            :name="model">
            <!-- <model1 v-model:value="params.modelParams[model]"></model1> -->
            <model-param
              :modelValue="params.modelParams[model]"
              :renderItems="getRenderItemsByModel(model)"
              @update="
                (data) => {
                  updateModelparams(model, data)
                }
              "></model-param>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
    <el-divider>
      <section @click="showModel = !showModel">
        <span>模型参数设置</span>
        <el-icon v-if="showModel">
          <ArrowUp />
        </el-icon>
        <el-icon v-else>
          <ArrowDown />
        </el-icon>
      </section>
    </el-divider>
    <result-list></result-list>
  </section>
  <el-dialog v-model="reset.show" v-if="reset.show" title="选择还原批次" width="40%" align-center>
    <el-select v-model="reset.batchId" placeholder="默认全部批次" clearable>
      <el-option
        v-for="item in searchParam.list"
        :key="item.batchId"
        :label="item.name"
        :value="item.batchId" />
    </el-select>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="resetToTask(false)">取消</el-button>
        <el-button type="primary" @click="resetParams">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import ModelTreeSelect from '@/components/model-tree-select'
import useTaskStore from '@/store/task'
import { ElMessage, ElMessageBox } from 'element-plus'
import { isEmpty, isEqual } from 'lodash-es'
import { splitOptions } from './constant'
import modelParam from './model-param.vue'
import ResultList from './result-list.vue'
const taskStore = useTaskStore()
const taskDetail = computed(() => {
  return taskStore.getTaskById(taskStore.active) || {}
})
const searchParam = computed(() => {
  if (taskDetail.value && taskDetail.value.searchParam) {
    return taskDetail.value.searchParam
  }

  return {
    list: []
  }
})
const getModelConfigById = (id: number) => {
  const models: any = taskStore.models || []
  const res: any = models.find((item: any) => item.id === id) || {}
  return res
}
const getModelNameById = (id: number) => {
  const res = getModelConfigById(id)
  return res?.model || ''
}
const getRenderItemsByModel = (id: number) => {
  const res = getModelConfigById(id)
  return res?.advancedParameters || []
}
const getAllModels = (models: number[]) => {
  return models.map((model) => {
    return {
      id: model,
      name: getModelNameById(model)
    }
  })
}
const state = reactive({
  activeTab: -1
})
const reset = reactive({
  batchId: '',
  show: false
})
const resetToTask = (show: boolean) => {
  reset.show = show
}
const updateModelparams = (model: string, data: any) => {
  params.modelParams[model] = {
    ...data
  }
}
const resetParams = async () => {
  if (!reset.batchId) {
    ElMessage.warning('请选择要还原的批次！')
    return
  }
  const data = await taskStore.getBatchDetail({
    taskId: taskStore.active!,
    batchId: reset.batchId
  })
  const content = data?.batchInfo?.content || {}
  const {
    models = [],
    modelParams = defaultParams.modelParams,
    repeatTimes = defaultParams.repeatTimes,
    vars = defaultParams.vars
  } = content
  if (models.length) {
    const isEveryModelCanUse = models.every((modelItem) =>
      taskStore.models.find((model) => model.id === modelItem.id)
    )
    if (!isEveryModelCanUse) {
      ElMessage.warning('当前批次含有不在当前业务线中的模型，不可还原！')
      return
    }
    params.models = content.models.map((item) => item.id)
  }
  resetToTask(false)
  Object.assign(params, {
    modelParams,
    repeatTimes,
    vars,
    msg: content.msg
  })
}
const detail = computed(() => {
  const task = taskStore.getTaskById(taskStore.active)
  return task
})
const defaultParams: any = {
  msg: '',
  repeatTimes: 1,
  vars: [],
  models: [],
  modelParams: {}
}
const params = reactive({
  ...defaultParams
})
const getModelDefaultParam = (id: number) => {
  const res: any = {}
  const renderItems = getRenderItemsByModel(id)
  renderItems.forEach((item: any) => {
    const { defaultValue, name } = item
    res[name] = defaultValue
  })
  return res
}
watch(
  () => taskStore.models,
  (models = []) => {
    if (params.models.length) {
      return
    }
    const firstModel = models[0]
    params.models.push(firstModel.id)
    const modelParams = getModelDefaultParam(firstModel.id)
    params.modelParams[firstModel.id] = modelParams
  },
  {
    immediate: true
  }
)
watch(
  () => params.models,
  (val: number[]) => {
    // 更新当前modelParams参数
    val.forEach((id: number) => {
      params.modelParams[id] = params.modelParams[id] || getModelDefaultParam(id)
    })
    // 如果activeTab不存在了 则修改为models第一个元素为选中tab
    const { activeTab } = state
    if (!val.includes(activeTab) && val.length) {
      state.activeTab = val[0]
    }
  },
  {
    deep: true,
    immediate: true
  }
)
const initStatus = ref(false)
watch(
  () => detail.value,
  (val) => {
    if (initStatus.value) {
      return
    }
    if (val && val.taskInfo && !isEqual(params, val.taskInfo)) {
      initStatus.value = true
      const content = val.taskInfo?.content || {}
      const modelParams = !isEmpty(content.modelParams)
        ? content.modelParams
        : defaultParams.modelParams
      const repeatTimes = content.repeatTimes || defaultParams.repeatTimes
      const vars = content.vars || defaultParams.vars
      Object.assign(params, {
        modelParams,
        repeatTimes,
        vars: vars,
        msg: content.msg
      })
      if (!isEmpty(content.models)) {
        params.models = content.models.map((item) => item.id)
      }
    }
  },
  {
    deep: true
  }
)
// 间隔一秒钟
let count = 0
let timer: NodeJS.Timeout | null = null
const getIntervalTime = () => {
  // 0-1分 每15s调用一次
  if (count <= 4) {
    return 1000 * 15
  }
  // 1-3分 每 30秒调用一次
  if (count <= 8) {
    return 1000 * 30
  }
  // 之后3-5分 每分钟调用一次
  if (count <= 10) {
    return 1000 * 60
  }
  // 之后每2分钟调用一次
  return 1000 * 60 * 2
}
const getDetail = async () => {
  await taskStore.getTaskResult()
}
onUnmounted(() => {
  if (timer) {
    clearTimeout(timer)
  }
})
const loading = ref(true)
const getTaskStatus = async () => {
  const data = await taskStore.getTaskStatus({
    taskId: taskStore.active!
  })
  const { detailStatus = 1, msg = '', taskId, batchId } = data
  if (detailStatus === 2) {
    // 任务执行中 继续轮询
    loading.value = true
    count += 1
    const timeout = getIntervalTime()
    timer = setTimeout(() => {
      getTaskStatus()
    }, timeout)
    return
  }
  loading.value = false
  if (detailStatus === 1) {
    // 任务执行成功 但是任务结果没有读取
    await ElMessageBox.alert(msg, {
      title: '任务执行结果',
      showClose: false
    })
    await taskStore.updateTaskStatus({
      taskId,
      batchId,
      setRead: true
    })
  }
  taskStore.getTaskDetail(taskStore.active)
  getDetail()
}
getTaskStatus()

const repeatTimeChange = (val: any) => {
  if (val === null) {
    nextTick(() => {
      params.repeatTimes = 1
    })
  }
}
watch(
  () => params.msg,
  (val) => {
    // eslint-disable-next-line no-useless-escape
    const re = /\$\{([^\}]+)\}/g
    const vars: any = {}
    let match: any = re.exec(val)
    while (match !== null) {
      const varName = match[1].trim()
      if (varName) {
        const target: any = params.vars.find((item: any) => item.name === varName) || {}
        const item = {
          name: varName,
          content: '',
          split: '\n',
          ...target
        }
        vars[varName] = item
      }
      match = re.exec(val)
    }
    params.vars = Object.values(vars)
  },
  {
    deep: true
  }
)
const validArrayContent = async () => {
  try {
    const { vars = [] } = params
    vars.every((varItem: any) => {
      const { content = '', split = '' } = varItem
      if (split !== 'array') {
        return true
      } else {
        const array = JSON.parse(content)
        return Array.isArray(array)
      }
    })
  } catch (e) {
    ElMessage.error('变量列表输入内容不合法，请检查！')
    return false
  }
  return true
}
const showModel = ref(false)
const runTask = async () => {
  if (!params.msg) {
    ElMessage.error('请输入发送内容')
    return
  }
  // 校验所有的array的内容是合法的json字符串同时是数组类型
  const isAllPassed = await validArrayContent()
  if (!isAllPassed) {
    return
  }
  const { vars = [], repeatTimes = 1, models = [] } = params
  const modelNum = models.length
  const varsNum = vars.length
  const varItemValues = vars.map((varItem: any) => {
    const { content = '', split = '' } = varItem
    if (split === 'array') {
      const array = JSON.parse(content)
      return array.length
    }
    return content.split(split).length
  })
  const count = [repeatTimes, modelNum, ...varItemValues].reduce((a, b) => a * b)
  const confirmText = `当前任务批次中包含替换词列表${varsNum}个，含有替换词${
    varItemValues.length ? varItemValues : 0
  }个。当前设置重复${repeatTimes}次，选中了${modelNum}个模型。故会产生${varItemValues.join(
    '*'
  )}*${repeatTimes}*${modelNum}=${count}次结果。`
  const action = await ElMessageBox.confirm(confirmText, {
    title: '执行确认'
  })
  if (action === 'confirm') {
    const models = getAllModels(params.models)
    await taskStore.runTask({
      taskId: taskStore.active!,
      content: {
        ...params,
        models
      }
    })
    loading.value = true
    setTimeout(() => {
      getTaskStatus()
    }, 5000)
  }
}
const saveToDraft = async () => {
  if (!params.msg) {
    ElMessage.error('请输入发送内容')
    return
  }
  // 校验所有的array的内容是合法的json字符串同时是数组类型
  const isAllPassed = await validArrayContent()
  if (!isAllPassed) {
    return
  }
  const models = getAllModels(params.models)
  await taskStore.update({
    taskId: taskStore.active!,
    content: {
      ...params,
      models
    }
  })
  ElMessage.success('保存草稿成功')
}
const clearMsg = () => {
  params.msg = ''
}
</script>

<style scoped lang="less">
.page-container {
  padding: 0 !important;

  .title {
    font-weight: 500;
    padding-bottom: 8px;
  }

  .el-row {
    max-height: 280px;

    p {
      padding-bottom: 5px;
    }

    .el-col {
      .vars-list {
        overflow: auto;
        max-height: 280px;

        :deep(.el-form-item__label) {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: inline-block;
          min-width: 90px;
          max-width: 150px;
          width: auto !important;
        }
      }

      .el-form-item {
        margin-bottom: 8px;

        :deep(.el-form-item__content) {
          min-width: 380px;
          align-items: start;

          .el-textarea.el-input--suffix {
            width: 200px;
          }

          .el-select {
            vertical-align: top;
            margin-left: 4px;
          }
        }
      }
    }
  }

  .setting-params {
    padding-top: 12px;
    padding-bottom: 6px;

    .right-col {
      text-align: right;
    }
  }

  .model-params {
    margin-top: 12px;
    padding-top: 16px;
    border-top: 1px solid var(--el-border-color);
    max-height: initial;

    .el-select {
      width: auto !important;
    }
  }

  .el-divider {
    section {
      cursor: pointer;
      display: flex;
      align-items: center;

      .el-icon {
        margin-left: 5px;
      }
    }
  }

  .var-number {
    margin-left: 5px;
  }
}
</style>
