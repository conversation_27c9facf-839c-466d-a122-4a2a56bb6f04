<template>
  <section>
    <section>
      <el-button @click="compare">比较选中</el-button>
      <el-button @click="removeCompare">清除比较</el-button>
      <el-button @click="deleteChecked(true)">删除选中</el-button>
      <el-button @click="deleteChecked(false)">删除未选</el-button>
      <el-button @click="exportChecked">导出选中</el-button>
      <el-button @click="selectAll">{{ isSelectAll ? '取消全选' : '全选' }}</el-button>
      <el-row class="filter-row">
        <el-select
          v-model="state.filter.batches"
          placeholder="默认全部批次"
          clearable
          multiple
          collapse-tags>
          <el-option
            v-for="item in searchParam.list"
            :key="item.batchId"
            :label="item.name"
            :value="item.batchId" />
        </el-select>
        <el-select
          placeholder="默认全部变量"
          v-for="item in vars"
          :key="item.name"
          v-model="state.filter.vars[item.name]"
          clearable
          multiple
          collapse-tags>
          <el-option v-for="value in item.values" :key="value" :label="value" :value />
        </el-select>
        <el-select
          v-model="state.filter.times"
          placeholder="默认全部重复"
          clearable
          multiple
          collapse-tags>
          <el-option v-for="item in times" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="state.sortType">
          <el-option v-for="item in sortMap" :key="item" :label="item.label" :value="item.value" />
        </el-select>
      </el-row>
    </section>
    <el-card
      class="box-card"
      v-for="item in list"
      :key="item.id"
      :class="{ 'active-card': item.id === state.compareId }">
      <template #header>
        <div class="card-header">
          <p>
            <el-checkbox v-model="item.checked" />
            <span>批次:{{ item.batchName || item.batchId }}</span>
            <span v-for="varItem in getVars(item)" :key="varItem.name"
              >{{ varItem.name }} : {{ varItem.value }}</span
            >
            <span>重复 : {{ item.time }}</span>
            <span>模型:{{ item.modelName }}</span>
          </p>
          <section class="space-b">
            <p style="margin-right: 12px">回复字数: {{ item.apiResult.length }}</p>
            <el-tooltip placement="top">
              <template #content>
                <p>Prompt: {{ item.apiParam }}</p>
                <p v-if="item.param.modelParam[item.modelId].temperature">
                  temperature: {{ item.param.modelParam[item.modelId].temperature }}
                </p>
                <p v-if="item.param.modelParam[item.modelId].top_p">
                  top_p: {{ item.param.modelParam[item.modelId].top_p }}
                </p>
                <p v-if="item.param.modelParam[item.modelId].max_tokens">
                  max_tokens: {{ item.param.modelParam[item.modelId].max_tokens }}
                </p>
                <p v-if="item.param.modelParam[item.modelId].presence_penalty">
                  presence_penalty: {{ item.param.modelParam[item.modelId].presence_penalty }}
                </p>
                <p v-if="item.param.modelParam[item.modelId].frequency_penalty">
                  frequency_penalty: {{ item.param.modelParam[item.modelId].frequency_penalty }}
                </p>
              </template>
              <el-icon>
                <InfoFilled />
              </el-icon>
            </el-tooltip>
          </section>
        </div>
      </template>
      <AssistantMessage class="result-container" v-if="!state.baseString" :text="item.apiResult" />
      <CompareStr v-else :baseStr="state.baseString" :compareStr="item.apiResult"></CompareStr>
    </el-card>
  </section>
</template>
<script lang="ts" setup>
import useTaskStore from '@/store/task'
import { ElMessage, ElMessageBox } from 'element-plus'
import CompareStr from './compare-str.vue'
import { cloneDeep } from 'lodash-es'
import AssistantMessage from '@/views/mark/rate/assistantMessage'
import { BatchVar, SearchDetailListItem } from '@/api/task/type'
type IList = IListItem[]
type IListItem = SearchDetailListItem & { checked?: boolean }
interface IState {
  baseString: string
  compareId?: number
  sortType: number
  detail: SearchDetailListItem
  filter: {
    batches: number[]
    vars: Record<string, string[]>
    times: number[]
  }
}
const taskStore = useTaskStore()
const taskDetail = computed(() => {
  return taskStore.getTaskById(taskStore.active) || {}
})
const searchParam = computed(() => {
  if (taskDetail.value && taskDetail.value.searchParam) {
    return taskDetail.value.searchParam
  }

  return {
    list: []
  }
})
const state = reactive<IState>({
  baseString: '',
  compareId: void 0,
  detail: {} as SearchDetailListItem,
  filter: {
    batches: [],
    vars: {},
    times: []
  },
  sortType: 1
})
const sortMap = [
  {
    label: '批次升序',
    value: 1,
    key: 'createTime'
  },
  {
    label: '批次降序',
    value: 2,
    key: 'createTime'
  },
  {
    label: '模型升序',
    value: 3,
    key: 'modelName'
  },
  {
    label: '模型降序',
    value: 4,
    key: 'modelName'
  }
]
const vars = computed(() => {
  const { list = [] } = searchParam.value
  let res: BatchVar[] = []
  list.forEach((item) => {
    if (state.filter.batches.length && !state.filter.batches.includes(item.batchId)) {
      return
    }
    const { vars = [] } = item
    vars.forEach((varItem) => {
      /**
       * 此处直接使用varItem会导致修改searchParam，从而导致修改store.folderMap
       * 而代码中很多次使用watch和computed依赖于store.folderMap
       * 会造成微任务堆积过多，页面卡死
       * */
      const varItemClone = cloneDeep(varItem)
      const target = res.find((resItem) => resItem.name === varItemClone.name)
      if (target) {
        const { values = [] } = target
        const allValues = values.concat(varItemClone.values)
        target.values = [...new Set(allValues)]
      } else {
        res.push(varItemClone)
      }
    })
  })
  return res
})
const times = computed(() => {
  const { list = [] } = searchParam.value
  let res: number[] = []
  list.forEach((item) => {
    const { times } = item
    res = res.concat(times)
  })
  return [...new Set(res)]
})
const list = computed(() => {
  let list = taskStore.getResultListByTaskId(taskStore.active)
  if (state.filter.batches.length) {
    list = list.filter((item) => state.filter.batches.includes(item.batchId))
  }
  if (state.filter.times.length) {
    list = list.filter((item) => state.filter.times.includes(item.time))
  }
  if (state.filter.vars) {
    Object.keys(state.filter.vars).forEach((name) => {
      const values = state.filter.vars[name] || []
      if (values.length) {
        list = list.filter((item) => values.includes(item.param.vars[name]))
      }
    })
  }
  const res: IList = []
  let target: SearchDetailListItem | undefined
  list.forEach((item) => {
    if (state.compareId && item.id == state.compareId) {
      target = item
      return
    }
    res.push(item)
  })
  sort(res)
  if (target) {
    res.unshift(target)
  }
  return res
})
const sort = (list: IList) => {
  const { key, value } = sortMap.find((item) => item.value === state.sortType) || {}
  list.sort((a, b) => {
    // 升序
    const isAscendingOrder = value! % 2 === 1
    const preValue = a[key as keyof IListItem]
    const nextValue = b[key as keyof IListItem]
    const res = isAscendingOrder ? preValue! > nextValue! : nextValue! > preValue!
    return res ? 1 : -1
  })
}
const getVars = (item: IListItem) => {
  const vars = item?.param?.vars || {}
  const res: { name: string; value: string }[] = []
  Object.keys(vars).forEach((key) => {
    res.push({
      name: key,
      value: vars[key]
    })
  })
  return res
}
watch(
  () => state.filter.batches,
  () => {
    state.filter.times = []
    state.filter.vars = {}
  }
)
const compare = () => {
  //首先判断选中的有几个结果 只能选中一个结果的时候才能比较
  let checkedNumbers: SearchDetailListItem[] = []
  list.value.forEach((item) => {
    if (item.checked) {
      checkedNumbers.push(item)
    }
  })
  const length = checkedNumbers.length
  if (length === 0) {
    ElMessage.warning('请选择要比较的结果')
    return
  }
  if (length > 1) {
    ElMessage.warning('请选择一条结果进行比较')
    return
  }
  const baseTask = checkedNumbers[0]
  state.detail = {
    ...baseTask
  }
  state.baseString = baseTask.apiResult
  state.compareId = baseTask.id
}
const removeCompare = () => {
  state.baseString = ''
  state.compareId = void 0
  state.detail = {} as SearchDetailListItem
}
const deleteChecked = async (isDeleteChecked = true) => {
  const idList = list.value.filter((item) => {
    return !!item.checked === isDeleteChecked
  })
  if (!idList.length) {
    ElMessage.warning('当前没有要删除的结果！')
    return
  }
  const action = await ElMessageBox.confirm('确认删除吗？', {
    title: '删除确认'
  })
  if (action === 'confirm') {
    const ids = idList.map((item) => item.id)
    await taskStore.delDetail({
      taskId: taskStore.active!,
      ids
    })
    if (ids.includes(state.compareId!)) {
      removeCompare()
    }
    taskStore.getTaskResult()
  }
}
const exportChecked = async () => {
  const idList = list.value.filter((item) => item.checked)
  if (!idList.length) {
    ElMessage.warning('请选中要导出的结果！')
    return
  }
  const ids = idList.map((item) => item.id)
  await taskStore.exportDetail(ids)
}
const selectAll = () => {
  const selected = !isSelectAll.value
  list.value.forEach((item) => {
    item.checked = selected
  })
}
const isSelectAll = computed(() => {
  if (!list.value.length) {
    return false
  }
  return list.value.every((item) => {
    return item.checked === true
  })
})
</script>

<style scoped lang="less">
section {
  padding-bottom: 8px;
}

.box-card {
  margin-top: 8px;
  cursor: pointer;

  &.active-card {
    position: sticky;
    top: -20px;
    z-index: 100;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  label {
    vertical-align: bottom;
  }

  span {
    margin-left: 10px;
    border-right: 1px solid #797b84;
    padding-right: 10px;
    max-width: 210px;
    display: inline-block;
    text-overflow: ellipsis;
    /* word-break: break-all; */
    white-space: nowrap;
    overflow: hidden;
  }
}

.result-container {
  white-space: pre-line;
}

.filter-row {
  margin-top: 8px;

  .el-select {
    width: 165px;
    margin-right: 8px;
    margin-bottom: 6px;
  }
}
</style>
