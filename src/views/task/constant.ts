import { TaskTypeEnum } from '@/api/task/type'
import Detail from './detail.vue'
import BatchDetail from './batch-detail.vue'
import ApiBatchDetail from './api-batch-detail'

export const splitOptions = [
  {
    name: '换行符',
    value: '\n'
  },
  {
    name: 'Tab符',
    value: '\t'
  },
  {
    name: '英文逗号',
    value: ','
  },
  {
    name: '数组',
    value: 'array'
  }
]

export const modelOptions = [
  {
    name: 'gpt-3.5-turbo',
    id: 1
  }
]

export const CreateTaskMap: Record<TaskTypeEnum, { name: string; component: Component }> = {
  [TaskTypeEnum.Task]: { name: 'New Task', component: Detail },
  [TaskTypeEnum.Batch]: { name: 'New Batch', component: BatchDetail },
  [TaskTypeEnum.ApiBatch]: { name: 'New Api Batch', component: ApiBatchDetail }
}
