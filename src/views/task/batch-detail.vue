<template>
  <section
    class="page-container"
    element-loading-text="任务执行中"
    element-loading-background="rgba(255, 255, 255, 0.7)">
    <el-form label-width="100">
      <el-form-item
        v-if="!notInit"
        class="flex items-center"
        style="margin-bottom: 12px"
        label="任务状态">
        <el-tag disable-transitions :type="statusToType">{{ statusText }}</el-tag>
        <el-tooltip
          v-if="status === Status.Failed"
          effect="dark"
          :content="detail?.promptBatch.failReason">
          <el-button link icon="QuestionFilled"></el-button>
        </el-tooltip>
      </el-form-item>
      <el-form-item
        v-if="!notInit && detail?.promptBatch?.total"
        class="flex items-center w-[420px]"
        style="margin-bottom: 12px"
        label="执行进度">
        <div class="progress-container w-full flex items-center">
          <div class="progress-bar flex-1">
            <div
              class="progress-success"
              :style="{
                width: `${((detail.promptBatch.success || 0) / detail.promptBatch.total) * 100}%`
              }">
              <span v-if="detail.promptBatch.success" class="progress-text">
                {{ detail.promptBatch.success }}</span
              >
            </div>
            <div
              class="progress-fail"
              :style="{
                width: `${((detail.promptBatch.fail || 0) / detail.promptBatch.total) * 100}%`
              }">
              <span v-if="detail.promptBatch.fail" class="progress-text">
                {{ detail.promptBatch.fail }}</span
              >
            </div>
            <div class="progress-total">总数: {{ detail.promptBatch.total }}</div>
          </div>
          <el-button
            class="ml-2"
            type="primary"
            link
            size="small"
            @click="showModelProcess = !showModelProcess">
            <el-icon>
              <component :is="showModelProcess ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
            {{ showModelProcess ? '收起' : '详情' }}
          </el-button>
        </div>
      </el-form-item>

      <!-- 模型处理详情表格 -->
      <el-collapse-transition>
        <div
          v-if="showModelProcess && detail?.promptBatch?.modelProcess"
          class="model-process-table mb-4">
          <el-table :data="detail.promptBatch.modelProcess" border stripe size="small">
            <el-table-column prop="modelName" label="模型名称" min-width="120" />
            <el-table-column prop="total" label="总数" width="80" />
            <el-table-column prop="success" label="成功" width="80">
              <template #default="{ row }">
                <span class="text-success">{{ row.success || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="fail" label="失败" width="80">
              <template #default="{ row }">
                <span class="text-danger">{{ row.fail || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="未执行" width="80">
              <template #default="{ row }">
                <span class="text-info">{{
                  row.total - (row.success || 0) - (row.fail || 0)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="成功率" width="100">
              <template #default="{ row }">
                {{ row.total ? Math.round(((row.success || 0) / row.total) * 100) + '%' : '0%' }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-collapse-transition>

      <el-form-item
        v-if="!notInit"
        class="flex items-center"
        style="margin-bottom: 12px"
        label="创建时间">
        {{
          state.batchDetail?.batchInfo?.createTime
            ? dayjs.unix(state.batchDetail?.batchInfo?.createTime).format('YYYY-MM-DD HH:mm')
            : ''
        }}
      </el-form-item>
      <el-form-item v-if="notInit" class="flex items-start" label="上传文件">
        <el-upload
          ref="uploadRef"
          v-model:file-list="fileList"
          class="upload-demo"
          :auto-upload="false"
          :on-remove="handleRemove"
          :limit="1"
          accept=".xlsx,.json"
          action="/openmis/task/promptbatch/import"
          :on-success="uploadSuccess">
          <el-button icon="Folder" size="small" type="primary" :disabled="!!fileList.length">
            选择文件
          </el-button>
          <el-button
            :loading="getTaskTplLoading"
            icon="Download"
            @click.stop="downLoadTplExcel"
            size="small"
            type="success">
            xlsx格式参考
          </el-button>
          <el-button
            :loading="getTaskTplLoading"
            icon="Download"
            @click.stop="downLoadTplJson"
            size="small"
            type="success">
            json格式参考
          </el-button>
        </el-upload>
      </el-form-item>
      <el-form-item class="flex items-center" v-if="showTaskResult" label="任务结果">
        <template #label>
          <div class="flex items-center">
            <span>任务结果</span>
            <el-tooltip effect="dark" content="下载数据为即时结果数据" placement="top">
              <el-icon class="ml-1 text-info cursor-help"><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <el-button @click="downloadResult(ExportType['Excel'])" type="primary" size="small"
          >下载Excel文件</el-button
        >
        <el-button @click="downloadResult(ExportType['Json'])" type="primary" size="small"
          >下载JSON文件</el-button
        >
      </el-form-item>
      <div v-if="notInit" class="flex justify-end">
        <el-button @click="confirm" type="primary">执行</el-button>
      </div>
      <div v-if="isFailed" class="flex justify-end">
        <el-button @click="runTaskAgain" type="primary">再次执行</el-button>
      </div>
      <div v-if="isRunningOrWaiting" class="flex justify-end">
        <el-button @click="cancelBatchTaskAction" type="primary">取消执行</el-button>
      </div>
    </el-form>
    <el-row v-if="showModel" class="model-params">
      <el-col :span="24">
        <el-form-item label="模型">
          <ModelTreeSelect
            multiple
            :style="{ minWidth: '100%' }"
            :disabled="!notInit"
            :data="taskStore.companyListId"
            :modelValue="models"
            @change="(e: number[]) => {
        if (!e.length) {
          models = [models[0]]
        } else {
          models = e
        }
      }" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-tabs v-model="state.activeTab" class="demo-tabs">
          <el-tab-pane
            v-for="model in models"
            :key="model"
            :label="getModelNameById(model)"
            :name="model">
            <model-param
              :modelValue="params.modelParams[model]"
              :renderItems="getRenderItemsByModel(model)"
              :disabled="!notInit"
              :init="!notInit"
              @update="
                (data) => {
                  updateModelparams(model, data)
                }
              "></model-param>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
    <el-divider>
      <section @click="showModel = !showModel">
        <span>模型参数设置</span>
        <el-icon v-if="showModel">
          <ArrowUp />
        </el-icon>
        <el-icon v-else>
          <ArrowDown />
        </el-icon>
      </section>
    </el-divider>
  </section>
</template>
<script lang="ts" setup>
import { retryTask, useGetTaskTpl, cancelBatchTask } from '@/api/task'
import ModelTreeSelect from '@/components/model-tree-select'
import { useOpenUrl } from '@/hooks/useBusinessCode'
import useTaskStore from '@/store/task'
import type { UploadFile, UploadFiles, UploadInstance } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'
import modelParam from './model-param.vue'
import { ModelParamsItem } from '@/api/business/type'
import { ModelsItemParams } from '@/api/marktask/type'
import { ResponseData } from '@/plugin/axios/interceptors'
import { BatchDetailRet, TaskVar } from '@/api/task/type'
import dayjs from 'dayjs'
const { openUrl } = useOpenUrl()
enum Status {
  WaitToInit,
  Init,
  Failed,
  Success,
  Running
}
const uploadRef = ref<UploadInstance>()
const taskStore = useTaskStore()
const getTaskDetail = () => taskStore.getTaskDetail(taskStore.active)
const fileList: Ref<UploadFiles> = ref([])
let uploadId: number | null = null
let uploadName: string = ''
const handleRemove = () => {
  fileList.value = []
  uploadId = null
  uploadName = ''
}

const detail = computed(() => taskStore.getTaskById(taskStore.active) || {})
const status = computed(
  () => (detail.value?.promptBatch?.batchStatus || 0) as keyof typeof statusMapText
)
const showTaskResult = computed(() => {
  const { promptBatch } = detail.value
  return promptBatch && (promptBatch.success > 0 || promptBatch.fail > 0)
})
const { pause, resume } = useIntervalFn(() => getTaskDetail(), 10000, { immediate: false })
watchImmediate(status, (val) => {
  if (val === Status['Init'] || val === Status['Running']) {
    resume()
  } else {
    pause()
  }
})
const statusMapText = {
  0: '排队中',
  1: '排队中',
  2: '执行失败',
  3: '执行成功',
  4: '执行中'
}
const statusText = computed(() => statusMapText[status.value])
const statusMapType = {
  0: 'info',
  1: 'info',
  2: 'danger',
  3: 'success',
  4: 'primary'
}
const statusToType = computed(() => statusMapType[status.value])

const batchId = computed<string>(() => {
  const batchId = detail.value.promptBatch?.batchId
  return batchId === '0' ? '' : batchId
})

const state = reactive({
  activeTab: -1,
  batchDetail: {} as BatchDetailRet
})
const getBatchDetail = async (id: string) => {
  const data = await taskStore.getBatchDetail({
    taskId: taskStore.active!,
    batchId: id
  })
  state.batchDetail = data
  const modelParams = data?.batchInfo?.content?.modelParams || {}
  params.modelParams = modelParams
}
const updateModelparams = (model: string, data: any) => {
  params.modelParams[model] = {
    ...data
  }
}
invoke(async () => {
  await until(batchId).toBeTruthy()
  getBatchDetail(batchId.value)
})

const notInit = computed(() => status.value === Status.WaitToInit)
const isFailed = computed(() => status.value === Status.Failed)
const isRunningOrWaiting = computed(
  () => status.value === Status.Running || status.value === Status.Init
)
const {
  mutate: getTaskTplMutate,
  isLoading: getTaskTplLoading,
  data: taskTplData
} = useGetTaskTpl()

whenever(notInit, getTaskTplMutate, { immediate: true })
const downLoadTplExcel = () => window.open(taskTplData.value?.newBatch.xlsxUrl)
const downLoadTplJson = () => window.open(taskTplData.value?.newBatch.jsonUrl)

const params = reactive<{
  msg: string
  repeatTimes: number
  vars: TaskVar[]
  modelParams: any
}>({
  msg: '',
  repeatTimes: 1,
  vars: [],
  modelParams: {}
})

const getModelConfigById = (id: number) => {
  const models = taskStore.models
  const res = models?.find((item) => item.id === id) || {}
  return res as ModelParamsItem
}
const getModelNameById = (id: number) => getModelById(id).model || ''
const getModelDescById = (id: number) => getModelById(id).desc || ''
const getModelById = (id: number) => getModelConfigById(id)
const getRenderItemsByModel = (id: number) => {
  const res = getModelConfigById(id)
  return res.advancedParameters || []
}
const getAllModels = (models: number[]) =>
  models.map((model) => ({
    id: model,
    name: getModelNameById(model),
    desc: getModelDescById(model)
  }))

const getModelDefaultParam = (id: number) => {
  const renderItems = getRenderItemsByModel(id)
  const res = renderItems.reduce((pre, item) => {
    const { defaultValue, name } = item
    pre[name] = defaultValue
    return pre
  }, {} as Record<keyof ModelsItemParams, number>)
  return res
}
const models = computed({
  get() {
    const { modelParams = {} } = params
    return Object.keys(modelParams).map((key) => +key)
  },
  set(data: any) {
    const { modelParams = {} } = params
    const newParams: any = {}
    data.forEach((id: number) => {
      newParams[id] = modelParams[id] || getModelDefaultParam(id)
    })
    params.modelParams = newParams
  }
})
watchImmediate(
  () => taskStore.models,
  (allModels = []) => {
    if (models.value.length) {
      return
    }
    const target: any = allModels.find((item) => item.status === 1)
    const { id } = target
    const modelParams = getModelDefaultParam(id)
    params.modelParams[id] = modelParams
  }
)
watch(
  () => models.value,
  (val: number[]) => {
    // 如果activeTab不存在了 则修改为models第一个元素为选中tab
    const { activeTab } = state
    if (!val.includes(activeTab) && val.length) {
      state.activeTab = val[0]
    }
  },
  {
    deep: true,
    immediate: true
  }
)

invoke(() => getTaskDetail())

const showModel = ref(false)
const confirm = async () => {
  if (!fileList.value.length) {
    ElMessage.error('请先上传文件')
    return
  }
  if (uploadId) {
    runTask(uploadId)
  } else {
    uploadRef.value!.submit()
  }
}
const runTaskAgain = async () => {
  await retryTask({
    taskId: taskStore.active!,
    batchId: batchId.value
  })
  getTaskDetail()
}
const cancelBatchTaskAction = async () => {
  try {
    await ElMessageBox.confirm('确认取消？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await cancelBatchTask({
      taskId: taskStore.active!,
      batchId: batchId.value
    })
    getTaskDetail()
  } catch {
    // 用户点击取消按钮时不做任何操作
  }
}
const enum ExportType {
  Excel = 1,
  Json
}
const downloadResult = (exportType: ExportType) =>
  openUrl('/openmis/task/promptbatch/export', {
    taskId: taskStore.active,
    batchId: batchId.value,
    exportType
  })

const showModelProcess = ref(false)
const runTask = async (id: number) => {
  await taskStore.runTask({
    taskId: taskStore.active!,
    content: {
      ...params,
      models: getAllModels(models.value),
      uploadId: id
    },
    fileName: uploadName
  })
  taskStore.getTaskById(taskStore.active).name = uploadName.slice(0, uploadName.lastIndexOf('.'))
  getTaskDetail()
}
const uploadSuccess = async (res: ResponseData<{ id: number }>, uploadFile: UploadFile) => {
  const { errNo = 0, errMsg = '' } = res
  if (errNo !== 0) {
    ElMessage.error(errMsg || '上传失败！')
    return
  }
  const id = res.data.id
  uploadId = id
  uploadName = uploadFile.name
  await runTask(id)
  getTaskDetail()
}
</script>

<style scoped lang="less">
.page-container {
  padding: 0 24px !important;

  .title {
    font-weight: 500;
    padding-bottom: 8px;
  }

  .el-row {
    max-height: 280px;

    p {
      padding-bottom: 5px;
    }

    .el-col {
      .upload-demo {
        min-width: 380px;
      }
    }
  }

  .model-params {
    margin-top: 12px;
    padding-top: 16px;
    border-top: 1px solid var(--el-border-color);
    max-height: initial;

    .el-select {
      width: auto !important;
    }
  }

  .el-divider {
    section {
      cursor: pointer;
      display: flex;
      align-items: center;

      .el-icon {
        margin-left: 5px;
      }
    }
  }

  // 进度条样式
  .progress-container {
    width: 100%;

    .progress-bar {
      height: 24px;
      background-color: #f0f0f0;
      border-radius: 4px;
      overflow: hidden;
      position: relative;
      display: flex;
      width: 100%;

      .progress-success {
        height: 100%;
        background-color: var(--el-color-success);
        transition: width 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 2;
      }

      .progress-fail {
        height: 100%;
        background-color: var(--el-color-danger);
        transition: width 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 2;
      }

      .progress-total {
        position: absolute;
        left: 10px;
        top: 0;
        height: 100%;
        display: flex;
        align-items: center;
        color: #606266;
        z-index: 1;
      }

      .progress-text {
        color: #fff;
        font-size: 12px;
        white-space: nowrap;
      }
    }
  }

  // 模型处理详情表格样式
  .model-process-table {
    margin-left: 70px;
    width: calc(100% - 120px);

    :deep(.el-table) {
      --el-table-border-color: var(--el-border-color-lighter);
      --el-table-header-bg-color: var(--el-fill-color-light);

      .text-success {
        color: var(--el-color-success);
      }

      .text-danger {
        color: var(--el-color-danger);
      }

      .text-info {
        color: var(--el-color-info);
      }
    }
  }

  // 帮助图标样式
  .cursor-help {
    cursor: help;
  }

  .text-info {
    color: var(--el-color-info);
  }
}
</style>
