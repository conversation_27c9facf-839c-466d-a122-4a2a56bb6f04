<template>
  <SearchContainer>
    <el-form inline="">
      <el-form-item class="!w-[240px]" label="用户">
        <el-select filterable v-model="state.query.uname">
          <el-option
            v-for="item in state.options.user"
            :key="item.uname"
            :label="item.name"
            :value="item.uname" />
        </el-select>
      </el-form-item>
      <el-form-item class="!w-[240px]" label="时间">
        <el-select v-model="state.query.relativeTimeRange">
          <el-option
            v-for="item in state.options.timeRange"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item class="!w-[240px]" label="请求来源">
        <el-select v-model="state.query.requestFrom" clearable>
          <el-option
            v-for="item in state.options.requestFrom"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
  </SearchContainer>
  <el-alert type="warning" @close="closeAlert" close-text="关闭提醒" v-if="showAlert">
    <template #title>
      更长时间的用量使用情况参考<el-link
        href="https://bluewhale.zuoyebang.cc/#/space/s_bd35d4e0e3de11edbb841be9a19c8f83/my-report/report-list?archor=r_84b5da00ee5311eda8346f2cb3fef8c2"
        type="primary"
        target="_blank"
        >蓝鲸报表</el-link
      >。申请蓝鲸报表权限的方法参考<el-link
        type="primary"
        href="https://docs.zuoyebang.cc/doc/1833461086209024002?ddtab=true"
        target="_blank"
        >FAQ</el-link
      >。
    </template>
  </el-alert>
  <el-row :gutter="24">
    <el-col :span="24">
      <Line
        type="api"
        :uid="state.query.uname"
        :requestFrom="state.query.requestFrom"
        :relativeTimeRange="state.query.relativeTimeRange"></Line>
    </el-col>
    <el-col :span="24">
      <Line
        type="token"
        :uid="state.query.uname"
        :requestFrom="state.query.requestFrom"
        :relativeTimeRange="state.query.relativeTimeRange"></Line>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import $http from '@/api'
// import { ElMessage } from 'element-plus'
import useUserStore from '@/store/user'
import Line from './line.vue'
import { RoleId } from '@/api/user/type'
import { debounce } from 'lodash-es'
import SearchContainer from '@/components/search-container'
const alertItemname = 'alert_close_time'
const showAlert = computed(() => {
  let time = localStorage.getItem(alertItemname) || 0
  const currentTime = new Date().getTime()
  const oneMonth = 30 * 24 * 60 * 60 * 1000
  if (time && currentTime - +time <= oneMonth) {
    return false
  }
  return true
})
const closeAlert = () => {
  const currentTime = new Date().getTime()
  localStorage.setItem(alertItemname, currentTime.toString())
}
const userStore = useUserStore()
const state: any = reactive({
  type: 1,
  query: {
    relativeTimeRange: 1,
    uname: 0,
    requestFrom: ''
  },
  detail: {},
  options: {
    user: [
      {
        name: '全业务线',
        uname: 0
      }
    ],
    timeRange: [
      {
        label: '当天',
        value: 1
      },
      {
        label: '近一周',
        value: 2
      }
    ],
    requestFrom: [
      {
        label: 'api',
        value: 'api'
      },
      {
        label: 'web',
        value: 'web'
      }
    ]
  },
  pageNum: 1,
  hasNextPage: false
})
const getList = async () => {
  const params = {
    pageNum: state.pageNum,
    pageSize: 100,
    status: 2,
    businessId: userStore.current.businessId
  }
  const { list = [], total = 0 } = await $http.getUserList(params)
  state.options.user = state.options.user.concat(list)
  if (total > 100 * state.pageNum) {
    state.pageNum++
    state.hasNextPage = true
  } else {
    state.hasNextPage = false
  }
}
const { current, name = '' } = userStore
if (userStore.isGuest) {
  state.options.user = [
    {
      name: name,
      uname: name
    }
  ]
  state.query.uname = name
} else if (current.roleId === RoleId.User) {
  state.options.user.push({
    name: name,
    uname: name
  })
} else {
  getList()
}
const offset = 200
const debounceTime = 75
const handler = debounce(() => {
  if (state.hasNextPage) {
    getList()
  }
}, debounceTime)
onMounted(() => {
  // vue3中方式
  let dom: any = document
    .querySelectorAll('.el-select-dropdown')[0]
    .querySelector('.el-scrollbar__wrap')

  dom.addEventListener('mousewheel', function () {
    if (dom.scrollTop + dom.clientHeight + offset >= dom.scrollHeight) {
      handler()
    }
  })
})
</script>
<style scoped lang="less">
.table-container {
  border-radius: 4px;
  background: white;
  padding: 12px;
}

.el-alert {
  margin-bottom: 12px;
  overflow: visible;
  .el-link {
    position: relative;
    top: -2px;
    padding: 0 3px;
  }
}
</style>
