<template>
  <section ref="chart" :inline="true" :model="state.query"></section>
</template>

<script lang="ts" setup>
import $http from '@/api'
import * as echarts from 'echarts/core'
import {
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  MarkLineComponent,
  MarkPointComponent
} from 'echarts/components'
import { LineChart } from 'echarts/charts'
import { UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import useUserStore from '@/store/user'

echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  <PERSON><PERSON>ineComponent,
  MarkPointComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition
])
const userStore = useUserStore()
const state: any = reactive({})
const props = defineProps({
  relativeTimeRange: {
    type: Number,
    value: 1
  },
  uid: {
    type: Number || String,
    value: 0
  },
  type: {
    type: String,
    value: ''
  },
  requestFrom: {
    type: String,
    value: ''
  }
})
let myChart: any = ''
const titleMap: any = {
  api: '请求量折线图',
  token: 'Token量折线图'
}
const getData = async () => {
  const params: any = {
    ...props,
    censusType: props.type,
    businessId: userStore.current.businessId
  }
  params.uname = params.uid || undefined
  const data = await $http.censusUsage(params)
  const { xAxis, series } = data
  const type = props.type || 'api'
  const option: any = {
    title: {
      text: titleMap[type]
    },
    xAxis: {
      type: 'category',
      data: xAxis.data
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: series[0].data,
        type: 'line'
      }
    ]
  }
  myChart.setOption(option)
}
getData()
watch(
  () => [props.relativeTimeRange, props.uid, props.requestFrom],
  () => {
    getData()
  },
  {
    immediate: true
  }
)
const chart = ref()
onMounted(() => {
  const chartDom = chart.value
  myChart = echarts.init(chartDom)
})
</script>
<style scoped lang="less">
section {
  height: 350px;
}
</style>
