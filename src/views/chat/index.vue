<template>
  <section>
    <iframe :src="url" frameborder="0" width="100%" height="100%" id="chat-iframe"></iframe>
  </section>
</template>
<script lang="ts" setup>
import useUserStore from '@/store/user'

const userStore = useUserStore()
const { origin, port } = location
let url = `${origin}/static/fe-chat/index.html`
// 根据port存在 则为本地开发环境
if (port) {
  url = import.meta.env.VITE_CHAT_URL
}
const getFrame = () => document.querySelector('#chat-iframe')! as HTMLObjectElement
const onMessage = async (event: any) => {
  const { data = {} } = event
  const { name } = data
  if (name === 'refresh-summary') {
    await userStore.updateBusSummary()
    const frame = getFrame()
    const contentWindow = frame.contentWindow!
    if (!userStore.summary) return
    contentWindow.postMessage(
      {
        name: 'update-summary',
        canUseTimes: userStore.summary.userApiCntLimit - userStore.summary.userApiCntNow
      },
      '*'
    )
  }
}
onMounted(() => {
  const frame = getFrame()
  frame.onload = function () {
    const contentWindow = frame.contentWindow!
    contentWindow.postMessage(
      {
        business: userStore.current.businessCode,
        name: 'bootstrap',
        user: {
          uname: userStore.name,
          zhName: userStore.zhName
        }
      },
      '*'
    )
    if (!userStore.summary) return
    contentWindow.postMessage(
      {
        name: 'update-summary',
        canUseTimes: userStore.summary.userApiCntLimit - userStore.summary.userApiCntNow
      },
      '*'
    )
  }
  window.addEventListener('message', onMessage)
})
onBeforeUnmount(() => window.removeEventListener('message', onMessage))
</script>

<style scoped lang="less">
section {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
</style>
