<template>
  <div class="batch-management">
    <h1 class="bg-white p-3 rounded-t font-bold border-b border-gray-200">ApiBatch任务管理</h1>
    <el-tabs v-model="activeTab" class="bg-white px-4 py-3 rounded-b" @tab-click="handleTabClick">
      <el-tab-pane label="用户任务" name="userTasks">
        <user-tasks ref="userTasksRef" />
      </el-tab-pane>
      <el-tab-pane label="调度任务" name="scheduledTasks">
        <schedule-tasks ref="scheduleTasksRef" />
      </el-tab-pane>
      <el-tab-pane label="Prompt任务" name="promptTasks">
        <prompt-tasks ref="promptTasksRef" />
      </el-tab-pane>
      <el-tab-pane label="Mark任务" name="markTasks">
        <mark-tasks ref="markTasksRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import UserTasks from './components/UserTasks.vue'
import ScheduleTasks from './components/ScheduleTasks.vue'
import PromptTasks from './components/PromptTasks.vue'
import MarkTasks from './components/MarkTasks.vue'

const activeTab = ref('userTasks')
const userTasksRef = ref()
const scheduleTasksRef = ref()
const promptTasksRef = ref()
const markTasksRef = ref()

const handleTabClick = () => {
  if (activeTab.value === 'userTasks') {
    userTasksRef.value?.fetchGlobalConfig()
  } else if (activeTab.value === 'scheduledTasks') {
    scheduleTasksRef.value?.fetchGlobalConfig()
  } else if (activeTab.value === 'promptTasks') {
    promptTasksRef.value?.fetchGlobalConfig()
  } else if (activeTab.value === 'markTasks') {
    markTasksRef.value?.fetchGlobalConfig()
  }
}
</script>

<style lang="scss" scoped>
.batch-management {
  :deep(.el-tabs__content) {
    background-color: #fff;
    border-radius: 4px;
  }
}
</style>
