<template>
  <!-- 状态标签 -->
  <div class="status-tabs">
    <el-radio-group v-model="statusTab">
      <el-radio-button :label="0">全部</el-radio-button>
      <el-radio-button :label="2">执行中 ({{ runningCount }})</el-radio-button>
      <el-radio-button :label="5">执行失败 ({{ failedCount }})</el-radio-button>
      <el-radio-button :label="4">执行成功 ({{ successCount }})</el-radio-button>
    </el-radio-group>
  </div>

  <!-- 搜索栏 -->
  <div class="search-section">
    <el-form :inline="true" :model="searchForm">
      <el-form-item label="模型" class="min-w-[200px]">
        <el-select v-model="searchForm.model" placeholder="请选择模型" clearable filterable>
          <el-option
            v-for="item in modelList"
            :key="item.value"
            :label="item.key"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务线" class="min-w-[200px]">
        <el-select
          v-model="searchForm.businessCode"
          placeholder="请选择任务类别"
          clearable
          filterable>
          <el-option
            v-for="item in businessList"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="用户" class="min-w-[200px]">
        <el-select v-model="searchForm.uname" placeholder="请选择用户" clearable filterable>
          <el-option
            v-for="item in userList"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch" class="mr-2">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>

  <!-- 表格 -->
  <el-table :data="tableData" style="width: 100%">
    <el-table-column prop="id" label="ID" width="80" />
    <el-table-column prop="businessName" label="业务线名称" />
    <el-table-column prop="modelName" label="模型名称" />
    <el-table-column prop="priority" label="优先级" />
    <el-table-column prop="uname" label="创建人" />
    <el-table-column prop="status" label="状态">
      <template #default="{ row }">
        <section class="flex justify-between items-center">
          <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
          <el-tooltip v-if="row.failReason" effect="dark" placement="top">
            <template #content>
              <span class="inline-block max-w-[70vw] overflow-auto max-h-[70vh]">
                {{ row.failReason }}
              </span>
            </template>
            <el-button text>
              <el-icon class="text-red-400"><QuestionFilled /></el-icon>
            </el-button>
          </el-tooltip>
        </section>
      </template>
    </el-table-column>
    <el-table-column label="请求情况">
      <template #default="{ row }">
        <section class="flex gap-2 items-center">
          <div class="request-status">
            <span class="text-danger">{{ row.failed }}</span>
            <span class="separator">/</span>
            <span class="text-success">{{ row.completed }}</span>
            <span class="separator">/</span>
            <span>{{ row.total }}</span>
          </div>
          <el-tooltip v-if="row.modelProcess" effect="dark" placement="top">
            <template #content>
              <section class="inline-block max-w-[70vw] overflow-auto max-h-[70vh]">
                <p v-for="key in Object.keys(row.modelProcess)" :key="key">
                  {{ key }}:
                  <span class="text-danger">{{ row.modelProcess[key].failed }}</span>
                  <span class="separator">/</span>
                  <span class="text-success">{{ row.modelProcess[key].completed }}</span>
                  <span class="separator">/</span>
                  <span>{{ row.modelProcess[key].total }}</span>
                </p>
              </section>
            </template>
            <el-button text>
              <el-icon><QuestionFilled /></el-icon>
            </el-button>
          </el-tooltip>
        </section>
      </template>
    </el-table-column>
    <el-table-column label="Token 情况" min-width="180">
      <template #default="{ row }">
        <span class="text-primary">{{ row.promptTokens.toLocaleString() }}</span>
        <span>/</span>
        <span class="text-warning">{{ row.completionTokens.toLocaleString() }}</span>
        <span>/</span>
        <span>{{ row.totalTokens.toLocaleString() }}</span>
      </template>
    </el-table-column>
    <el-table-column label="创建时间" width="160">
      <template #default="{ row }">
        {{ formatTime(row.createAt) }}
      </template>
    </el-table-column>
    <el-table-column label="完成时间" width="160">
      <template #default="{ row }">
        {{ formatTime(row.finishAt) }}
      </template>
    </el-table-column>
    <el-table-column prop="execTime" label="执行时间" width="100" />
    <el-table-column label="是否过期" width="100">
      <template #default="{ row }">
        <el-tag v-if="row.expire" type="danger">已过期</el-tag>
        <span v-else>-</span>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="190">
      <template #default="{ row }">
        <div class="flex flex-wrap gap-1">
          <el-button
            v-if="row.inputUrl"
            type="primary"
            size="small"
            link
            @click="handleDownload(row.inputUrl)">
            下载输入
          </el-button>
          <el-button
            v-if="row.outputUrl"
            type="success"
            size="small"
            link
            @click="handleDownload(row.outputUrl)">
            下载输出
          </el-button>
          <el-button
            v-if="row.execStatus === 1"
            size="small"
            type="warning"
            link
            @click="handleUpdateStatus(row, OpEnum.stop)">
            暂停
          </el-button>
          <el-button
            v-else-if="row.execStatus === 2"
            size="small"
            type="primary"
            link
            @click="handleUpdateStatus(row, OpEnum.restart)">
            恢复
          </el-button>
          <el-button type="primary" size="small" link @click="handleShowPriorityDialog(row)">
            调整优先级
          </el-button>
        </div>
      </template>
    </el-table-column>
  </el-table>

  <!-- 分页 -->
  <div class="pagination">
    <el-pagination
      v-model:current-page="pagination.rn"
      v-model:page-size="pagination.pn"
      :page-sizes="[20, 50, 100]"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange" />
  </div>

  <!-- 优先级调整对话框 -->
  <el-dialog v-model="priorityDialogVisible" title="调整优先级" width="30%">
    <el-form :model="priorityForm" label-width="100px">
      <el-form-item label="优先级">
        <el-input-number
          v-model="priorityForm.priority"
          :min="1"
          :precision="0"
          :step="1"
          controls-position="right" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="priorityDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleUpdatePriority">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch, defineExpose } from 'vue'
// biome-ignore lint/style/useImportType: <explanation>
import {
  getGlobal,
  getBusiness,
  getUser,
  getUserTaskList,
  getModelList,
  updateTaskStatus,
  updatePriority,
  OpEnum,
  type TaskItem,
  type BusinessItem
} from '@/api/apiSubBatch'
import { ElMessage } from 'element-plus'
import { omit } from 'lodash-es'

const statusTab = ref(0)
const runningCount = ref(0)
const failedCount = ref(0)
const successCount = ref(0)

const searchForm = reactive({
  businessCode: '',
  uname: '',
  model: '',
  status: statusTab
})

const businessList = ref<{ value: string; label: string }[]>([])
const modelList = ref<{ value: string; key: string }[]>([])
const userList = ref<{ value: string; label: string }[]>([])
const tableData = ref<TaskItem[]>([])
const pagination = reactive({
  rn: 1,
  pn: 20,
  total: 0
})

// 优先级调整对话框
const priorityDialogVisible = ref(false)
const priorityForm = reactive({
  id: 0,
  priority: 1
})

// 获取全局配置
const fetchGlobalConfig = async () => {
  try {
    const { failedCount: failed, runningCount: running, successCount: success } = await getGlobal()
    failedCount.value = failed
    runningCount.value = running
    successCount.value = success
  } catch (error) {
    console.error('获取全局配置失败:', error)
  }
}
// 获取业务线数据
const fetchBusinessData = async (model?: string) => {
  try {
    const { list = [] } = await getBusiness({ model, aggregationStatus: searchForm.status })
    businessList.value = list.map((item: BusinessItem) => ({
      value: item.value,
      label: item.key
    }))
  } catch (error) {
    console.error('获取业务线数据失败:', error)
  }
}

// 获取模型列表
const fetchModelList = async () => {
  try {
    const { list = [] } = await getModelList({
      aggregationStatus: searchForm.status
    })
    modelList.value = list
  } catch (error) {
    console.error('获取模型列表失败:', error)
  }
}

// 获取用户数据
const fetchUserData = async (businessCode?: string, model?: string) => {
  try {
    const { list = [] } = await getUser({
      businessCode,
      model,
      aggregationStatus: searchForm.status
    })
    userList.value = list
  } catch (error) {
    console.error('获取用户数据失败:', error)
  }
}

// 获取任务列表
const fetchTaskList = async () => {
  try {
    const data = omit(searchForm, ['status'])
    const { list = [], total = 0 } = await getUserTaskList({
      ...data,
      aggregationStatus: searchForm.status,
      pageNum: pagination.rn,
      pageSize: pagination.pn
    })
    tableData.value = list
    pagination.total = total
  } catch (error) {
    console.error('获取任务列表失败:', error)
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.rn = 1
  fetchTaskList()
}

const handleReset = () => {
  searchForm.businessCode = ''
  searchForm.uname = ''
  searchForm.model = ''
  handleSearch()
}

const handleSizeChange = (val: number) => {
  pagination.pn = val
  fetchTaskList()
}

const handleCurrentChange = (val: number) => {
  pagination.rn = val
  fetchTaskList()
}

// 监听状态变化
watch(statusTab, (newVal) => {
  searchForm.status = newVal
  handleReset()
  fetchBusinessData()
  fetchModelList()
  fetchUserData()
})

// 监听模型变化
watch(
  () => searchForm.model,
  (newVal) => {
    // 清空业务线和用户选择
    searchForm.businessCode = ''
    searchForm.uname = ''
    // 重新获取业务线列表和用户列表
    fetchBusinessData(newVal)
    fetchUserData(undefined, newVal)
  }
)

// 监听业务线变化
watch(
  () => searchForm.businessCode,
  (newVal) => {
    // 清空用户选择
    searchForm.uname = ''
    // 重新获取用户列表，同时传入当前选中的模型
    fetchUserData(newVal, searchForm.model)
  }
)

// 初始化数据
const initData = async () => {
  await Promise.all([fetchGlobalConfig(), fetchModelList()])
  // 初始不传参数，获取所有业务线和用户
  await fetchBusinessData()
  await fetchUserData()
  fetchTaskList()
}

// 格式化时间戳
const formatTime = (timestamp: number) => {
  if (!timestamp) return '-'
  return new Date(timestamp * 1000).toLocaleString()
}

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '数据检查',
    2: '执行中',
    3: '数据输出中',
    4: '执行成功',
    5: '执行失败',
    6: '过期',
    7: '取消中',
    8: '取消完毕',
    9: '排队中'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态标签类型
const getStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    1: 'info',
    2: 'warning',
    3: 'info',
    4: 'success',
    5: 'danger'
  }
  return typeMap[status] || ''
}

// 处理下载
const handleDownload = (url: string) => {
  window.open(url, '_blank')
}

// 显示优先级调整对话框
const handleShowPriorityDialog = (row: TaskItem) => {
  priorityForm.id = row.id
  priorityForm.priority = row.priority ?? 1
  priorityDialogVisible.value = true
}

// 更新任务状态
const handleUpdateStatus = async (row: TaskItem, op: OpEnum) => {
  try {
    const { result } = await updateTaskStatus({
      subBatchId: row.id,
      op: op
    })
    if (result === 1) {
      ElMessage.success('操作成功')
      fetchTaskList()
    } else {
      ElMessage.error('操作失败')
    }
  } catch (error) {
    console.error('更新任务状态失败:', error)
    ElMessage.error('操作失败')
  }
}

// 更新优先级
const handleUpdatePriority = async () => {
  try {
    const { result } = await updatePriority({
      subBatchId: priorityForm.id,
      priority: priorityForm.priority
    })
    if (result === 1) {
      ElMessage.success('优先级调整成功')
      priorityDialogVisible.value = false
      fetchTaskList()
    } else {
      ElMessage.error('优先级调整失败')
    }
  } catch (error) {
    console.error('更新优先级失败:', error)
    ElMessage.error('优先级调整失败')
  }
}

onMounted(() => {
  initData()
})

defineExpose({
  fetchGlobalConfig
})
</script>

<style lang="less" scoped>
.status-tabs {
  margin-bottom: 20px;
}

.search-section {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.text-danger) {
  color: var(--el-color-danger);
}

:deep(.text-success) {
  color: var(--el-color-success);
}

:deep(.text-primary) {
  color: var(--el-color-primary);
}

:deep(.text-warning) {
  color: var(--el-color-warning);
}

:deep(.el-button) {
  margin: 0;
}
</style>
