<template>
  <!-- 状态标签 -->
  <div class="aggregationStatus-tabs">
    <el-radio-group v-model="statusTab">
      <el-radio-button :label="0">全部</el-radio-button>
      <el-radio-button :label="2">执行中 ({{ runningCount }})</el-radio-button>
      <el-radio-button :label="5">执行失败 ({{ failedCount }})</el-radio-button>
      <el-radio-button :label="4">执行成功 ({{ successCount }})</el-radio-button>
    </el-radio-group>
  </div>

  <!-- 搜索栏 -->
  <div class="search-section">
    <el-form :inline="true" :model="searchForm">
      <el-form-item label="业务线" class="min-w-[200px]">
        <el-select
          v-model="searchForm.businessCode"
          placeholder="请选择任务类别"
          clearable
          filterable>
          <el-option
            v-for="item in businessList"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="用户" class="min-w-[200px]">
        <el-select v-model="searchForm.uname" placeholder="请选择用户" clearable filterable>
          <el-option
            v-for="item in userList"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>

  <!-- 表格 -->
  <el-table :data="tableData" style="width: 100%">
    <el-table-column prop="id" label="ID" width="80" />
    <el-table-column prop="businessCode" label="业务线代码" min-width="120" />
    <el-table-column prop="businessName" label="业务线名称" min-width="120" />
    <el-table-column prop="uname" label="创建人" width="120" />
    <el-table-column prop="status" label="状态" min-width="100">
      <template #default="{ row }">
        <section class="flex justify-between items-center">
          <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
          <el-tooltip v-if="row.failReason" effect="dark" placement="top">
            <template #content>
              <span class="inline-block max-w-[70vw] overflow-auto max-h-[70vh]">
                {{ row.failReason }}
              </span>
            </template>
            <el-button text>
              <el-icon class="text-red-400"><QuestionFilled /></el-icon>
            </el-button>
          </el-tooltip>
        </section>
      </template>
    </el-table-column>
    <el-table-column label="请求情况" min-width="150">
      <template #default="{ row }">
        <section class="flex gap-2 items-center">
          <div class="request-status">
            <span class="text-danger">{{ row.failed }}</span>
            <span class="separator">/</span>
            <span class="text-success">{{ row.completed }}</span>
            <span class="separator">/</span>
            <span>{{ row.total }}</span>
          </div>
          <el-tooltip v-if="row.modelProcess" effect="dark" placement="top">
            <template #content>
              <section class="inline-block max-w-[70vw] overflow-auto max-h-[70vh]">
                <p v-for="key in Object.keys(row.modelProcess)" :key="key">
                  {{ key }}:
                  <span class="text-danger">{{ row.modelProcess[key].failed }}</span>
                  <span class="separator">/</span>
                  <span class="text-success">{{ row.modelProcess[key].completed }}</span>
                  <span class="separator">/</span>
                  <span>{{ row.modelProcess[key].total }}</span>
                </p>
              </section>
            </template>
            <el-button text>
              <el-icon><QuestionFilled /></el-icon>
            </el-button>
          </el-tooltip>
        </section>
      </template>
    </el-table-column>
    <el-table-column label="创建时间" width="180">
      <template #default="{ row }">
        {{ formatTime(row.createAt) }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="150" fixed="right">
      <template #default="{ row }">
        <div class="flex gap-2" v-if="row.status === 4 || row.status === 1">
          <el-button
            :type="row.execStatus === 1 ? 'warning' : 'success'"
            size="small"
            link
            @click="handleExecStatus(row)">
            {{ row.execStatus === 1 ? '暂停' : '恢复' }}
          </el-button>
        </div>
      </template>
    </el-table-column>
  </el-table>

  <!-- 分页 -->
  <div class="pagination">
    <el-pagination
      v-model:current-page="pagination.rn"
      v-model:page-size="pagination.pn"
      :page-sizes="[20, 50, 100]"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, defineExpose } from 'vue'
import { getGlobal, getBusiness, getUser, getTaskList, execStatus } from '@/api/apiBatch/mark'
import type { TaskItem, BusinessItem } from '@/api/apiBatch'
import { ElMessageBox, ElMessage } from 'element-plus'
const statusTab = ref(0)
const runningCount = ref(0)
const failedCount = ref(0)
const successCount = ref(0)

const searchForm = reactive({
  businessCode: '',
  uname: '',
  aggregationStatus: statusTab
})

const businessList = ref<{ value: string; label: string }[]>([])
const userList = ref<{ value: string; label: string }[]>([])
const tableData = ref<TaskItem[]>([])
const pagination = reactive({
  rn: 1,
  pn: 20,
  total: 0
})

// 获取全局配置
const fetchGlobalConfig = async () => {
  try {
    const { failedCount: failed, runningCount: running, successCount: success } = await getGlobal()
    failedCount.value = failed
    runningCount.value = running
    successCount.value = success
  } catch (error) {
    console.error('获取全局配置失败:', error)
  }
}

// 获取业务线数据
const fetchBusinessData = async () => {
  try {
    const { list = [] } = await getBusiness({
      aggregationStatus: searchForm.aggregationStatus
    })
    businessList.value = list.map((item: BusinessItem) => ({
      value: item.value,
      label: item.key
    }))
  } catch (error) {
    console.error('获取业务线数据失败:', error)
  }
}

// 获取用户数据
const fetchUserData = async (businessCode?: string) => {
  try {
    const { list = [] } = await getUser({
      businessCode,
      aggregationStatus: searchForm.aggregationStatus
    })
    userList.value = list
  } catch (error) {
    console.error('获取用户数据失败:', error)
  }
}

// 获取任务列表
const fetchTaskList = async () => {
  try {
    const { list = [], total = 0 } = await getTaskList({
      ...searchForm,
      pageNum: pagination.rn,
      pageSize: pagination.pn
    })
    tableData.value = list
    pagination.total = total
  } catch (error) {
    console.error('获取任务列表失败:', error)
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.rn = 1
  fetchTaskList()
}

const handleReset = () => {
  searchForm.businessCode = ''
  searchForm.uname = ''
  handleSearch()
}

const handleSizeChange = (val: number) => {
  pagination.pn = val
  fetchTaskList()
}

const handleCurrentChange = (val: number) => {
  pagination.rn = val
  fetchTaskList()
}

// 监听状态变化
watch(statusTab, (newVal) => {
  searchForm.aggregationStatus = newVal
  handleReset()
  fetchUserData()
  fetchBusinessData()
})

// 监听业务线变化
watch(
  () => searchForm.businessCode,
  (newVal) => {
    // 清空用户选择
    searchForm.uname = ''
    // 重新获取用户列表
    fetchUserData(newVal)
  }
)

// 初始化数据
const initData = async () => {
  await Promise.all([fetchGlobalConfig(), fetchBusinessData()])
  // 初始不传businessCode，获取所有用户
  await fetchUserData()
  fetchTaskList()
}

// 格式化时间戳
const formatTime = (timestamp: number) => {
  if (!timestamp) return '-'
  return new Date(timestamp * 1000).toLocaleString()
}

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '初始化',
    2: '执行失败',
    3: '执行成功',
    4: '执行中',
    5: '取消'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态标签类型
const getStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    1: 'info',
    2: 'danger',
    3: 'success',
    4: 'primary',
    5: 'info'
  }
  return typeMap[status] || ''
}

// 处理下载
// biome-ignore lint/suspicious/noExplicitAny: <explanation>
const handleExecStatus = async (item: any) => {
  // confirm对话框
  ElMessageBox.confirm(`确定要${item.execStatus === 1 ? '暂停' : '恢复'}吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    await execStatus({
      promptBatchId: item.id,
      op: item.execStatus
    })
    ElMessage.success('操作成功')
    fetchTaskList()
  })
}

initData()

defineExpose({
  fetchGlobalConfig
})
</script>

<style lang="less" scoped>
.aggregationStatus-tabs {
  margin-bottom: 20px;
}

.search-section {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.text-danger) {
  color: var(--el-color-danger);
}

:deep(.text-success) {
  color: var(--el-color-success);
}

:deep(.text-primary) {
  color: var(--el-color-primary);
}

:deep(.text-warning) {
  color: var(--el-color-warning);
}

.request-status {
  display: flex;
  align-items: center;
  gap: 4px;

  .separator {
    color: var(--el-text-color-secondary);
  }
}

.flex {
  display: flex;
  align-items: center;
}

.gap-2 {
  gap: 8px;
}
</style>
