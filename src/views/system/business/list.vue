<template>
  <SearchContainer>
    <el-form-item class="!w-[240px]">
      <el-input v-model.trim="state.query.name" placeholder="业务线名称（code）搜索" clearable>
        <template #append>
          <el-button type="primary" @click="queryList">搜索</el-button>
        </template>
      </el-input>
    </el-form-item>
    <section class="">
      <el-button link type="primary" @click="exportData">导出全部数据</el-button>
      <el-button type="primary" @click="refresh">刷新</el-button>
      <el-button type="primary" @click="addItem" v-if="isPassed">新建</el-button>
    </section>
  </SearchContainer>
  <section class="table-container">
    <el-table
      v-loading="isLoading"
      :data="listParams.list"
      style="width: 100%"
      :show-overflow-tooltip="false">
      <el-table-column :show-overflow-tooltip="false" prop="name" label="业务方" />
      <el-table-column prop="code" label="业务方标识" />
      <el-table-column prop="businessId" label="业务线ID" />
      <el-table-column prop="budgetName" label="预算单元" />
      <el-table-column prop="applyUname" label="业务线负责人" />
      <el-table-column prop="modelSchedPackageName" label="调度套餐" width="110px">
        <template #default="scope">
          <div>{{ scope.row.modelSchedPackageName }}</div>
          <div>{{ scope.row.exsitCustom ? '自定义套餐' : '非自定义套餐' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="useScenario" label="使用场景" :formatter="scenarioFormatter" />
      <el-table-column label="gpt-4" width="145px">
        <template #default="scope">
          <div v-if="getModelConfig(scope.row, 'gpt-4')">
            <p>TPM: {{ formatNumber(getModelConfig(scope.row, 'gpt-4').tpm) }}</p>
            <p>RPM: {{ formatNumber(getModelConfig(scope.row, 'gpt-4').rpm) }}</p>
            <p>用途: {{ getModelConfig(scope.row, 'gpt-4').typeString }}</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="dailyTokenLimit"
        label="每日最大使用token数量"
        :formatter="(row: any) => formatNumber(row.dailyTokenLimit)" />
      <el-table-column prop="memberLimit" label="组内最大成员数" />
      <el-table-column prop="nowMemberCnt" label="当前组内人数量" />
      <el-table-column
        width="160"
        prop="createTime"
        label="申请时间"
        :formatter="updateTimeFormatter" />
      <el-table-column
        width="160"
        prop="auditTime"
        label="修改时间"
        :formatter="updateTimeFormatter" />
      <el-table-column prop="status" label="状态" :formatter="statusFormatter" />
      <el-table-column fixed="right" label="操作" width="110" class-name="operation-column">
        <template #default="scope">
          <template v-if="scope.row.businessId !== 1">
            <el-button
              text
              type="primary"
              size="small"
              @click="deleteBatch(scope.row)"
              v-if="isPassed && scope.row.status === 2"
              >停用</el-button
            >
            <el-button
              text
              type="primary"
              size="small"
              @click="enableBatch(scope.row.businessId)"
              v-if="isPassed && scope.row.status === 4"
              >启用</el-button
            >
            <el-button
              text
              type="primary"
              size="small"
              @click="passBatch(scope.row.businessId)"
              v-if="!isPassed"
              >通过</el-button
            >
            <template v-if="!isPassed">
              <DelButton
                confirm-text="确认拒绝？"
                v-if="scope.row.status === 1"
                size="small"
                @click="dismissBatch(scope.row.businessId)"
                >拒绝</DelButton
              >
              <el-button
                v-else
                size="small"
                @click="rejectEditBatch(scope.row.businessId)"
                text
                type="danger"
                >拒绝</el-button
              >
            </template>
          </template>
          <el-button text type="primary" size="small" v-if="isPassed" @click="editBus(scope.row)"
            >修改</el-button
          >
          <el-button text type="primary" size="small" @click="viewBus(scope.row)">查看</el-button>
          <el-button
            text
            type="primary"
            size="small"
            @click="configSchedule(scope.row)"
            v-if="showConfigSchedule(scope.row)"
            >调度配置</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </section>
  <Add
    v-if="state.show"
    @close="closeDialog"
    :value="state.currentRow"
    :view="state.view"
    :showDiff="props.status === 1">
  </Add>
  <ConfigSchedule
    v-if="state.configScheduleShow"
    @close="closeConfigScheduleDialog"
    :condfigType="2"
    :businessId="state.currentRow.businessId"
    :businessName="state.currentRow.name"></ConfigSchedule>
</template>

<script lang="tsx" setup>
import $http from '@/api'
import { ControlAction, GetBusListRetListItem, Scenario } from '@/api/business/type'
import Add from '@/components/create-business/index.vue'
import SearchContainer from '@/components/search-container'
import { formatNumber } from '@/utils/format'
import { useList } from '@znzt-fe/hooks'
import { ElMessage, ElMessageBox } from 'element-plus'
import ConfigSchedule from '@/components/config-sched/index.vue'
import DelButton from '@/components/del-button'
const props = defineProps({
  status: {
    type: Number,
    default: 0
  }
})
const { mutateAsync: getBusListAsync } = $http.useGetBusList()
const exportData = () => window.open('/openmis/business/listexport')
const getList = async () => {
  const params = {
    ...state.query,
    status: props.status,
    withSk: true,
    withMember: true
  }
  const data = await getBusListAsync(params)
  return data
}

const { mutate, isLoading, listParams } = useList<GetBusListRetListItem>({
  getList
})

const state: any = reactive({
  type: 1,
  query: {
    name: ''
  },
  dialog: false,
  detail: {},
  show: false,
  view: false,
  currentRow: {},
  configScheduleShow: false
})
const isPassed = props.status === 2

const queryList = () => {
  listParams.pageInfo.pageNum = 1
  mutate()
}

const addItem = async () => {
  state.show = true
  state.currentRow = {}
  state.view = false
}
// 停用
const deleteBatch = (row: any) => {
  const { businessId, name } = row
  ElMessageBox({
    title: '确认是否停用',
    message: `您即将停用<strong style='margin: 0 3px;'><i>${name}</i></strong></span>业务线，此操作将导致所有相关服务立即中断。如果您确定要进行此操作，请点击确认按钮。`,
    dangerouslyUseHTMLString: true,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(() => {
    busControl(businessId, 3)
  })
}
// 启用
const enableBatch = (id = '') => {
  busControl(id, ControlAction.Enable)
}
// 通过
const passBatch = (id = '') => {
  busControl(id, ControlAction.Pass)
}
// 拒绝业务线申请
const dismissBatch = (id = '') => {
  busControl(id, ControlAction.Dismiss)
}
// 拒绝业务线编辑
const rejectEditBatch = (id = '') => {
  ElMessageBox.prompt('拒绝原因', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^.+$/,
    inputErrorMessage: '请输入拒绝理由'
  })
    .then(({ value }) => {
      busControl(id, ControlAction.RejectEdit, value)
    })
    .catch(() => {})
}
const busControl = async (
  businessId = '',
  controlAction: ControlAction = ControlAction.Pass,
  reason = ''
) => {
  await $http.busControl({
    controlAction,
    businessId,
    reason: reason || undefined
  })
  const messageMap = {
    [ControlAction.Pass]: '审批通过！5分钟后生效',
    [ControlAction.Deactivate]: '停用成功！5分钟后生效',
    [ControlAction.Enable]: '启用成功！5分钟后生效',
    [ControlAction.Dismiss]: '审批已拒绝！5分钟后生效',
    [ControlAction.RejectEdit]: '审批已拒绝！'
  }
  ElMessage.success(messageMap[controlAction])
  queryList()
}

const scenarioFormatter = (row: any) => {
  const scenarioMap: any = {
    [Scenario.Unset]: '未设置',
    [Scenario.Online]: '在线',
    [Scenario.Offline]: '离线',
    [Scenario.Inside]: '内部'
  }
  return scenarioMap[row.useScenario]
}
const statusFormatter = (row: any) => {
  if (isPassed && row.status === 2) {
    return '正常'
  }
  const statusMap: any = {
    1: '新建',
    2: '修改',
    3: '驳回',
    4: '停用'
  }
  return statusMap[row.status]
}
const updateTimeFormatter = (row: any, t: any, value: number) => {
  if (!value) {
    return '-'
  }
  return new Date(value * 1000).toLocaleString()
}
const viewBus = (data: any) => {
  state.show = true
  state.currentRow = data
  state.view = true
}
const editBus = (data: any) => {
  state.show = true
  state.currentRow = data
  state.view = false
}
const closeDialog = (refresh = false) => {
  state.show = false
  if (refresh) {
    mutate()
  }
}
const refresh = () => {
  state.query.name = ''
  listParams.pageInfo.pageNum = 1
  mutate()
}
const getModelConfig = (row: any = {}, model: string) => {
  const { config = [] } = row
  const data: any = config.find((item: any) => item.modelName === model)
  return data
}
const showConfigSchedule = (row: any) => {
  const { config = [] } = row
  return config.some((item: any) => item.customHandSrv)
}
const configSchedule = (row: any) => {
  state.configScheduleShow = true
  state.currentRow = row
}
const closeConfigScheduleDialog = (refresh = false) => {
  state.configScheduleShow = false
  if (refresh) {
    mutate()
  }
}
</script>

<style scoped lang="less">
.table-container {
  border-radius: 4px;
  background: white;
  padding: 12px;
  border-radius: 4px;
  background: white;
  padding: 12px;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.el-table {
  :deep(.operation-column) {
    .cell {
      display: flex;
      flex-wrap: wrap;

      .el-button {
        padding: 0;
        margin: 0;
        margin-right: 6px;
      }
    }
  }
}
</style>
