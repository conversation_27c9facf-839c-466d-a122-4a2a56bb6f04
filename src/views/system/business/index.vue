<template>
  <el-tabs v-model="state.type" class="demo-tabs">
    <el-tab-pane label="已审核" :name="2">
      <List :status="2"></List>
    </el-tab-pane>
    <el-tab-pane label="待审核" :name="1">
      <List :status="1"></List>
    </el-tab-pane>
  </el-tabs>
</template>

<script lang="ts" setup>
import List from './list.vue'

const state: any = reactive({
  type: 2
})
</script>
<style scoped lang="less"></style>
