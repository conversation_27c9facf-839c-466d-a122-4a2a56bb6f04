<template>
  <section class="add" :inline="true" :model="state.query">
    <el-form-item class="!w-[240px]">
      <el-input v-model.trim="state.name" placeholder="用户邮箱前缀" clearable>
        <template #append>
          <el-button type="primary" @click="search">搜索</el-button>
        </template>
      </el-input>
    </el-form-item>
  </section>
  <section class="table-container">
    <el-table :data="state.list" style="width: 100%" :show-overflow-tooltip="true">
      <el-table-column prop="businessName" label="业务线名称" />
      <el-table-column prop="businessCode" label="业务线标识" />
      <el-table-column prop="status" label="用户状态" :formatter="statusFormatter" />
      <el-table-column prop="roleName" label="用户角色" />
      <el-table-column prop="uname" label="用户名" />
      <el-table-column prop="zhName" label="用户中文名" />
    </el-table>
    <Pagination
      v-model:pageNum="state.pageInfo.pageNum"
      v-model:page-size="state.pageInfo.pageSize"
      :total="state.pageInfo.total"
      @refresh="search">
    </Pagination>
  </section>
</template>

<script lang="ts" setup>
import $http from '@/api'
import { Pagination } from '@znzt-fe/components'
import { ElMessage } from 'element-plus'

const state: any = reactive({
  name: '',
  list: [],
  pageInfo: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  }
})
const statusFormatter = (row: any) => {
  const statusMap: any = {
    0: 'guest',
    1: '审核中',
    2: '审核通过',
    3: '审核拒绝',
    4: '已删除'
  }
  return statusMap[row.status]
}
const search = async () => {
  if (!state.name) {
    ElMessage.warning('请输入用户邮箱前缀!')
    return
  }
  const params = {
    ...state.pageInfo,
    uname: state.name,
  }
  const { total, list = [] } = await $http.getAdminUserList(params)
  state.list = list
  state.pageInfo.total = total
}
</script>
<style scoped lang="less">
.add {
  background: white;
  padding: 16px;
  padding-bottom: 0;
  border-radius: 4px;
  min-height: 60px;
  box-sizing: border-box;
  margin-bottom: 12px;
  display: flex;
}

.table-container {
  border-radius: 4px;
  background: white;
  padding: 12px;
}
</style>
