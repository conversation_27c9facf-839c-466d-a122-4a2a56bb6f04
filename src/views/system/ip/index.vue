<template>
  <el-tabs v-model="state.type" class="demo-tabs">
    <List :status="2"></List>
  </el-tabs>
</template>

<script lang="ts" setup>
import List from './list.vue'
import useUserStore from '@/store/user'

const userStore = useUserStore()
const isGuestBus = computed(() => {
  const { current } = userStore
  return current.businessId === 1
})
const state: any = reactive({
  type: 2
})
watch(
  () => isGuestBus.value,
  (val) => {
    if (!val) {
      state.type = 2
    }
  }
)
</script>
<style scoped lang="less"></style>
