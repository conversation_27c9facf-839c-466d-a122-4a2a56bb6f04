<template>
  <SearchContainer>
    <el-form inline>
      <el-form-item class="!w-[240px]" label="厂商">
        <el-select v-model="state.query.ipFactoryId" @change="queryList">
          <el-option
            v-for="item in state.factoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item class="!w-[240px]">
        <el-input v-model.trim="state.query.ip" placeholder="ip" clearable>
          <template #append>
            <el-button type="primary" @click="queryList">搜索</el-button>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <section class="">
      <el-button type="danger" @click="deleteBatch()" :disabled="!state.selection.length"
        >批量删除</el-button
      >
      <el-button type="primary" @click="addItem" v-if="isPassed">新建</el-button>
    </section>
  </SearchContainer>
  <section class="table-container">
    <el-table
      :data="state.list"
      :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="ip" label="IP" />
      <el-table-column prop="ipFactoryName" label="厂商" />
      <el-table-column fixed="right" label="操作" width="150">
        <template #default="scope">
          <DelButton size="small" @click="deleteBatch([scope.row.id])" v-if="isPassed"></DelButton>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-model:pageNum="state.pageInfo.pageNum"
      v-model:page-size="state.pageInfo.pageSize"
      :total="state.pageInfo.total"
      @refresh="getList">
    </Pagination>
  </section>
  <Add v-if="state.show" @close="closeDialog"></Add>
</template>

<script lang="ts" setup>
import { Pagination } from '@znzt-fe/components'
import DelButton from '@/components/del-button'
import $http from '@/api'
import { ElMessage } from 'element-plus'
import Add from './add.vue'
import SearchContainer from '@/components/search-container'
const props = defineProps({
  status: {
    type: Number,
    default: 0
  }
})
const isPassed = props.status === 2

const state: any = reactive({
  type: 1,
  query: {
    ip: '',
    ipFactoryId: ''
  },
  list: [],
  pageInfo: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  dialog: false,
  detail: {},
  selection: [],
  show: false,
  factoryOptions: [{ label: '全部', value: 0 }]
})
const queryList = () => {
  state.pageInfo.pageNum = 1
  getList()
}
const getList = async () => {
  const params = {
    ...state.query,
    ...state.pageInfo
  }
  const { list = [], total } = await $http.getIpList(params)
  state.list = list
  state.pageInfo.total = +total
}
queryList()
const addItem = async () => {
  state.show = true
}
const handleSelectionChange = (val: any[]) => {
  state.selection = val.map((item: any) => item.id)
}
const deleteBatch = (ids = state.selection) => {
  ipControl(ids, 1)
}
const ipControl = async (ipIds = [], controlAction = 1) => {
  await $http.ipControl({
    controlAction,
    ipIds
  })
  const message = '删除成功！5分钟后生效'
  ElMessage.success(message)
  queryList()
}
const closeDialog = (refresh = false) => {
  state.show = false
  if (refresh) {
    getList()
  }
}
const getOptions = async () => {
  const data = await $http.getIpFactoryList({ pageSize: 100, pageNum: 1 })
  state.factoryOptions = state.factoryOptions.concat(
    data.list.map((item: any) => {
      return {
        label: `${item.name}`,
        value: item.id
      }
    })
  )
}
getOptions()
</script>
<style scoped lang="less">
.table-container {
  border-radius: 4px;
  background: white;
  padding: 12px;
}
</style>
