<template>
  <section class="node-dialog">
    <el-dialog
      v-model="state.show"
      title="新增IP"
      width="450px"
      @close="close()"
      :close-on-click-modal="false">
      <el-form :model="state.detail" label-width="64px" ref="formRef">
        <el-form-item label="厂商" prop="ipFactoryId" label-width="80px">
          <el-select v-model="state.detail.ipFactoryId">
            <el-option
              v-for="item in state.options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="ip" prop="ipListStr" label-width="80px">
          <el-input type="textarea" :rows="5" v-model="state.detail.ipListStr"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="confirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </section>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import $http from '@/api'
const state: any = reactive({
  show: true,
  detail: {}
})
const getOptions = async () => {
  const data = await $http.getIpFactoryList({ pageSize: 100, pageNum: 1 })
  state.options = data.list.map((item: any) => {
    return {
      label: `${item.name}`,
      value: item.id
    }
  })
}
getOptions()
const emits = defineEmits(['close'])
const confirm = async () => {
  const { detail } = state
  if (!detail.ipFactoryId) {
    ElMessage.error('请选择厂商！')
    return
  }
  if (!detail.ipListStr) {
    ElMessage.error('请输入IP地址')
    return
  }
  const params = {
    ...detail
  }
  await $http.addIp(params).finally(() => {
    state.disabled = false
  })
  ElMessage.success({
    message: '添加IP成功！5分钟后生效',
    duration: 3000
  })
  close(true)
}
const close = (refresh = false) => {
  state.show = false
  emits('close', refresh)
}
</script>
<style scoped lang="less">
.node-dialog {
  :deep(.el-dialog) {
    .el-dialog__body {
      padding-top: 8px;

      .el-table {
        .el-input,
        .el-select {
          width: 100%;
        }
      }
    }
  }
}
</style>
