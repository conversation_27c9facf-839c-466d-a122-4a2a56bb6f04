<template>
  <section class="add" :inline="true" :model="state.query">
    <el-button type="primary" @click="addItem">新建</el-button>
  </section>
  <section class="table-container">
    <el-table :data="state.list" style="width: 100%" :show-overflow-tooltip="true">
      <el-table-column prop="id" label="id" />
      <el-table-column prop="name" label="名称" />
      <el-table-column prop="desc" label="IP厂商描述" />
      <el-table-column prop="status" label="状态" :formatter="statusFormatter" />
      <el-table-column :show-overflow-tooltip="false" fixed="right" align="center" label="操作">
        <template #default="scope">
          <el-button
            text
            type="primary"
            size="small"
            @click="factoryControl(scope.row.id, 1)"
            v-if="scope.row.status === 1"
            >停用</el-button
          >
          <el-button
            text
            type="primary"
            size="small"
            @click="factoryControl(scope.row.id, 2)"
            v-if="scope.row.status === 2"
            >启用</el-button
          >
          <el-button text type="primary" size="small" @click="editFactory(scope.row)"
            >修改</el-button
          >
          <DelButton size="small" @click="factoryControl(scope.row.id, 3)"></DelButton>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-model:pageNum="state.pageInfo.pageNum"
      v-model:page-size="state.pageInfo.pageSize"
      :total="state.pageInfo.total"
      @refresh="getList">
    </Pagination>
  </section>
  <Add v-if="state.show" @close="closeDialog" :detail="state.detail"></Add>
</template>

<script lang="ts" setup>
import DelButton from '@/components/del-button'
import { Pagination } from '@znzt-fe/components'
import $http from '@/api'
import { ElMessage } from 'element-plus'
import Add from './add.vue'

const state: any = reactive({
  list: [],
  pageInfo: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  dialog: false,
  detail: {},
  selection: [],
  show: false
})
const statusFormatter = (row: any) => {
  const statusMap: any = {
    1: '正常',
    2: '已停用',
    3: '已删除'
  }
  return statusMap[row.status]
}
const queryList = () => {
  state.pageInfo.pageNum = 1
  getList()
}
const getList = async () => {
  const params = {
    status: 0,
    ...state.pageInfo
  }
  const { list = [], total } = await $http.getIpFactoryList(params)
  state.list = list
  state.pageInfo.total = +total
}
queryList()
const addItem = async () => {
  state.detail = {}
  state.show = true
}
const factoryControl = async (id: number, actionControl: number) => {
  await $http.factoryControl({
    id,
    actionControl
  })
  const messageMap: any = {
    1: '停用成功！',
    2: '启用成功！',
    3: '删除成功！'
  }
  ElMessage.success(messageMap[actionControl])
  queryList()
}
const editFactory = (data: any) => {
  state.show = true
  state.detail = data
}
const closeDialog = (refresh = false) => {
  state.show = false
  if (refresh) {
    getList()
  }
}
</script>
<style scoped lang="less">
.add {
  background: white;
  padding: 16px;
  border-radius: 4px;
  min-height: 60px;
  box-sizing: border-box;
  margin-bottom: 12px;
  display: flex;
  justify-content: end;
}

.table-container {
  border-radius: 4px;
  background: white;
  padding: 12px;
}
</style>
