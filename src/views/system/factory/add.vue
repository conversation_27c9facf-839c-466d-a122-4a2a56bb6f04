<template>
  <section class="node-dialog">
    <el-dialog
      v-model="state.show"
      :title="state.detail.id ? '编辑IP厂商信息' : '新增IP厂商'"
      width="450px"
      @close="close()"
      :close-on-click-modal="false">
      <el-form :model="state.detail" :rules="state.rules" label-width="80px" ref="formRef">
        <el-form-item label="厂商名称" prop="name">
          <el-input v-model="state.detail.name"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="desc">
          <el-input type="textarea" :rows="5" v-model="state.detail.desc"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="confirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </section>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { isEmpty } from 'lodash-es'
import $http from '@/api'
const props = defineProps({
  detail: {
    type: Object,
    default: () => ({})
  }
})
const state: any = reactive({
  show: true,
  detail: {},
  rules: {
    name: {
      required: true,
      message: '请输入厂商名称',
      trigger: 'blur'
    }
  }
})
if (!isEmpty(props.detail)) {
  Object.assign(state.detail, {
    ...props.detail
  })
}

const formRef = ref()
const emits = defineEmits(['close'])
const confirm = async () => {
  const form = formRef.value
  const valid = await form.validate().catch(() => false)
  if (valid) {
    const params = {
      ...state.detail
    }
    if (props.detail.id) {
      await $http.updateFactory(params).finally(() => {
        state.disabled = false
      })
      ElMessage.success('修改成功!')
    } else {
      await $http.addFactory(params).finally(() => {
        state.disabled = false
      })
      ElMessage.success('添加成功!')
    }
    close(true)
  }
}
const close = (refresh = false) => {
  state.show = false
  emits('close', refresh)
}
</script>
<style scoped lang="less">
.node-dialog {
  :deep(.el-dialog) {
    .el-dialog__body {
      padding-top: 8px;

      .el-table {
        .el-input,
        .el-select {
          width: 100%;
        }
      }
    }
  }
}
</style>
