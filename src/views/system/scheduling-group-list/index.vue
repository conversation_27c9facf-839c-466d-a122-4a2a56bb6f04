<template>
  <SearchContainer>
    <section></section>
    <section class="">
      <el-button type="primary" @click="addConfig">新增</el-button>
    </section>
  </SearchContainer>
  <section class="table-container">
    <el-table :data="list" style="width: 100%" :show-overflow-tooltip="false">
      <el-table-column prop="id" label="ID" />
      <el-table-column prop="name" label="名称" />
      <el-table-column
        width="160"
        prop="updateTime"
        label="创建时间"
        :formatter="updateTimeFormatter" />
      <el-table-column label="默认" width="110">
        <template #default="scope">
          <section v-if="scope.row.type === 1">--</section>
          <template v-else>
            <el-tag v-if="scope.row.isDefault" type="success">默认套餐</el-tag>
            <el-button v-else type="primary" size="small" @click="setDefault(scope.row)"
              >设置为默认</el-button
            >
          </template>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="110" class-name="operation-column">
        <template #default="scope">
          <el-button text type="primary" size="small" @click="editConfig(scope.row)"
            >编辑</el-button
          >
          <el-button
            v-if="scope.row.type !== 1"
            text
            type="danger"
            size="small"
            @click="deleteConfig(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </section>
  <ConfigSchedule
    v-if="state.configScheduleShow"
    @close="closeConfigScheduleDialog"
    :id="state.currentRow.id">
  </ConfigSchedule>
</template>

<script lang="tsx" setup>
import SearchContainer from '@/components/search-container'
import ConfigSchedule from '@/components/config-sched/index.vue'
import { ElMessageBox } from 'element-plus'
import { useGetModelSchedList, useDelModelSched, useSetDefaultModelSched } from '@/api/modelSched'
import { GetModelSchedListParams } from '@/api/modelSched/type'
const list = ref([])
const { mutate: getListMuate } = useGetModelSchedList({
  onSuccess: (data: any) => {
    list.value = data.list || []
  }
})
const getList = async () => {
  const params: GetModelSchedListParams = {
    type: 0
  }
  await getListMuate(params)
}
const { mutate: delMutate } = useDelModelSched({
  onSuccess: () => {
    getList()
  }
})
const { mutate: setDefaultMutate } = useSetDefaultModelSched({
  onSuccess: () => {
    getList()
  }
})

getList()

const state: any = reactive({
  currentRow: {},
  configScheduleShow: false
})

const updateTimeFormatter = (row: any) => {
  if (!row.createTime) {
    return '-'
  }
  return new Date(row.createTime * 1000).toLocaleString()
}
// 删除
const deleteConfig = (data: any = {}) => {
  ElMessageBox({
    title: '删除提示',
    message: `是否确定删除？`,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(() => {
    delMutate({
      id: data.id
    })
  })
}
const setDefault = (data: any = {}) => {
  ElMessageBox({
    title: '提示',
    message: `是否设置为默认？`,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(() => {
    setDefaultMutate({
      id: data.id
    })
  })
}
const configSchedule = (row: any = {}) => {
  state.configScheduleShow = true
  state.currentRow = row
}
const editConfig = (data: any) => {
  configSchedule(data)
}
const addConfig = () => {
  configSchedule()
}
const closeConfigScheduleDialog = (refresh = false) => {
  state.configScheduleShow = false
  if (refresh) {
    getList()
  }
}
</script>

<style scoped lang="less">
.table-container {
  border-radius: 4px;
  background: white;
  padding: 12px;
  border-radius: 4px;
  background: white;
  padding: 12px;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.el-table {
  :deep(.operation-column) {
    .cell {
      display: flex;
      flex-wrap: wrap;

      .el-button {
        padding: 0;
        margin: 0;
        margin-right: 6px;
      }
    }
  }
}
</style>
