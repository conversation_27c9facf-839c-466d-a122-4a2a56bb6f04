import {
  ElButton,
  ElFormItem,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElSelect,
  ElSpace,
  ElTable,
  ElUpload,
  UploadInstance
} from 'element-plus'
import { useForm, useTable } from './hooks'
import Modal from './modal'
import { useModal } from './hooks'
import SearchContainer from '@/components/search-container'
import { Pagination } from '@znzt-fe/components'
import { ResponseData } from '@/plugin/axios/interceptors'
import { useBatchAdd } from '@/api/cloudresource'
export default defineComponent({
  setup() {
    const { visible, modalId, openModal } = useModal()
    const { form, adminModelOptions, modelList, refetchGetAdminModelOptions } = useForm()
    const { listParams, isLoading, tableColumn, refetchData, getAdminModelListMutate } = useTable(
      openModal,
      form,
      adminModelOptions
    )
    const { mutate: batchAddMutate, isLoading: batchAddLoading } = useBatchAdd({
      onSuccess: () => ElMessage.success('上传成功'),
      onSettled: () => uploadRef.value?.clearFiles()
    })
    const uploadSuccess = (val: ResponseData<{ fileName: string }>) =>
      batchAddMutate({ fileName: val.data.fileName })
    const uploadRef = ref<UploadInstance | null>(null)
    const uploadError = () => {
      uploadRef.value?.clearFiles()
      ElMessageBox.alert('文件上传失败，请重新选择文件上传', '上传失败', {
        confirmButtonText: '关闭'
      })
    }
    return () => (
      <div>
        <SearchContainer>
          <div class="flex-just-space w-full">
            <ElSpace>
              <ElFormItem class="!w-[240px]" label="厂商名称">
                <ElSelect
                  clearable
                  filterable
                  onChange={(val) => {
                    if (
                      !adminModelOptions.value?.companyList
                        .find((item) => item.id === val)
                        ?.modelCategoryList.find((item) => item.id === form.parentId)
                    )
                      form.parentId = undefined
                  }}
                  v-model={form.companyId}>
                  {adminModelOptions.value?.companyList.map((item) => (
                    <ElOption key={item.id} label={item.name} value={item.id} />
                  ))}
                </ElSelect>
              </ElFormItem>
              <ElFormItem class="!w-[240px]" label="模型组">
                <ElSelect clearable filterable v-model={form.parentId}>
                  {modelList.value?.map((item) => (
                    <ElOption key={item.id} label={item.name} value={item.id} />
                  ))}
                </ElSelect>
              </ElFormItem>
              <ElButton onClick={refetchData}>查询</ElButton>
            </ElSpace>
            <ElSpace>
              <ElUpload
                accept=".xlsx,.json"
                action="/openmis/file/upload"
                data={{ oriName: 1, filePath: 'cloudResource' }}
                limit={1}
                onError={uploadError}
                onSuccess={uploadSuccess}
                ref={uploadRef}
                showFileList={false}
                v-loading={batchAddLoading.value}>
                <ElButton>选择文件</ElButton>
              </ElUpload>

              <ElButton onClick={() => window.open('/openmis/cloudresource/export')}>导出</ElButton>
              <ElButton onClick={() => openModal()} type="primary">
                新增
              </ElButton>
            </ElSpace>
          </div>
        </SearchContainer>
        <ElTable data={listParams.list} v-loading={isLoading.value}>
          {tableColumn}
        </ElTable>
        <Pagination
          onRefresh={getAdminModelListMutate}
          style={{ marginRight: '6px' }}
          total={listParams.pageInfo.total}
          v-model:pageNum={listParams.pageInfo.pageNum}
          v-model:pageSize={listParams.pageInfo.pageSize}
        />
        <Modal
          adminModelListItem={listParams.list.find((item) => item.id + '' === modalId.value)}
          onRefetch={() => {
            refetchGetAdminModelOptions()
            refetchData()
          }}
          v-model={visible.value}
        />
      </div>
    )
  }
})
