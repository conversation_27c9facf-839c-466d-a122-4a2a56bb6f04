<template>
  <el-collapse v-model="activeNames" class="w-full">
    <el-collapse-item :title="props.title" name="1">
      <el-form :model="form" :labelWidth="120" inline>
        <section class="flex">
          <section class="input-panel w-[50%]">
            <el-form-item label="prompt" prop="prompt">
              <el-input-number :controls="false" :min="0" v-model="form.prompt" />
            </el-form-item>
            <template v-if="!props.batch">
              <el-form-item label="textCachedInput" prop="textCachedInput">
                <el-input-number :controls="false" :min="0" v-model="form.textCachedInput" />
              </el-form-item>
              <el-form-item label="cacheCreate" prop="cacheCreate">
                <el-input-number :controls="false" :min="0" v-model="form.cacheCreate" />
              </el-form-item>
              <template v-if="props.isAudio">
                <el-form-item label="textInput" prop="textInput">
                  <el-input-number :controls="false" :min="0" v-model="form.textInput" />
                </el-form-item>
                <el-form-item label="audioInput" prop="audioInput">
                  <el-input-number :controls="false" :min="0" v-model="form.audioInput" />
                </el-form-item>
                <el-form-item label="audioCachedInput" prop="audioCachedInput">
                  <el-input-number :controls="false" :min="0" v-model="form.audioCachedInput" />
                </el-form-item>
              </template>
            </template>
          </section>
          <section class="output-panel w-[50%]">
            <el-form-item label="completion" prop="completion">
              <el-input-number :controls="false" :min="0" v-model="form.completion" />
            </el-form-item>
            <template v-if="!props.batch && props.isAudio">
              <el-form-item label="textOutput" prop="textOutput">
                <el-input-number :controls="false" :min="0" v-model="form.textOutput" />
              </el-form-item>
              <el-form-item label="audioOutput" prop="audioOutput">
                <el-input-number :controls="false" :min="0" v-model="form.audioOutput" />
              </el-form-item>
            </template>
          </section>
        </section>
      </el-form>
    </el-collapse-item>
  </el-collapse>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  batch: {
    type: Boolean,
    default: false
  },
  isAudio: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: Object,
    default: () => ({})
  }
})
const activeNames = ref([])
const emits: any = defineEmits(['update:modelValue'])
const form: any = useModel(props, 'modelValue', emits)
</script>
