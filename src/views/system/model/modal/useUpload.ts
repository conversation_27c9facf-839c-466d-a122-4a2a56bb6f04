import { ElMessage } from 'element-plus'

export function useUpload() {
  const fileList: Ref<any> = ref([])
  watch(
    () => fileList.value,
    async (val = []) => {
      const file = val[0]
      if (file && file.raw) {
        const { fileName, url } = await uploadAction({
          file: file.raw,
          filePath: 'model'
        })
        fileList.value = [
          {
            logoPath: fileName,
            url
          }
        ]
      }
    }
  )
  const removeChange = () => {
    fileList.value = []
  }
  const uploadAction = async (data: any) => {
    const formData = new FormData()
    const keys = Object.keys(data)
    keys.forEach((key) => formData.append(key, data[key]))
    const res = await fetch('/openmis/file/upload', {
      method: 'POST',
      body: formData
    })
    if (res.ok) {
      const { errNo = 0, data = {}, errMsg } = await res.json()
      if (errNo !== 0) {
        ElMessage.warning(errMsg)
        throw new Error(errMsg)
      }
      return data
    }
  }

  return {
    fileList,
    removeChange,
    uploadAction
  }
}
