import { useElForm, useRule, useSelectList } from '@znzt-fe/hooks'
import { ElMessage } from 'element-plus'
import { afterDecorator, beforeNextTick, objectToArray, resetObj } from '@znzt-fe/utils'
import { useAddAdminModel, useGetAdminModelOptions, useUpdateAdminModel } from '@/api/adminModel'
import {
  AddAdminModelParams,
  AdminModelListItem,
  AdminModelSource,
  AdminModelType
} from '@/api/adminModel/type'
import { Options } from '@znzt-fe/declare'

export const useForm = (
  refetch: () => void,
  visible: Ref<boolean>,
  adminModelListItem: Ref<AdminModelListItem>,
  fileList: Ref<any>
) => {
  const initData: AddAdminModelParams = {
    name: '', // 模型名称
    type: AdminModelType.External, // 模型类型 1 正式 2  内测
    source: AdminModelSource.Preset, // 模型来源 1 预置 2 自建
    parentId: undefined, // 父模型id
    modelCompanyId: undefined, // 厂商Id
    supportThinking: false, // 是否支持思考
    desc: '', // 模型描述
    scene: '', // 使用场景
    url: '', // 模型访问url
    maxTokens: undefined, // 最大token数量
    maxInTokens: undefined, // 最大输入token数量
    contextWindow: undefined, // 最大窗口token数量
    useCn: false, // 国内模型
    forbidRequest: false, // 是否不支持调用
    supportMsgType: [], // 支持消息格式
    withBatch: false, // 是否支持跑批
    cost: {},
    realCost: {},
    shareCost: {},
    batchCost: {},
    batchRealCost: {},
    batchShareCost: {},
    logoPath: '', // 模型logo地址
    ipm: undefined,
    rpm: undefined,
    tpm: undefined,
    qps: undefined,
    isFree: 0
  }
  const formId = ref<number>(0)
  const { formRef, validate, clearValidate, validateField, resetForm, form } = useElForm(initData)
  const selectForm = toRefs(useSelectList(form, ['parentId']))
  const title = computed(() => (!formId.value ? '新增模型' : '编辑模型'))
  const rules = useRule({
    name: '模型名称不能为空',
    type: '模型类型不能为空',
    source: '模型来源不能为空',
    modelCompanyId: '模型厂商不能为空',
    scene: '使用场景不能为空'
  })
  const { adminModelOptions, refetchGetAdminModelOptions, ...rest } = useOptions()
  const options = {
    onSuccess() {
      ElMessage.success('操作成功')
      refetchGetAdminModelOptions()
      refetch()
      visible.value = false
    }
  }
  const resetFormData = async () => {
    formId.value = 0
    resetForm()
    fileList.value = []
    clearValidate()
  }

  const getDetail = () => {
    if (!adminModelListItem.value.id) return
    const { id, logoUrl, ...rest } = adminModelListItem.value
    formId.value = id
    resetObj(form, { ...rest })
    const { logoPath } = rest
    if (logoUrl && logoPath) {
      fileList.value = [
        {
          logoPath: logoPath,
          url: logoUrl
        }
      ]
    }
    form.parentId = rest.parentId || undefined
  }
  whenever(visible, beforeNextTick(afterDecorator(resetFormData, getDetail)))
  whenever(fileList, () => {
    if (fileList.value?.length) {
      const file = fileList.value[0]
      form.logoPath = file.logoPath
    }
  })
  const { mutate: addAdminModel } = useAddAdminModel(options)
  const { mutate: updateAdminModel } = useUpdateAdminModel(options)
  const submit = async () => {
    const result = await validate()
    if (!result) return
    formId.value ? updateAdminModel({ ...form, id: formId.value }) : addAdminModel(form)
  }
  const modelList = computed(() => {
    return adminModelOptions?.value?.companyList.reduce((pre, now) => {
      if (form.modelCompanyId) {
        now.id === form.modelCompanyId && pre.push(...now.modelCategoryList)
      } else {
        pre.push(...now.modelCategoryList)
      }
      return pre
    }, [] as Options[])
  })
  return {
    formRef,
    validate,
    clearValidate,
    validateField,
    resetFormData,
    form,
    rules,
    submit,
    title,
    modelList,
    adminModelOptions,
    selectForm,
    ...rest
  }
}

export const useOptions = () => {
  const { data: adminModelOptions, refetch: refetchGetAdminModelOptions } =
    useGetAdminModelOptions()
  const adminModelTypeMap = {
    [AdminModelType.External]: '正式',
    [AdminModelType.Internal]: '内测'
  }
  const adminModelTypeOptions = objectToArray(adminModelTypeMap)
  const adminModelSourceMap = {
    [AdminModelSource.Preset]: '非自研'
  }
  const adminModelSourceOptions = objectToArray(adminModelSourceMap)

  return {
    adminModelOptions,
    adminModelTypeOptions,
    adminModelSourceOptions,
    adminModelTypeMap,
    adminModelSourceMap,
    refetchGetAdminModelOptions
  }
}
