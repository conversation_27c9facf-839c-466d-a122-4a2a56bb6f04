<template>
  <section class="bg-white rounded-lg overflow-hidden h-full p-6">
    <config-schedule
      :tabs="models"
      v-if="models.length"
      :canConfigDefault="false"
      :canCrossModelSchedule="false" />
  </section>
</template>

<script lang="ts" setup>
import { getHandConfigList } from '@/api/adminModel'
import ConfigSchedule from '@/components/config-sched/index.vue'
import useCommonStore from '@/store/common'

const models: any = ref([])
const commonStore: any = useCommonStore()

const getAdminModelListAction = async () => {
  const data = await getHandConfigList({
    pageNum: 1,
    pageSize: 500,
    filterCustomHandSrv: 1
  })
  models.value = (data.list || []).map((item: any) => {
    return {
      modelName: item.name,
      handDefault: item.handDefault,
      id: item.id,
      handSrvs: item.handSrvs || []
    }
  })
}
getAdminModelListAction()
commonStore.getModelHandsrv()
</script>
