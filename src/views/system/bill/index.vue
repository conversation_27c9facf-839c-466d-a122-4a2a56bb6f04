<template>
  <section class="bg-white rounded-lg h-full overflow-auto px-4 flex flex-col">
    <el-tabs v-model="tab">
      <el-tab-pane label="导入账单" :name="1" />
      <el-tab-pane label="修正账单" :name="2" />
      <el-tab-pane label="业务线修改" :name="3" />
    </el-tabs>
    <ImportBill
      @refetch="getCloudVendors"
      v-if="tab === 1"
      :vendors="cloudVendors"
      :canUpload="canUpload"
      :batchName="batchName"></ImportBill>
    <FixBill v-if="tab === 2" :vendors="cloudVendors"></FixBill>
    <FixBus v-if="tab === 3"></FixBus>
  </section>
</template>

<script lang="ts" setup>
import { getOptions } from '@/api/bill'
import FixBill from './fix-bill.vue'
import FixBus from './fix-bus.vue'
import ImportBill, { Vendors } from './import-bill.vue'
const tab = ref(1)
const cloudVendors = ref<Vendors>()
const canUpload = ref(false)
const batchName = ref('')
const getCloudVendors = async () => {
  const { list = [], useRate, batchName: batch } = await getOptions()
  cloudVendors.value = list as Vendors
  canUpload.value = !!useRate
  batchName.value = batch
}
getCloudVendors()
</script>
