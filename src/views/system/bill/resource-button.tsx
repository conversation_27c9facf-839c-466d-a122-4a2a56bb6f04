import { useResourceImport } from '@/api/businessresource'
import { ResponseData } from '@/plugin/axios/interceptors'
import { ElButton, ElMessage, ElMessageBox, ElUpload, UploadInstance } from 'element-plus'
export default defineComponent({
  setup() {
    const uploadResourceRef = ref<UploadInstance>()
    const { mutate: resourceImportMutate, isLoading: resourceImportLoading } = useResourceImport({
      onSuccess: () => ElMessage.success('上传成功'),
      onSettled: () => uploadResourceRef.value?.clearFiles()
    })
    const uploadSuccess = (val: ResponseData<{ fileName: string }>) =>
      resourceImportMutate({ fileName: val.data.fileName })
    const uploadData = { oriName: 1, filePath: 'businessResource' }
    const uploadError = () => {
      uploadResourceRef.value?.clearFiles()
      ElMessageBox.alert('文件上传失败，请重新选择文件上传', '上传失败', {
        confirmButtonText: '关闭'
      })
    }
    const exportFile = () => window.open('/openmis/businessresource/export')
    return () => (
      <>
        <ElUpload
          accept=".xlsx,.json"
          action="/openmis/file/upload"
          class="mr-3"
          data={uploadData}
          limit={1}
          onError={uploadError}
          onSuccess={uploadSuccess}
          ref={uploadResourceRef}
          showFileList={false}
          v-loading={resourceImportLoading.value}>
          <ElButton>选择使用资源</ElButton>
        </ElUpload>
        <ElButton onClick={exportFile}>导出使用资源</ElButton>
      </>
    )
  }
})
