<template>
  <section v-loading="isLoading" class="flex-1 flex flex-col overflow-hidden">
    <section class="flex-1 overflow-auto px-4 pl-20">
      <div class="mb-3 flex justify-between">
        <p>
          当前批次： <span class="font-bold text-lg">{{ props.batchName }}</span>
        </p>
        <div class="flex">
          <resource-button></resource-button>
          <el-button type="primary" @click="confirm">导入OP系统</el-button>
        </div>
      </div>
      <section
        v-for="cloud in props.vendors"
        :key="cloud.value"
        class="py-3 flex gap-8 items-center">
        <span class="min-w-32">{{ cloud.label }}</span>
        <el-upload
          ref="uploadRef"
          class="upload-demo flex items-center min-w-96 gap-4"
          :limit="1"
          :auto-upload="false"
          accept=".csv,.xls,.xlsx"
          :on-change="(any:UploadFile) => fileChange(any, cloud)"
          :on-remove="() => removeFile(cloud)">
          <template #trigger>
            <el-button :disabled="!!cloud.file || !props.canUpload" type="primary">上传</el-button>
          </template>
        </el-upload>
        {{ cloud.fileName }}
      </section>
    </section>
    <section class="flex-shrink-0 flex h-12 items-center justify-end pr-4 mt-3">
      <el-button type="primary" @click="submit">导入</el-button>
      <el-button type="primary" @click="preview">预览</el-button>
    </section>
  </section>
  <Preview v-if="previewDialog" @close="closeDialog"></Preview>
</template>

<script lang="ts" setup>
import resourceButton from './resource-button'
import { billReady, uploadBill } from '@/api/bill'
import { uploadAction } from '@/utils/upload'
import { ElMessage, ElMessageBox, UploadFile, UploadInstance } from 'element-plus'
import Preview from './preview.vue'
import { OptionRow } from '@/api/bill/type'
import { useMutation } from '@znzt-fe/axios'
import { useLeaveTip } from '@znzt-fe/hooks'
export type Vendors = Array<VendorsItem>
export type VendorsItem = OptionRow & { file?: UploadFile }
const props = defineProps({
  vendors: {
    type: Array as PropType<Vendors>,
    default: () => []
  },
  canUpload: {
    type: Boolean,
    default: false
  },
  batchName: {
    type: String,
    default: ''
  }
})

const { setIsEdit } = useLeaveTip(false)
const uploadRef = ref<UploadInstance[]>([])
const emits = defineEmits(['refetch'])
const previewDialog = ref(false)
const closeDialog = () => {
  previewDialog.value = false
}
const fileChange = (file: UploadFile, cloud: VendorsItem) => {
  cloud.file = {
    ...file
  }
}
const removeFile = (cloud: VendorsItem) => {
  cloud.file = undefined
}

const { mutate: submit, isLoading: submitLoading } = useMutation(
  async () => {
    const list = props.vendors.filter((item) => item.file)
    if (!list.length) {
      ElMessage.warning('请先上传账单！')
      return
    }
    const uploadPromiseList = list.map((item) =>
      uploadAction({
        file: item.file!.raw,
        filePath: 'bill',
        oriName: 1 // 回传文件名
      })
    )
    const resList = await Promise.all(uploadPromiseList)
    const uploadBillList = resList.map((item: any, index: number) =>
      uploadBill({
        fileName: item.fileName,
        fileType: list[index].value
      })
    )
    await Promise.all(uploadBillList)
  },
  {
    onSuccess: () => {
      uploadRef.value.forEach((item) => item.clearFiles())
      props.vendors.forEach((item) => (item.file = undefined))
      emits('refetch')
      ElMessage.success('导入成功！')
    }
  }
)
const confirm = async () => {
  const result = await ElMessageBox.confirm(`确认${props.batchName}数据均已上传完毕？`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  if (!result) {
    return
  }
  billReadyMutate()
}
const { mutate: billReadyMutate, isLoading: confirmLoading } = useMutation(
  async () => await billReady(),
  {
    onSuccess: () => ElMessage.success('导入成功')
  }
)

const isLoading = computed(() => submitLoading.value || confirmLoading.value)
watch(isLoading, (val) => {
  setIsEdit(val)
})
const preview = async () => {
  previewDialog.value = true
}
</script>
<style>
.el-upload-list {
  margin: 0;
  flex: 1;
}

.el-upload-list > li {
  margin: 0;
}
</style>
