<template>
  <el-form :model="state.detail" label-width="85px" ref="formRef" class="my-3 mx-6">
    <el-form-item label="云厂商" prop="fileType" class="max-w-64 !mb-6" required>
      <el-select placeholder="请选择云厂商" v-model="state.detail.fileType" filterable>
        <el-option
          v-for="item in props.vendors"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </el-select>
    </el-form-item>
    <el-form-item label="月份" prop="fixBatchName" required class="!mb-6">
      <el-date-picker
        v-model="state.detail.fixBatchName"
        type="month"
        placeholder="选择月份"
        value-format="YYYYMM" />
    </el-form-item>
    <el-form-item label="账单" required class="!mb-6">
      <el-upload
        ref="upload"
        class="upload-demo flex items-center min-w-96 gap-4"
        :limit="1"
        :auto-upload="false"
        accept=".csv,.xls,.xlsx"
        :on-change="fileChange"
        :on-remove="() => removeFile()">
        <template #trigger>
          <el-button :disabled="!!state.file" type="primary">上传</el-button>
        </template>
      </el-upload>
    </el-form-item>
    <el-form-item class="mt-8">
      <el-button type="primary" @click="subimt">修正</el-button>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { fixBill } from '@/api/bill'
import { uploadAction } from '@/utils/upload'
import { ElMessage } from 'element-plus'
const props: any = defineProps({
  vendors: {
    type: Array,
    default: () => []
  }
})
const state: any = reactive({
  detail: {},
  file: undefined
})
const fileChange = (uploadFile: any) => {
  state.file = {
    ...uploadFile
  }
}
const removeFile = () => {
  state.file = undefined
}
const subimt = async () => {
  const { file, detail = {} } = state
  const { fileType, fixBatchName } = detail
  if (!fileType) {
    ElMessage.warning('请选择云厂商！')
    return
  }
  if (!fixBatchName) {
    ElMessage.warning('请选择月份！')
    return
  }
  if (!file) {
    ElMessage.warning('请上传账单！')
    return
  }
  const ret = await uploadAction({
    file: state.file.raw,
    filePath: 'bill'
  })
  if (!ret) return
  await fixBill({
    fileType,
    fixBatchName,
    fileName: ret.fileName
  })
  ElMessage.warning('修复账单成功！')
}
</script>
<style>
.el-upload-list {
  margin: 0;
  flex: 1;
}

.el-upload-list > li {
  margin: 0;
}
</style>
