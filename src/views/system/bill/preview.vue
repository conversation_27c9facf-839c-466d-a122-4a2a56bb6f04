<template>
  <el-dialog
    v-model="state.show"
    title="预览账单"
    width="850px"
    @close="close()"
    :close-on-click-modal="false">
    <el-tabs v-model="state.tab">
      <el-tab-pane label="明细数据" :name="1" class="max-h-[600px] overflow-auto">
        <el-table :data="state.list" style="width: 100%" :show-overflow-tooltip="true">
          <el-table-column prop="id" label="id" />
          <el-table-column prop="batchName" label="批次名称/月份" />
          <el-table-column prop="type" label="类型" />
          <el-table-column prop="ori" label="数据来源" />
          <el-table-column prop="budgetName" label="业务线名称" />
          <el-table-column prop="bussName" label="业务线名称" />
          <el-table-column prop="handSrv" label="服务商/购买名称" />
          <el-table-column prop="modelName" label="模型名称/型号" />
          <el-table-column prop="skType" label="sk类型" />
          <el-table-column prop="price" label="消费" />
          <el-table-column prop="taxRate" label="税率" />
          <el-table-column prop="isCorrect" label="是否为修正数据" />
          <el-table-column prop="correctFromId" label="修正来源id" />
          <el-table-column prop="notes" label="备注" />
        </el-table>
        <Pagination
          v-model:pageNum="state.pageInfo.pageNum"
          v-model:page-size="state.pageInfo.pageSize"
          :total="state.pageInfo.total"
          @refresh="getPreviewDetail">
        </Pagination>
      </el-tab-pane>
      <el-tab-pane label="业务线汇总数据" :name="2" class="max-h-[600px] overflow-auto">
        <div>
          <div>
            <el-form inline>
              <el-form-item label="业务线">
                <el-select clearable filterable class="!w-[150px]" v-model="form.bussCode">
                  <el-option v-for="item in bussList" :value="item.bussCode" :key="item.bussCode"
                    >{{ item.bussName }}({{ item.bussCode }})</el-option
                  >
                </el-select>
              </el-form-item>
              <el-form-item label="业务线">
                <el-select clearable filterable class="!w-[150px]" v-model="form.budgetName">
                  <el-option v-for="item in budgetList" :label="item" :value="item" :key="item" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <el-table :data="busList" style="width: 100%" :show-overflow-tooltip="true">
          <el-table-column prop="batchName" label="批次" />
          <el-table-column prop="bussName" label="业务线" />
          <el-table-column prop="budgetName" label="业务线" />
          <el-table-column prop="price" label="消费" />
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="() => billExportMutate()" type="primary">导出</el-button>
        <el-button @click="close()">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { getBusinessDetail, getDetail } from '@/api/bill'
import { BillBussListItem, BillDetailListItem } from '@/api/bill/type'
import { Pagination } from '@znzt-fe/components'
import { uniqBy } from 'lodash-es'

const form = reactive({
  budgetName: '',
  bussCode: ''
})
const billExportMutate = () => window.open('/openmis/bill/export')
const state = reactive<{
  show: boolean
  tab: number
  list: BillDetailListItem[]
  pageInfo: {
    pageSize: number
    pageNum: number
    total: number
  }
  busList: BillBussListItem[]
}>({
  show: true,
  tab: 1,
  list: [],
  pageInfo: {
    pageSize: 10,
    pageNum: 1,
    total: 0
  },
  busList: []
})
const busList = computed(() => {
  let list = state.busList
  if (form.budgetName) {
    list = list.filter((item) => item.budgetName === form.budgetName)
  }
  if (form.bussCode) {
    list = list.filter((item) => item.bussCode === form.bussCode)
  }
  return list
})
const bussList = computed(() => uniqBy(state.busList, 'bussCode'))
const budgetList = computed(() => Array.from(new Set(state.busList.map((item) => item.budgetName))))
const emits = defineEmits(['close'])
const close = () => {
  state.show = false
  emits('close')
}
const getPreviewDetail = async () => {
  const res = await getDetail({
    pageNum: state.pageInfo.pageNum,
    pageSize: state.pageInfo.pageSize
  })
  state.list = res.detailList || []
  state.pageInfo.total = res.total || 0
}
const getBusDetail = async () => {
  const res = await getBusinessDetail()
  state.busList = res.bussList || []
}
const init = () => {
  getPreviewDetail()
  getBusDetail()
}
init()
</script>
