<template>
  <el-form :model="state" label-width="85px" ref="formRef" class="my-3 mx-6">
    <el-form-item label="业务线" prop="bussCode" class="!mb-6">
      <el-select placeholder="请选择业务线" class="!w-56" v-model="state.bussCode" filterable>
        <el-option
          v-for="item in businessList"
          :key="item.code"
          :label="item.name"
          :value="item.code" />
      </el-select>
    </el-form-item>
    <el-form-item label="月份" prop="fixMonth" class="!mb-6" filterable clearable>
      <el-date-picker
        v-model="state.fixMonth"
        class="!w-56"
        type="months"
        placeholder="选择月份"
        value-format="YYYYMM" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="subimt">修正</el-button>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { fixBusiness } from '@/api/bill'
import { ElMessage } from 'element-plus'
import $http from '@/api'
const state: any = reactive({
  bussCode: '',
  fixMonth: []
})
const businessList: Ref<Array<{ code: string; name: string }>> = ref([])
const { mutateAsync: getBusListAsync } = $http.useGetBusList()
const getList = async () => {
  const params: any = {
    status: 2,
    withSk: false,
    withMember: false
  }
  const data = await getBusListAsync(params)
  businessList.value = data.list || []
}
getList()
const subimt = async () => {
  const { bussCode, fixMonth } = state
  if (!bussCode) {
    ElMessage.warning('请选择业务线！')
    return
  }
  if (!fixMonth.length) {
    ElMessage.warning('请选择月份！')
    return
  }
  await fixBusiness({
    bussCode,
    fixMonth
  })
  ElMessage.warning('业务线修改成功！')
  state.bussCode = ''
  state.fixMonth = []
}
</script>
<style>
.el-upload-list {
  margin: 0;
  flex: 1;
}

.el-upload-list > li {
  margin: 0;
}
</style>
