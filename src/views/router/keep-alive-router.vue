<template>
  <el-container class="page-container">
    <KeepAliveRouterView :include="['mark-task']" />
  </el-container>
</template>

<script lang="ts" setup>
import { KeepAliveRouterView } from '@znzt-fe/components'
defineOptions({
  name: 'keep-alive-router'
})
</script>

<style scoped lang="less">
.page-container {
  padding: 12px 16px;
  height: 100%;
  // background-color: red;
  flex-direction: column;
  overflow: auto;
}
</style>
