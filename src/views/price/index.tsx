import { <PERSON><PERSON><PERSON>, ElSlider } from 'element-plus'
import style from './index.module.less'
import { TablePriceItem, useTable } from './hooks'
import { RecycleScroller, RecycleScrollerSlotProps } from 'vue-virtual-scroller'
import Progress from './progress'
import TableColumn from '@/components/tableColumn'
import Table from '@/components//table'
export default defineComponent({
  setup() {
    const {
      getModelPriceLoading,
      tableData,
      modelPrice,
      fullWidth,
      progressWidth,
      Form,
      activeForm,
      form,
      max,
      min,
      rateRef,
      minRate,
      finalRate,
      cardRef
    } = useTable()
    return () => (
      <div class={style['container']}>
        <Form />
        <ElCard class="card-column h-full" v-loading={getModelPriceLoading.value || false}>
          <div class="flex flex-col w-full" ref={cardRef}>
            <Table tableData={tableData.value} tooltip width={fullWidth.value}>
              <RecycleScroller class="h-full" item-size={58} items={modelPrice.value}>
                {(data: RecycleScrollerSlotProps<TablePriceItem>) => {
                  const { item } = data
                  const { input, inputDollar, output, outputDollar, totalDollar, total } = item
                  const inputWidth = !activeForm.value.input
                    ? 0
                    : ((item.cnPromptPrice * form.value.inputToken) / finalRate.value) * 100
                  const outputWidth = !activeForm.value.output
                    ? 0
                    : ((item.cnCompletionPrice * form.value.outputToken) / finalRate.value) * 100
                  return (
                    <div class={cx('pt-3 flex pl-4', style['active'])} key={item.name}>
                      <div class="flex shrink-0 h-[40px] leading-[40px] items-center">
                        <TableColumn data={data.index} key={'index'} prop="index" />
                        <TableColumn data={item} key={'id'} prop="id" />
                        <TableColumn
                          data={item.modelCompanyName}
                          key={'modelCompanyName'}
                          prop="modelCompanyName"
                        />
                        <TableColumn data={item.logoUrl} key={'logoUrl'} prop="logoUrl" />
                        <TableColumn data={item} key={'rate'} prop="rate" />
                        <div class="shrink-0 relative" style={{ width: progressWidth + 'px' }}>
                          <div class="flex w-full z-10 relative">
                            <TableColumn data={item.name} key={'name'} prop="name" />
                            <TableColumn
                              data={{ input, isCn: item.isCn, inputDollar }}
                              key={'input'}
                              prop="input"
                            />
                            <TableColumn
                              data={{ output, isCn: item.isCn, outputDollar }}
                              key={'output'}
                              prop="output"
                            />
                            <TableColumn
                              data={{ total, isCn: item.isCn, totalDollar }}
                              key={'total'}
                              prop="total"
                            />
                          </div>
                          <Progress inputWidth={inputWidth} outputWidth={outputWidth} />
                        </div>
                      </div>
                    </div>
                  )
                }}
              </RecycleScroller>
            </Table>
            <div class="h-[32px] shrink-0 flex justify-end mt-2">
              <div style={{ width: progressWidth.value + 'px' }}>
                <ElSlider
                  max={(max.value * minRate.value) / 4}
                  min={min.value * minRate.value}
                  showTooltip={false}
                  v-model={rateRef.value}
                />
              </div>
            </div>
          </div>
        </ElCard>
      </div>
    )
  }
})
