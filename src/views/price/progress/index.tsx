export default defineComponent({
  props: {
    inputWidth: Number,
    outputWidth: Number
  },
  setup(props) {
    return () => (
      <div class="absolute h-full top-0 flex w-full overflow-hidden">
        <div
          class="bg-[#409eff] duration-300 shrink-0"
          style={{
            width: `${props.inputWidth}%`
          }}
        />
        <div
          class="bg-[#67c23a] duration-300 shrink-0"
          style={{
            width: `${props.outputWidth}%`
          }}
        />
      </div>
    )
  }
})
