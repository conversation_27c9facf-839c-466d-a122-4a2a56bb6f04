import { useGetModelPrice } from '@/api/model'
import { PriceItem } from '@/api/model/type'
import Sort, { SortValue } from '@/components/sort'
import { TableDataProps } from '@/components/table'
import type { ResizeObserverEntry } from '@vueuse/core'
import { beforeNextTick } from '@znzt-fe/utils'
import {
  ElButton,
  ElCheckbox,
  ElForm,
  ElFormItem,
  ElInputNumber,
  ElOption,
  ElSelect,
  ElTag
} from 'element-plus'
import { cloneDeep } from 'lodash-es'
export const useOptions = () => {
  const nameList = ref<{ name: string; companyName: string }[]>([])
  const modelCompanyNameList = ref<string[]>([])
  return {
    nameList,
    modelCompanyNameList
  }
}
interface FormRef {
  inputToken: number
  outputToken: number
  calls: number
  modelCompanyName: string[]
  name: string[]
}
export const useForm = (
  nameList: Ref<{ name: string; companyName: string }[]>,
  modelCompanyNameList: Ref<string[]>,
  filterData: () => void,
  changeRate: () => void
) => {
  const form = ref<FormRef>({
    inputToken: 1_000,
    outputToken: 1_000,
    calls: 1_000,
    modelCompanyName: [],
    name: []
  })
  const activeForm = ref({
    input: true,
    output: true
  })
  const calls = computed({
    get: () => form.value.calls,
    set: (v) => (form.value.calls = v || 1)
  })
  const inputToken = computed({
    get: () => form.value.inputToken,
    set: (v) => (form.value.inputToken = v || 0)
  })
  const outputToken = computed({
    get: () => form.value.outputToken,
    set: (v) => (form.value.outputToken = v || 0)
  })

  const Form = () => (
    <ElForm inline>
      <ElFormItem>
        {{
          label: () => (
            <ElButton
              color={activeForm.value.input ? '#409eff' : '#909399'}
              onClick={() => (activeForm.value.input = !activeForm.value.input)}
              round>
              <div class="text-white">输入token</div>
            </ElButton>
          ),
          default: () => (
            <ElInputNumber
              controls={false}
              min={0}
              modelValue={inputToken.value}
              onChange={(v) => {
                inputToken.value = v!
                filterData()
                changeRate()
              }}
              precision={0}
            />
          )
        }}
      </ElFormItem>
      <ElFormItem>
        {{
          label: () => (
            <ElButton
              color={activeForm.value.output ? '#67c23a' : '#909399'}
              onClick={() => (activeForm.value.output = !activeForm.value.output)}
              round>
              <div class="text-white">输出token</div>
            </ElButton>
          ),
          default: () => (
            <ElInputNumber
              controls={false}
              min={0}
              modelValue={outputToken.value}
              onChange={(v) => {
                outputToken.value = v!
                filterData()
                changeRate()
              }}
              precision={0}
            />
          )
        }}
      </ElFormItem>
      <ElFormItem label="调用次数">
        <ElInputNumber
          controls={false}
          min={1}
          modelValue={calls.value}
          onChange={(v) => {
            calls.value = v!
            filterData()
          }}
          precision={0}
        />
      </ElFormItem>
      <ElFormItem label="厂商名称">
        <ElSelect
          class="!w-[186px]"
          clearable
          collapseTags
          collapseTagsTooltip
          filterable
          multiple
          onChange={(v) => {
            if (v.length) {
              form.value.name = form.value.name.filter((item) =>
                v.includes(nameList.value.find((name) => name.name === item)?.companyName || '')
              )
            }
            filterData()
          }}
          v-model={form.value.modelCompanyName}>
          {modelCompanyNameList.value.map((item) => (
            <ElOption key={item} label={item} value={item} />
          ))}
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="模型名称">
        <ElSelect
          class="!w-[186px]"
          clearable
          collapseTags
          collapseTagsTooltip
          filterable
          multiple
          onChange={filterData}
          v-model={form.value.name}>
          {nameList.value
            .filter(
              (item) =>
                !form.value.modelCompanyName.length ||
                form.value.modelCompanyName.includes(item.companyName)
            )
            .map((item) => (
              <ElOption key={item.name} label={item.name} value={item.name} />
            ))}
        </ElSelect>
      </ElFormItem>
    </ElForm>
  )
  return {
    form,
    activeForm,
    Form
  }
}
export interface TablePriceItem extends PriceItem {
  input: string
  inputDollar: string
  output: string
  outputDollar: string
  totalDollar: string
  total: string
  totalNumber: number
}
export const useTable = () => {
  const { nameList, modelCompanyNameList } = useOptions()
  const { form, activeForm, Form } = useForm(
    nameList,
    modelCompanyNameList,
    () => filterData(),
    () => changeRate()
  )
  // 计算出最大的total （不带调用次数）
  const max = computed(
    () =>
      modelPriceOrigin.value?.reduce((pre, now) => {
        const nowValue =
          now.cnPromptPrice * form.value.inputToken + now.cnCompletionPrice * form.value.outputToken
        return pre > nowValue ? pre : nowValue
      }, 0) || 0
  )
  // 计算出最小的total （不带调用次数）
  const min = computed(() =>
    modelPriceOrigin.value?.reduce((pre, now) => {
      const nowValue =
        now.cnPromptPrice * form.value.inputToken + now.cnCompletionPrice * form.value.outputToken
      if (!pre) {
        pre = nowValue
        return pre
      }
      if (!nowValue) return pre
      return pre > nowValue ? nowValue : pre
    }, 0)
  )
  // 把最小的total转换为大于1小于10的数字需要乘以多次10
  const minRate = computed(() => {
    let rate = 1
    let minNum = min.value
    if (!minNum) return rate
    const radix = 10
    while (minNum < 1) {
      minNum *= radix
      rate *= radix
    }
    return rate
  })
  const rateRef = ref(0)
  const finalRate = computed(() =>
    !rateRef.value
      ? 0
      : new Decimal(min.value)
          .times(minRate.value)
          .dividedBy(rateRef.value)
          .times(max.value)
          .toNumber()
  )
  // 计算设为基准的总数
  const baseRate = computed(() => {
    const baseId = base.value
    if (!baseId) return max.value * form.value.calls
    const baseDetail = computedPrice(modelPriceOrigin.value.find((item) => item.id === baseId)!)
    return baseDetail.totalNumber
  })
  const base = ref<number>(0)
  // 根据选中的基准修改缩放条的值
  whenever(base, (baseId) => {
    if (!baseId) return ''
    changeRate()
  })

  const changeRate = () => {
    if (!base.value) return ''
    const now = modelPrice.value.find((item) => item.id === base.value)!
    const nowPrice =
      now.cnPromptPrice * form.value.inputToken + now.cnCompletionPrice * form.value.outputToken
    rateRef.value = ((max.value / nowPrice) * min.value * minRate.value) / 4
  }

  const { computedPrice } = usePrice(form)
  const inputSort = ref<SortValue>('')
  const outputSort = ref<SortValue>('')
  const totalSort = ref<SortValue>('down')

  // 排序相关处理
  whenever(
    () => !inputSort.value && !outputSort.value && !totalSort.value,
    () => (totalSort.value = 'down')
  )

  const tableData: Ref<TableDataProps[]> = ref([
    {
      prop: 'index',
      label: '序号',
      class: 'px-1',
      width: 50,
      slots: (data: number) => <div>{data + 1}</div>
    },
    {
      prop: 'id',
      label: '设为基准',
      class: 'px-1',
      width: 80,
      slots: (item: TablePriceItem) => (
        <ElCheckbox
          disabled={!item.totalNumber}
          modelValue={base.value === item.id}
          onChange={(v) => (v ? (base.value = item.id) : (base.value = 0))}
        />
      )
    },
    {
      prop: 'modelCompanyName',
      label: '厂商',
      class: 'px-1',
      width: 100,
      slots: (modelCompanyName: string) => modelCompanyName
    },
    {
      prop: 'logoUrl',
      label: '厂商logo',
      class: 'px-1',
      width: 80,
      slots: (logoUrl: string) => (
        <div class="flex items-center h-full">
          <img class="w-[40px] max-h-[40px]" src={logoUrl} />
        </div>
      )
    },
    {
      prop: 'rate',
      label: '倍率',
      class: 'px-1 ',
      width: 80,
      slots: (item: TablePriceItem) => {
        const rate = (item.totalNumber / baseRate.value).toFixed(2)
        return (
          <ElTag
            disableTransitions
            effect="dark"
            type={+rate === 1 ? 'primary' : +rate > 1 ? 'danger' : 'success'}>
            {rate}
          </ElTag>
        )
      }
    },
    {
      prop: 'name',
      label: '模型名称',
      class: 'px-1 ',
      width: 300,
      rate: 3,
      slots: (name: string) => name
    },
    {
      prop: 'input',
      class: 'px-1',
      label: '输入价格',
      width: 200,
      rate: 2,
      header: () => (
        <Sort
          onChange={() => {
            outputSort.value = ''
            totalSort.value = ''
            sortFn(modelPrice.value)
          }}
          v-model={inputSort.value}>
          输入价格
        </Sort>
      ),
      slots: (params: { input: string; isCn: boolean; inputDollar: string }) => (
        <>
          {params.input}
          {!params.isCn && `(${params.inputDollar})`}
        </>
      )
    },
    {
      class: 'px-1',
      width: 200,
      rate: 2,
      prop: 'output',
      label: '输出价格',
      header: () => (
        <Sort
          onChange={() => {
            inputSort.value = ''
            totalSort.value = ''
            sortFn(modelPrice.value)
          }}
          v-model={outputSort.value}>
          输出价格
        </Sort>
      ),
      slots: (params: { output: string; isCn: boolean; outputDollar: string }) => (
        <>
          {params.output}
          {!params.isCn && `(${params.outputDollar})`}
        </>
      )
    },
    {
      class: 'px-1',
      width: 200,
      rate: 2,
      prop: 'total',
      label: '整体价格',
      header: () => (
        <Sort
          onChange={() => {
            inputSort.value = ''
            outputSort.value = ''
            sortFn(modelPrice.value)
          }}
          v-model={totalSort.value}>
          总体价格
        </Sort>
      ),
      slots: (params: { total: string; isCn: boolean; totalDollar: string }) => (
        <>
          {`${params.total}`}
          {!params.isCn && `(${params.totalDollar})`}
        </>
      )
    }
  ])
  const fullWidth = computed(() => tableData.value.reduce((pre, now) => pre + now.width, 0))
  const progressProp = ['name', 'input', 'output', 'total']
  const progressWidth = computed(() =>
    tableData.value
      .filter((item) => progressProp.includes(item.prop))
      .reduce((pre, now) => pre + now.width, 0)
  )
  const omitProgressWidth = computed(() =>
    tableData.value
      .filter((item) => !progressProp.includes(item.prop))
      .reduce((pre, now) => pre + now.width, 0)
  )
  const modelPriceOrigin = ref<PriceItem[]>([])
  const modelPrice = ref<TablePriceItem[]>([])
  // 根据 name 和 modelCompanyName做数据筛选
  const filterData = () => {
    modelPrice.value = modelPriceOrigin.value
      .filter((item) => {
        const modelCompanyName = form.value.modelCompanyName
        const name = form.value.name
        if (modelCompanyName.length && !modelCompanyName.includes(item.modelCompanyName))
          return false
        if (name.length && !name.includes(item.name)) return false
        return true
      })
      .map((item) => {
        const data = computedPrice(item)
        return { ...item, ...data }
      })
  }
  const sortFn = beforeNextTick((list: PriceItem[]) => {
    let v: PriceItem[]
    const inputToken = form.value.inputToken
    const outputToken = form.value.outputToken
    switch (true) {
      case !!totalSort.value:
        v = cloneDeep(list).sort((a, b) =>
          a.cnPromptPrice * inputToken + a.cnCompletionPrice * outputToken >
          b.cnPromptPrice * inputToken + b.cnCompletionPrice * outputToken
            ? -1
            : 1
        )
        modelPriceOrigin.value = totalSort.value === 'down' ? v : v.reverse()
        break
      case !!inputSort.value:
        v = cloneDeep(list).sort((a, b) =>
          a.cnPromptPrice * inputToken > b.cnPromptPrice * inputToken ? -1 : 1
        )
        modelPriceOrigin.value = inputSort.value === 'down' ? v : v.reverse()
        break
      case !!outputSort.value:
        v = cloneDeep(list).sort((a, b) =>
          a.cnCompletionPrice * outputToken > b.cnCompletionPrice * outputToken ? -1 : 1
        )
        modelPriceOrigin.value = outputSort.value === 'down' ? v : v.reverse()
        break
    }
    filterData()
  })
  const { mutate: getModelPriceMutate, isLoading: getModelPriceLoading } = useGetModelPrice({
    onSuccess: (res) => {
      const list = res.list
      sortFn(
        list.map((item) => {
          const promptPrice = new Decimal(item.promptPrice).dividedBy(1_000_000).toNumber()
          const completionPrice = new Decimal(item.completionPrice).dividedBy(1_000_000).toNumber()
          return {
            ...item,
            promptPrice,
            completionPrice,
            cnCompletionPrice: item.isCn
              ? new Decimal(completionPrice).toNumber()
              : new Decimal(completionPrice).times(item.rate).toNumber(),
            cnPromptPrice: item.isCn
              ? promptPrice
              : new Decimal(promptPrice).times(item.rate).toNumber()
          }
        })
      )
      nameList.value = list.map((item) => ({ name: item.name, companyName: item.modelCompanyName }))
      modelCompanyNameList.value = Array.from(new Set(list.map((item) => item.modelCompanyName)))
      base.value = list.find((item) => item.name === 'gpt-4o')?.id || 0
    }
  })
  invoke(() => getModelPriceMutate({}))
  const totalRete = tableData.value
    .filter((item) => progressProp.includes(item.prop))
    .reduce((pre, now) => pre + now.rate!, 0)
  watchDeep(form, () => sortFn(modelPriceOrigin.value))
  const cardRef = ref()
  const debouncedFn = useDebounceFn((entries: readonly ResizeObserverEntry[]) => {
    const entry = entries[0]
    const { width } = entry.contentRect
    const newProgressWidth = width - omitProgressWidth.value - 16
    tableData.value
      .filter((item) => progressProp.includes(item.prop))
      .forEach((item) => {
        item.width = (newProgressWidth / totalRete) * item.rate!
      })
  }, 300)
  useResizeObserver(cardRef, (entries) => debouncedFn(entries))
  return {
    getModelPriceLoading,
    max,
    min,
    modelPrice,
    modelPriceOrigin,
    sortFn,
    tableData,
    fullWidth,
    progressWidth,
    Form,
    activeForm,
    form,
    rateRef,
    minRate,
    finalRate,
    cardRef
  }
}

export const usePrice = (
  form: Ref<{
    inputToken: number
    outputToken: number
    calls: number
  }>
) => {
  const computedPrice = (item: PriceItem) => {
    const formInputToken = form.value.inputToken
    const formOutputToken = form.value.outputToken
    const formCalls = form.value.calls

    const inputDecimal = new Decimal(item.cnPromptPrice).times(formInputToken).times(formCalls)
    const inputDollarDecimal = new Decimal(item.promptPrice).times(formInputToken).times(formCalls)
    const inputNumber = inputDecimal.toNumber()
    const inputDollarNumber = inputDollarDecimal.toNumber()
    const input = '￥' + inputNumber.toLocaleString('en-US')
    const inputDollar = '$' + inputDollarNumber.toLocaleString('en-US')

    const outputDecimal = new Decimal(item.cnCompletionPrice)
      .times(formOutputToken)
      .times(formCalls)
    const outputDollarDecimal = new Decimal(item.completionPrice)
      .times(formOutputToken)
      .times(formCalls)
    const outputDollarNumber = outputDollarDecimal.toNumber()
    const outputNumber = outputDecimal.toNumber()
    const output = '￥' + outputNumber.toLocaleString('en-US')
    const outputDollar = '$' + outputDollarNumber.toLocaleString('en-US')

    const totalNumber = outputDecimal.plus(inputDecimal).toNumber()
    const total = '￥' + totalNumber.toLocaleString('en-US')
    const totalDollarNumber = inputDollarDecimal.plus(outputDollarDecimal).toNumber()
    const totalDollar = '$' + totalDollarNumber.toLocaleString('en-US')

    return {
      formCalls,
      input,
      inputDollar,
      output,
      outputDollar,
      total,
      totalDollar,
      totalNumber
    }
  }
  return {
    computedPrice
  }
}
