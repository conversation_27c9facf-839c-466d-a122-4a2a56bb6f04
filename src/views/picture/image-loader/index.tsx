import style from './index.module.less'
import { ElButton, ElIcon, ElText } from 'element-plus'
import { useLoadImage, useLoading, useShowMasker } from './hooks'
import { CopyDocument } from '@element-plus/icons-vue'
import { isSupportWebp } from '@znzt-fe/webp'
export default defineComponent({
  props: {
    originImageUrl: {
      type: String,
      default: ''
    },
    webpImageUrl: {
      type: String,
      default: ''
    },
    thumbnailImageUrl: {
      type: String,
      default: ''
    },
    isError: {
      type: Boolean,
      default: false
    },
    /** 控制是否在展示缩略图之后，原图之前展示loading，目前仅弹窗需要 */
    showLoading: {
      type: Boolean,
      default: false
    },
    /**控制组件先不加载原图，目前查看历史传递为true */
    originPending: {
      type: Boolean,
      default: false
    },
    /**控制组件先不加载缩略图，目前查看历史传递为true */
    thumbnailPending: {
      type: Boolean,
      default: false
    },
    elementLoadingText: {
      type: String,
      default: '正在生成图像...'
    },
    elementLoadingBackground: {
      type: String,
      default: ''
    },
    prompt: {
      type: String,
      default: ''
    },
    width: { type: String, default: '300px' },
    height: { type: String, default: '300px' }
  },
  exposed: ['abort', 'continue'],
  emits: ['refetch', 'beforeThumbnail', 'thumbnailLoad', 'beforeOrigin', 'originLoad'],
  setup(props, { emit, expose }) {
    const {
      originImageUrl: originImageUrlProp,
      webpImageUrl,
      thumbnailImageUrl,
      isError,
      width,
      height,
      originPending,
      thumbnailPending,
      elementLoadingText,
      elementLoadingBackground,
      showLoading,
      prompt
    } = toRefs(props)
    const originImageUrl = computed(() => {
      if (!isSupportWebp() || !webpImageUrl.value) return originImageUrlProp.value
      return webpImageUrl.value
    })
    const beforeThumbnailEmit = () => emit('beforeThumbnail')
    const beforeOriginEmit = () => emit('beforeOrigin')
    const thumbnailLoadEmit = () => emit('thumbnailLoad')
    const originLoadEmit = () => emit('originLoad')
    const refetchEmit = () => emit('refetch')
    const { loading, setLoading, refetch } = useLoading(isError, refetchEmit)
    const { abort, fetchOriginUrl, url, picLoading } = useLoadImage(
      thumbnailImageUrl,
      originImageUrl,
      thumbnailPending,
      originPending,
      beforeThumbnailEmit,
      beforeOriginEmit,
      thumbnailLoadEmit,
      originLoadEmit,
      () => setLoading(false),
      showLoading
    )
    const { setShowMasker, showMasker, copyPrompt } = useShowMasker(prompt)
    expose({
      abort: () => abort.value?.abort(),
      continue: () => fetchOriginUrl()
    })

    return () => (
      <div
        class={style['container']}
        element-loading-background={elementLoadingBackground.value}
        element-loading-text={elementLoadingText.value}
        style={{ width: width.value, height: height.value }}
        v-loading={loading.value || picLoading.value}>
        {isError.value ? (
          <div class="flex-just-center h-full">
            <div class="block">
              <div class="flex-just-center">
                <ElText type="danger">图片获取失败</ElText>
              </div>
              <div class="flex-just-center">
                <ElButton
                  onClick={refetch}
                  size="small"
                  style={{ marginTop: '10px' }}
                  type="primary">
                  重试
                </ElButton>
              </div>
            </div>
          </div>
        ) : (
          <div
            class="relative w-full h-full"
            onMouseenter={() => setShowMasker(true)}
            onMouseleave={() => setShowMasker(false)}>
            <img class="w-full" src={url.value} />
            <div
              class="w-full h-full flex-center absolute overflow-auto"
              style={{
                padding: '10px',
                boxSizing: 'border-box',
                background: 'rgba(225,225,225,0.85)',
                top: 0
              }}
              v-show={showMasker.value}>
              <div class="flex-direction max-h-full">
                <div
                  class="max-h-full overflow-hidden text-ellipsis"
                  style={{
                    padding: '0 10px',
                    display: '-webkit-box',
                    '-webkit-box-orient': 'vertical',
                    '-webkit-line-clamp': 4,
                    boxSizing: 'border-box'
                  }}>
                  {prompt.value}
                </div>
                <div class="flex-just-end absolute" style={{ bottom: '24px', right: '24px' }}>
                  <ElButton
                    icon={
                      <ElIcon size={20}>
                        <CopyDocument />
                      </ElIcon>
                    }
                    link
                    onClick={(e) => {
                      e.stopPropagation()
                      copyPrompt()
                    }}
                    type="primary"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }
})
