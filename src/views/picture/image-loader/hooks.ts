import { afterDecorator, copyText, decorator, fetchFile } from '@znzt-fe/utils'
import { ElMessage } from 'element-plus'

export const useLoadImage = (
  thumbnailImageUrl: Ref<string>,
  originImageUrl: Ref<string>,
  thumbnailPending: Ref<boolean>,
  originPending: Ref<boolean>,
  beforeThumbnailEmit: () => void,
  beforeOriginEmit: () => void,
  thumbnailLoadEmit: () => void,
  originLoadEmit: () => void,
  setLoadingFalse: () => void,
  showLoading: Ref<boolean>
) => {
  const { setPicLoading, picLoading } = usePicLoading(showLoading)
  const abort = ref<AbortController>()
  const url = ref('')
  const initAbort = () => {
    abort.value = new AbortController()
    return abort.value.signal
  }
  const setImageUrl = afterDecorator(
    async (imageUrl: string, signal?: AbortSignal) => {
      const image = await fetchFile(imageUrl, { signal, onlyUrl: true })
      if (!image) return
      url.value = image
    },
    () => {
      setLoadingFalse()
    }
  )
  const setThumbnailUrl = decorator(setImageUrl, {
    beforeFn: beforeThumbnailEmit,
    afterFn: () => {
      thumbnailLoadEmit()
      setPicLoading(true)
    }
  })
  const setOriginUrl = decorator((url: string) => setImageUrl(url, initAbort()), {
    beforeFn: beforeOriginEmit,
    afterFn: () => {
      originLoadEmit()
      setPicLoading(false)
    }
  })
  invoke(async () => {
    await until(originImageUrl).toBeTruthy()
    if (thumbnailImageUrl.value) {
      await until(thumbnailPending).toBe(false)
      await setThumbnailUrl(thumbnailImageUrl.value)
    }
    /** 此处控制权交由父组件手动调用fetchOriginUrl */
    if (originPending.value) {
      return
    }
    await fetchOriginUrl()
  })
  const fetchOriginUrl = () => setOriginUrl(originImageUrl.value)
  return {
    fetchOriginUrl,
    abort,
    url,
    picLoading
  }
}
export const usePicLoading = (showLoading: Ref<boolean>) => {
  const [picLoading, setPicLoading] = useToggle(false)
  const setSafePicLoading = (loading: boolean) => {
    if (showLoading.value) setPicLoading(loading)
  }
  return {
    picLoading,
    setPicLoading: setSafePicLoading
  }
}
export const useLoading = (isError: Ref<boolean>, refetchEmit: () => void) => {
  /** 加载图片前的loading状态 */
  const [loading, setLoading] = useToggle(true)

  whenever(isError, () => setLoading(false))
  const init = () => setLoading(true)
  const refetch = () => {
    init()
    refetchEmit()
  }
  onMounted(init)
  return {
    loading,
    setLoading,
    refetch
  }
}

export const useShowMasker = (prompt: Ref<string>) => {
  const [showMasker, setShowMasker] = useToggle(false)
  const copyPrompt = async () => {
    await copyText(prompt.value)
    ElMessage.success('复制prompt成功')
  }
  return {
    showMasker,
    setShowMasker: prompt.value ? setShowMasker : () => {},
    copyPrompt
  }
}
