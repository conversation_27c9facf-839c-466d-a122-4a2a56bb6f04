.container {
  align-items: flex-start;
  flex-grow: 1;
  overflow: hidden;
  .image-guide {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    grid-row-gap: 12px;
    grid-column-gap: 12px;
  }

  .title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .operation {
    box-sizing: border-box;
    position: sticky;
    top: 0;
    height: 100%;
    transition: 0.3s;
    flex-shrink: 0;
    background: #f2f2f4;
    overflow: hidden;
    &-title {
      font-weight: 500;
      font-size: 16px;
      width: 300px;
      box-sizing: border-box;
      margin-bottom: 24px;
    }
  }

  .main-container {
    flex-grow: 1;
    padding-right: 24px;
    padding-bottom: 10px;
    overflow: auto;
    box-sizing: border-box;
  }
}
