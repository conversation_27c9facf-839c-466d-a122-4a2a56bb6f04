import { useImageGen } from '@/api/imagelab'
import { ImageGenParams } from '@/api/imagelab/type'
import { usePictureStore } from '@/store/picture'
import { suffixWebp } from '@/utils'
import { Operation, Right } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { v4 as uuidv4 } from 'uuid'
export const useOption = () => {
  const [operationVisible, setOperationVisible] = useToggle(false)
  const operationData = computed(() =>
    operationVisible.value
      ? {
          rightPanelWidth: '300px',
          icon: <Right />
        }
      : {
          rightPanelWidth: '0',
          icon: <Operation />
        }
  )
  const styleOptions = [
    { value: 'vivid', label: '生动' },
    { value: 'natural', label: '自然' }
  ]
  const qualityOptions = [
    { value: 'standard', label: '标准' },
    { value: 'hd', label: '高清' }
  ]
  return {
    setOperationVisible,
    operationVisible,
    operationData,
    qualityOptions,
    styleOptions
  }
}

export const usePicture = () => {
  const { imageGuide, showImageGuide } = storeToRefs(usePictureStore())
  return {
    imageGuide,
    showImageGuide
  }
}
interface MapValue {
  url: string
  webpUrl: string
  status: 'pending' | 'success' | 'error'
  options: ImageGenParams
  id: number
}
export const useForm = () => {
  const imageListMap = ref(new Map<string, MapValue>())
  const deletePrompt = () => {
    const key = [...imageListMap.value.keys()][imageListIndex.value]
    imageListMap.value.delete(key)
  }

  const { mutateAsync: imageGenMutateAsync } = useImageGen()
  const form = reactive<ImageGenParams>({
    prompt: '',
    style: 'vivid',
    quality: 'standard'
  })
  const imageGen = async () => {
    if (!form.prompt) {
      ElMessage.warning('请填写prompt')
      return
    }
    const uuid = uuidv4()
    const options = { ...form }
    imageListMap.value.set(uuid, {
      url: '',
      webpUrl: '',
      status: 'pending',
      options,
      id: -1
    })
    fetchImage(uuid)
  }
  const refetch = (uuid: string) => fetchImage(uuid)
  const fetchImage = async (uuid: string) => {
    const options = imageListMap.value.get(uuid)!.options
    imageListMap.value.set(uuid, { url: '', status: 'pending', options, id: -1, webpUrl: '' })
    try {
      const data = await imageGenMutateAsync(options)
      imageListMap.value.set(uuid, {
        url: data.url,
        status: 'success',
        options,
        id: data.id,
        webpUrl: data.url + suffixWebp
      })
    } catch {
      imageListMap.value.set(uuid, { url: '', status: 'error', options, id: -1, webpUrl: '' })
    }
  }
  const imageList = computed(() => [...imageListMap.value.entries()])
  const { count: imageListIndex, set: setImageListIndex, inc, dec } = useCounter(0)
  const pre = computed(() => !!Reflect.get(imageList.value, imageListIndex.value - 1))
  const next = computed(() => !!Reflect.get(imageList.value, imageListIndex.value + 1))
  const onPre = dec
  const onNext = inc
  const imageDetail = computed(() => imageList.value[imageListIndex.value])
  return {
    imageGen,
    form,
    imageList,
    refetch,
    pre,
    next,
    imageListIndex,
    onPre,
    onNext,
    setImageListIndex,
    imageDetail,
    deletePrompt
  }
}
