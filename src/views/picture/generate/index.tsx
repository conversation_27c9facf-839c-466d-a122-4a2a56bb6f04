import { ElButton, ElForm, ElFormItem, ElIcon, ElInput, ElOption, ElSelect } from 'element-plus'
import ImageLoader from '../image-loader'
import style from './index.module.less'
import { useForm, useOption, usePicture } from './hooks'
import LoadingWrap from '../loading-wrap'
import PictureModal from '../picture-modal'
import { useElModal } from '@znzt-fe/hooks'
import { VideoPlay } from '@element-plus/icons-vue'
import { usePreventButton } from '@/hooks/usePreventButton'
import { useUserStore } from '@/store/user'
export default defineComponent({
  setup() {
    const { setOperationVisible, operationData, qualityOptions, styleOptions, operationVisible } =
      useOption()
    const { imageGuide, showImageGuide } = usePicture()
    const {
      form,
      imageGen,
      imageList,
      refetch,
      pre,
      next,
      imageDetail,
      onNext,
      onPre,
      setImageListIndex,
      deletePrompt
    } = useForm()
    const user = useUserStore()
    const { visible, openModal } = useElModal()
    const event = usePreventButton()
    return () => (
      <div class={cx('flex-just-space', style['container'])}>
        <div class={cx('flex-direction', 'h-full', style['main-container'])}>
          <div class="flex-just-end" style={{ marginTop: '24px' }}>
            <ElButton
              icon={<ElIcon size={'18px'}>{operationData.value.icon}</ElIcon>}
              link
              onClick={() => setOperationVisible()}
              size="large"
              type="primary"
            />
          </div>
          <div class="flex-just-space" style={{ marginTop: '16px' }}>
            <div>描述你想要的图片</div>
          </div>
          <div class="flex-just-space" style={{ marginTop: '12px' }}>
            <ElInput
              clearable
              placeholder={imageGuide.value?.guidText.exampleContent}
              style={{ marginRight: '10px' }}
              v-model={form.prompt}
            />
            <ElButton onClick={imageGen} {...event} type="primary">
              生成
            </ElButton>
          </div>
          {showImageGuide.value && !imageList.value.length ? (
            <div
              class="flex-just-center grow relative"
              style={{
                margin: `60px ${operationVisible.value ? '0' : '100px'} 0`
              }}>
              <LoadingWrap>
                {imageGuide.value?.imageList.length && (
                  <div class={style['image-guide']}>
                    {imageGuide.value?.imageList.map((item) => (
                      <ImageLoader
                        key={item.originImageUrl}
                        originImageUrl={item.originImageUrl}
                        thumbnailImageUrl={item.thumbnailImageUrl}
                        webpImageUrl={item.webpImageUrl}
                      />
                    ))}
                  </div>
                )}
              </LoadingWrap>
              <div style={{ marginLeft: '36px' }}>
                <div class={style['title']}>{imageGuide.value?.guidText.tipTitle}</div>
                <div>{imageGuide.value?.guidText.tipContent}</div>
                <div
                  class={style['title']}
                  style={{
                    marginTop: '48px'
                  }}>
                  {imageGuide.value?.guidText.exampleTitle}
                </div>
                <div>{imageGuide.value?.guidText.exampleContent}</div>
              </div>
              <div class="absolute right-0 bottom-0">
                <ElButton
                  icon={
                    <ElIcon>
                      <VideoPlay />
                    </ElIcon>
                  }
                  onClick={() => {
                    form.prompt = imageGuide.value?.guidText.exampleContent || ''
                    imageGen()
                  }}>
                  试一试
                </ElButton>
              </div>
            </div>
          ) : (
            <div class="flex-just-start flex-wrap">
              {imageList.value.map((item, index) => (
                <div
                  key={item[1].url}
                  onClick={() => {
                    if (item[1].status !== 'success') return
                    setImageListIndex(index)
                    openModal()
                  }}
                  style={{
                    padding: '6px',
                    cursor: item[1].status !== 'success' ? 'unset' : 'pointer'
                  }}>
                  <ImageLoader
                    isError={item[1].status === 'error'}
                    key={item[1].url}
                    onRefetch={() => refetch(item[0])}
                    originImageUrl={item[1].url}
                    prompt={item[1].options.prompt}
                    webpImageUrl={item[1].webpUrl}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
        <div
          class={style['operation']}
          style={{
            width: operationData.value.rightPanelWidth
          }}>
          <div style={{ padding: '24px 12px' }}>
            <div class={style['operation-title']}>设置生图参数</div>
            <ElForm>
              <ElFormItem label="图像样式">
                <ElSelect v-model={form.style}>
                  {styleOptions.map((item) => (
                    <ElOption key={item.value} label={item.label} value={item.value} />
                  ))}
                </ElSelect>
              </ElFormItem>
              <ElFormItem label="图像质量">
                <ElSelect v-model={form.quality}>
                  {qualityOptions.map((item) => (
                    <ElOption key={item.value} label={item.label} value={item.value} />
                  ))}
                </ElSelect>
              </ElFormItem>
            </ElForm>
          </div>
        </div>
        <PictureModal
          id={imageDetail.value?.[1].id}
          next={next.value}
          onDeletePrompt={deletePrompt}
          onNext={onNext}
          onPre={onPre}
          originImageUrl={imageDetail.value?.[1].url}
          owner={user.name}
          pre={pre.value}
          prompt={imageDetail.value?.[1].options.prompt}
          v-model={visible.value}
        />
      </div>
    )
  }
})
