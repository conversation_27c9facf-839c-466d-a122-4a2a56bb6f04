import ImageLoader from '../image-loader'
import { useSchedule, Status, useModal, useImageGenList } from './hooks'
import PictureModal from '../picture-modal'
import { safeSetMapValue } from '@znzt-fe/utils'
import { Search, WarningFilled } from '@element-plus/icons-vue'
import { ElButton, ElIcon, ElInput, ElTooltip } from 'element-plus'
import { useUserStore } from '@/store/user'
export default defineComponent({
  setup() {
    const { imageGenList, prompt, search, deleteImageById } = useImageGenList()
    const { eleMap, thumbnailLoad, loaderMap, pause, recovery, deleteSet } = useSchedule(
      imageGenList,
      deleteImageById
    )
    const user = useUserStore()
    const {
      next,
      onNext,
      pre,
      onPre,
      imageDetail,
      setImageListIndex,
      openModal,
      visible,
      deletePrompt
    } = useModal(pause, deleteSet, imageGenList)

    return () => (
      <div class="flex-direction" style={{ flex: 1, overflow: 'auto' }}>
        <div class="flex-just-space">
          <div
            class="flex-just-start"
            style={{ padding: '24px 10px', color: '#303133', fontSize: '16px', fontWeight: 500 }}>
            <span>历史</span>
            <ElTooltip
              class="box-item"
              content="历史图片储存上限为150张，超过上限时，最老的图片将被自动替换。"
              effect="dark"
              placement="right">
              <ElButton
                icon={
                  <ElIcon>
                    <WarningFilled />
                  </ElIcon>
                }
                link
                style={{ marginLeft: '8px' }}
              />
            </ElTooltip>
          </div>
          <div style={{ marginRight: '24px' }}>
            <ElInput
              clearable
              onBlur={search}
              onKeydown={(e) => (e as KeyboardEvent).code === 'Enter' && search()}
              placeholder="根据prompt搜索"
              prefixIcon={<Search />}
              v-model={prompt.value}
            />
          </div>
        </div>
        <div
          class="flex-just-start grow overflow-auto flex-wrap"
          style={{ alignItems: 'flex-start' }}>
          {imageGenList.value.map((item, index) => (
            <div
              data-id={item.id}
              key={item.id}
              onClick={() => {
                setImageListIndex(index)
                openModal()
              }}
              ref={(el) =>
                safeSetMapValue(eleMap, item.id, (value) => (value.target = el as Element))
              }
              style={{ padding: '6px', cursor: 'pointer' }}>
              <ImageLoader
                elementLoadingText="正在加载图片..."
                onBeforeThumbnail={() =>
                  safeSetMapValue(eleMap, item.id, (item) => (item.status = Status.beforeThumbnail))
                }
                onThumbnailLoad={() => {
                  safeSetMapValue(eleMap, item.id, (item) => (item.status = Status.thumbnailLoad))
                  thumbnailLoad()
                }}
                originImageUrl={item.originImageUrl}
                originPending={loaderMap.value.get(item.id)?.originPending}
                prompt={item.prompt}
                ref={(ref) => safeSetMapValue(loaderMap, item.id, (value) => (value.ref = ref))}
                thumbnailImageUrl={item.thumbnailImageUrl}
                thumbnailPending={loaderMap.value.get(item.id)?.thumbnailPending}
                webpImageUrl={item.webpImageUrl}
              />
            </div>
          ))}
        </div>
        <PictureModal
          id={imageDetail.value?.id}
          next={next.value}
          onDeletePrompt={deletePrompt}
          onNext={onNext}
          onOriginLoad={recovery}
          onPre={onPre}
          originImageUrl={imageDetail.value?.originImageUrl}
          owner={user.name}
          pre={pre.value}
          prompt={imageDetail.value?.prompt}
          thumbnailImageUrl={imageDetail.value?.thumbnailImageUrl}
          v-model={visible.value}
        />
      </div>
    )
  }
})
