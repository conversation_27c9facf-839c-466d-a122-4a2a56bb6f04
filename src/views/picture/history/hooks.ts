import { usePictureStore } from '@/store/picture'
import { useElModal } from '@znzt-fe/hooks'
import { ImageGenListItem } from '@/api/imagelab/type'
import { safeSetMapValue } from '@znzt-fe/utils'
import { differenceBy, intersectionBy } from 'lodash-es'
interface EleValue {
  target: Element | null
  status: Status
  observe: IntersectionObserver | null
  id: number
}
type ImageLoaderRef = ComponentPublicInstance<any>
interface LoaderValue {
  ref: ImageLoaderRef | null
  thumbnailPending: boolean // 控制图片首次加载缩略图片
  originPending: boolean // 控制imageLoader组件不直接加载原图，需要阻塞
  id: number
}
export const enum Status {
  pending,
  beforeThumbnail,
  thumbnailLoad,
  suspend,
  beforeOrigin,
  originLoad
}
const MaximumConcurrency = 3
export const useImageGenList = () => {
  const { imageGenList } = storeToRefs(usePictureStore())
  const prompt = ref('')
  const imageList = computed(() =>
    imageGenList.value.filter((item) => !!~item.prompt.search(finalPrompt.value))
  )
  const finalPrompt = ref('')
  const search = () => {
    finalPrompt.value = prompt.value
  }
  const deleteImageById = (id: number) => {
    const index = imageGenList.value.findIndex((item) => item.id === id)
    imageGenList.value.splice(index, 1)
  }
  return { imageGenList: imageList, prompt, search, deleteImageById }
}

export const useSchedule = (
  imageGenList: ComputedRef<ImageGenListItem[]>,
  deleteImageById: (id: number) => void
) => {
  const eleMap = ref<Map<number, EleValue>>(new Map())
  const loaderMap = ref<Map<number, LoaderValue>>(new Map())
  /** 最近应该被渲染的原图，每一项为id */
  const originPendingSet = ref(new Set<number>())

  const setOriginPendingSet = (id: number) => {
    if (eleMap.value.get(id)?.status === Status.originLoad)
      // 已经加载完毕则不需要进入队列
      return
    // 提升优先级
    originPendingSet.value.delete(id)
    originPendingSet.value.add(id)
  }
  /**
   *  此处init不可以在 until中触发（即使传递了flush 为 pre）
   *  until源码中，会把watcher放到promise.then去触发，也就是自动nextTick，此处渲染页面元素根据imageGenList
   *  初始化如果在后，则会造成props传递报错
   * */
  const init = (newVal: ImageGenListItem[], oldVal: ImageGenListItem[]) => {
    pause()
    /** 需要保留的数据 */
    const intersectionArr = intersectionBy(newVal, oldVal, 'id').map((item) => item.id)
    /** 新添加的数据 */
    const differenceArr = differenceBy(newVal, oldVal, 'id').map((item) => item.id)

    const oldEleMapValue = intersectionArr.map((item) => eleMap.value.get(item))
    const oldLoaderMapValue = intersectionArr.map((item) => loaderMap.value.get(item))

    eleMap.value = new Map()
    loaderMap.value = new Map()

    oldEleMapValue.forEach((item) => eleMap.value.set(item!.id, item!))
    oldLoaderMapValue.forEach((item) => loaderMap.value.set(item!.id, item!))

    differenceArr.forEach((item) =>
      eleMap.value.set(item, {
        status: Status.pending,
        target: null,
        observe: null,
        id: item
      })
    )
    differenceArr.forEach((item) =>
      loaderMap.value.set(item, {
        thumbnailPending: true,
        originPending: true, // 触发时机由父组件手动触发，所以此值暂时不需要修改
        ref: null,
        id: item
      })
    )

    /** 需要删除的数据 */
    const deleteArr = differenceBy(oldVal, newVal, 'id')
    deleteArr.forEach((item) => originPendingSet.value.delete(item.id))
    recovery()
  }

  watch(imageGenList, init)

  watch(
    imageGenList,
    () => {
      eleMap.value.forEach((item) => {
        const observer = new IntersectionObserver(callback)
        observer.observe(item.target!)
        item.observe = observer
      })
    },
    { flush: 'post' }
  )
  /** 查询最近未加载原图 */
  const getIdleEleIndex = (arr: number[], index: number): number | false => {
    /** 获取到存储在 originPendingSet 的 index */
    const loadOriginId = arr[index]
    // 没有则说明全部完毕
    if (typeof loadOriginId !== 'number') {
      return false
    }
    // 如果查询到非空闲状态，则递归找下一个
    if (eleMap.value.get(loadOriginId)!.status >= Status.beforeOrigin) {
      return getIdleEleIndex(arr, index + 1)
    }
    return index
  }
  const [suspense, setSuspense] = useToggle(false)
  const pause = () => {
    setSuspense(true)
    const eleArr = [...eleMap.value.values()]
    // 正在加载的全部触发abort中断请求
    eleArr
      .reduce((pre: number[], now) => {
        if (now.status === Status.beforeOrigin) {
          now.status = Status.suspend
          pre.push(now.id)
        }
        return pre
      }, [])
      .forEach((item) => loaderMap.value.get(item)?.ref.abort())
  }
  const recovery = () => {
    setSuspense(false)
    const eleArr = [...eleMap.value.values()]
    eleArr
      .reduce((pre: number[], now) => {
        if (now.status === Status.suspend) {
          pre.push(now.id)
        }
        return pre
      }, [])
      .forEach(loadOriginImage)
  }
  const thumbnailLoad = () => {
    if (suspense.value) return
    // 添加顺序为add，所以需要reverse
    /** originPendingSet 转换为正常顺序数组 */
    const arr = [...originPendingSet.value.values()].reverse()
    /** 是否全部加载完毕缩略图 */
    const allThumbnailLoad = arr.every(
      (id) => eleMap.value.get(id)?.status === Status.thumbnailLoad
    )
    // 如果没有则不应该加载原图
    if (!allThumbnailLoad) {
      return
    }
    /** 获取正在加载的个数 */
    const beforeOriginLength = [...eleMap.value.values()].filter(
      (item) => item.status === Status.beforeOrigin
    ).length
    /** 获取可以加载原图的个数，最大并发减去正在加载的数量 */
    Array(MaximumConcurrency - beforeOriginLength)
      // 不填充则无法reduce
      .fill(0)
      .reduce((pre: number[], _now, index) => {
        /** 获取空闲index */
        const IdleEleIndex = getIdleEleIndex(arr, index)
        IdleEleIndex !== false && pre.push(IdleEleIndex)
        return pre
      }, [])
      .forEach((item) => {
        const loadOriginId = arr[item]
        if (typeof loadOriginId !== 'number') {
          return
        }
        loadOriginImage(loadOriginId)
      })
  }
  watchDeep(originPendingSet, thumbnailLoad)

  const loadOriginImage = async (loadOriginId: number) => {
    safeSetMapValue(eleMap, loadOriginId, (item) => (item.status = Status.beforeOrigin))
    await loaderMap.value.get(loadOriginId)?.ref.continue()
    // 如果原图被中断
    if (eleMap.value.get(loadOriginId)?.status === Status.suspend) {
      return
    }
    /** 从等待队列中删除，并且由于状态修改为了 Status.originLoad，后续也不会再次进入等待队列 */
    safeSetMapValue(eleMap, loadOriginId, (item) => (item.status = Status.originLoad))
    safeSetMapValue(eleMap, loadOriginId, (item) => item.observe?.disconnect())
    originPendingSet.value.delete(loadOriginId)
  }

  const callback: IntersectionObserverCallback = (entries) => {
    entries.forEach((entry) => {
      const id = Number(entry.target.getAttribute('data-id'))
      const eleItem = eleMap.value.get(id)
      const loaderItem = loaderMap.value.get(id)
      if (entry.isIntersecting) {
        // 初次进入可视范围内则加载缩略图
        if (eleItem?.status === Status.pending) {
          loaderItem!.thumbnailPending = false
        }
        // 如果进入视口，则提升渲染原图优先级
        setOriginPendingSet(id)
      }
    })
  }
  const deleteSet = (id: number) => {
    deleteImageById(id)
  }
  return {
    imageGenList,
    eleMap,
    loaderMap,
    pause,
    recovery,
    setOriginPendingSet,
    thumbnailLoad,
    deleteSet
  }
}

export const useModal = (
  pause: () => void,
  deleteSet: (id: number) => void,
  imageGenList: ComputedRef<ImageGenListItem[]>
) => {
  // 记录弹窗中当前是哪一个图片
  const { count: imageListIndex, set: setImageListIndex, inc, dec } = useCounter(0)
  const pre = computed(() => !!Reflect.get(imageGenList.value, imageListIndex.value - 1))
  const next = computed(() => !!Reflect.get(imageGenList.value, imageListIndex.value + 1))
  const onPre = dec
  const onNext = inc
  const imageDetail = computed(() => imageGenList.value[imageListIndex.value])
  // 弹窗中切换图片需要继续触发pause
  watch(imageListIndex, pause)
  const { visible, openModal } = useElModal()
  const deletePrompt = () => deleteSet(imageDetail.value.id)
  return {
    imageDetail,
    onPre,
    onNext,
    pre,
    next,
    setImageListIndex,
    visible,
    openModal,
    deletePrompt
  }
}
