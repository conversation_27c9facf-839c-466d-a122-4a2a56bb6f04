import { ElTabs, ElTabPane } from 'element-plus'
import Generate from './generate'
import History from './history'
import style from './index.module.less'
import { usePictureStore } from '@/store/picture'
export default defineComponent({
  setup() {
    const { init } = usePictureStore()
    onMounted(init)
    const tabList = [
      {
        label: '生图',
        name: 'generatingPicture'
      },
      {
        label: '历史',
        name: 'history'
      }
    ]
    const activeTab = ref(tabList[0].name)
    return () => (
      <div class={cx('flex-direction', 'h-full', style['container'])}>
        <ElTabs class={style['tabs']} v-model={activeTab.value}>
          {tabList.map((item) => (
            <ElTabPane key={item.name} label={item.label} name={item.name} />
          ))}
        </ElTabs>
        <Generate v-show={activeTab.value === tabList[0].name} />
        <History v-show={activeTab.value === tabList[1].name} />
      </div>
    )
  }
})
