import { useImageGenDel } from '@/api/imagelab'
import { copyText, fetchFile } from '@znzt-fe/utils'
import { ElMessage, ElMessageBox } from 'element-plus'

export const useDialog = (
  prompt: Ref<string>,
  src: Ref<string>,
  deletePromptEmit: (id: number) => void
) => {
  const buttonStyle = {
    style: { padding: '8px' }
  }
  const copyPrompt = async () => {
    await copyText(prompt.value)
    ElMessage.success('复制prompt成功')
  }
  const deletePrompt = async (id: number) => {
    const result = await ElMessageBox.confirm('确认删除', '警告', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }).catch(() => false)
    if (!result) return
    await imageGenDelMutateAsync({ id })
    ElMessage.success('删除成功')
    deletePromptEmit(id)
  }
  const { mutateAsync: imageGenDelMutateAsync } = useImageGenDel()
  const downLoad = () => fetchFile(src.value!)
  return {
    deletePrompt,
    downLoad,
    copyPrompt,
    buttonStyle
  }
}

export const useKeyup = (visible: Ref<boolean>, emitNext: () => void, emitPre: () => void) => {
  const onKeyup = (e: KeyboardEvent) => {
    switch (e.code) {
      case 'ArrowRight':
        emitNext()
        break
      case 'ArrowLeft':
        emitPre()
        break
    }
  }
  const removeEventListener = () => document.removeEventListener('keydown', onKeyup)
  watch(visible, (val) => {
    if (val) {
      document.addEventListener('keydown', onKeyup)
    } else {
      removeEventListener()
    }
  })
  onBeforeUnmount(removeEventListener)
}
