import { Back, CopyDocument, Delete, Download, Picture, Right } from '@element-plus/icons-vue'
import { ElButton, ElDialog, ElIcon, ElSpace, ElTooltip } from 'element-plus'
import { useDialog, useKeyup } from './hook'
import style from './index.module.less'
import ImageLoader from '../image-loader'
export default defineComponent({
  props: {
    modelValue: Boolean,
    id: Number,
    prompt: { type: String, default: '' },
    owner: { type: String, default: '' },
    next: <PERSON>olean,
    pre: Boolean,
    thumbnailImageUrl: { type: String, default: '' },
    webpImageUrl: { type: String, default: '' },
    originImageUrl: { type: String, default: '' }
  },
  emits: ['update:modelValue', 'deletePrompt', 'pre', 'next', 'originLoad'],
  setup(props, { emit }) {
    const visible = useModel(props, 'modelValue')
    const { thumbnailImageUrl, originImageUrl, next, pre, prompt, id, webpImageUrl } = toRefs(props)
    const emitPre = () => pre.value && emit('pre')
    const emitNext = () => next.value && emit('next')
    useKeyup(visible, emitNext, emitPre)
    const deletePromptEmit = (id: number) => {
      emit('deletePrompt', { id })
      close()
    }
    const close = () => {
      visible.value = false
    }
    const originLoadEmit = () => emit('originLoad')
    const { deletePrompt, downLoad, copyPrompt, buttonStyle } = useDialog(
      prompt,
      originImageUrl,
      deletePromptEmit
    )
    return () => (
      <ElDialog modalClass={style['dialog']} v-model={visible.value}>
        <div class="flex-just-space w-full">
          <ElTooltip content="上一个（键盘 <-）">
            <ElButton
              disabled={!pre.value}
              icon={
                <ElIcon size={16}>
                  <Back />
                </ElIcon>
              }
              link
              onClick={emitPre}
              type="primary"
            />
          </ElTooltip>
          <div class={cx('flex-just-space', style['container'])}>
            {originImageUrl.value ? (
              <ImageLoader
                elementLoadingBackground="rgba(0, 0, 0, 0.4)"
                elementLoadingText="正在加载原图"
                height="600px"
                key={originImageUrl.value}
                onOriginLoad={originLoadEmit}
                originImageUrl={originImageUrl.value}
                showLoading={true}
                style={{ borderRadius: '24px' }}
                thumbnailImageUrl={thumbnailImageUrl.value}
                webpImageUrl={webpImageUrl.value}
                width="600px"
              />
            ) : (
              <div class={cx('flex-just-center', style['no-result'])}>
                <div>
                  <ElIcon size="100">
                    <Picture />
                  </ElIcon>
                  <div style={{ fontSize: '16px' }}>暂无图片</div>
                </div>
              </div>
            )}
            <div
              class="flex-direction h-full"
              style={{ justifyContent: 'space-between', marginLeft: '48px', flex: 1 }}>
              <div class="flex-just-end">
                <ElSpace size={12}>
                  <ElTooltip content="删除">
                    <ElButton
                      icon={
                        <ElIcon size={20}>
                          <Delete />
                        </ElIcon>
                      }
                      onClick={() => deletePrompt(id.value!)}
                      {...buttonStyle}
                    />
                  </ElTooltip>
                  <ElTooltip content="复制Prompt">
                    <ElButton
                      icon={
                        <ElIcon size={20}>
                          <CopyDocument />
                        </ElIcon>
                      }
                      onClick={copyPrompt}
                      {...buttonStyle}
                    />
                  </ElTooltip>
                  <ElTooltip content="下载图片">
                    <ElButton
                      icon={
                        <ElIcon size={20}>
                          <Download />
                        </ElIcon>
                      }
                      onClick={downLoad}
                      {...buttonStyle}
                    />
                  </ElTooltip>
                </ElSpace>
              </div>
              <div style={{ overflow: 'auto', margin: '20px 0', fontSize: '24px' }}>
                <p class="flex-just-start">“{props.prompt}”</p>
                <p class="flex-just-end" style={{ marginTop: '12px', fontSize: '16px' }}>
                  Design by {props.owner}
                </p>
              </div>
              <div class="flex-just-end">
                <ElButton onClick={close} type="primary">
                  关闭
                </ElButton>
              </div>
            </div>
          </div>
          <ElTooltip content="下一个（键盘 ->）">
            <ElButton
              disabled={!next.value}
              icon={
                <ElIcon size={16}>
                  <Right />
                </ElIcon>
              }
              link
              onClick={emitNext}
              type="primary"
            />
          </ElTooltip>
        </div>
      </ElDialog>
    )
  }
})
