import { ElSkeleton } from 'element-plus'
export default defineComponent({
  setup(_props, { slots }) {
    return () => (
      <>
        {slots.default?.()[0].children?.length ? (
          slots.default()
        ) : (
          <ElSkeleton
            animated
            rows={10}
            style={{ width: '624px', height: '412px', overflow: 'hidden' }}
          />
        )}
      </>
    )
  }
})
