import './style/index.less'
import App from './App.vue'
import pinia from '@/store/index'
import router from './router'
import vueQuery from './plugin/vue-query'
import directivePlugin from '@/directive'
import iconPlugin from '@/plugin/icon'
import zs from '@/plugin/zs'
import { webpInit, instalComponent } from '@znzt-fe/webp'
import '@/style/tailwind.css'
import VueVirtualScroller from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
async function bootstrap() {
  webpInit()
  zs.init()
  const app = createApp(App)
  app
    .use(directivePlugin)
    .use(iconPlugin)
    .use(pinia)
    .use(vueQuery)
    .use(router)
    .use(instalComponent)
    .use(VueVirtualScroller)
    .mount('#app')
}
// 域名调整
if (location.href.includes('openproxy')) {
  window.location.href = location.href.replace('openproxy', 'llmplatform')
} else {
  bootstrap()
}
/**
 *  备注：清除掉之前piana持久化在localstorage中的用户信息
 *  日期：2025-03-20
 */
window.localStorage.removeItem('user')