.app-layout {
  height: 100%;

  & > .el-main {
    padding: 0;
  }

  .app-header {
    background-color: white;
    overflow: auto;
    .left {
      min-width: 200px;
      cursor: pointer;

      img {
        width: 36px;
        height: 36px;
      }

      span {
        margin-left: 6px;
        color: rgb(51 51 51);
        font-weight: 600;
        line-height: 54px;
        font-size: 18px;
      }
    }

    .center {
      flex: 2;
      height: 100%;

      .app-menu {
        border: none;
        height: 100%;
        :global {
          .el-menu-item,
          .el-sub-menu {
            // width: 115px;
            height: 100%;
            text-align: center;
            background: #fff !important;
            border: none;

            .el-sub-menu__title {
              border: none;
            }

            &.is-active {
              color: #2372f6 !important;
              position: relative;

              .el-sub-menu__title {
                border: none;
                color: #2372f6 !important;
              }

              &::after {
                content: '';
                width: 15px;
                height: 2px;
                background: #2372f6;
                bottom: 0;
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
              }
            }
          }
        }
      }
    }

    .right {
      height: 100%;
      min-width: 200px;

      .username {
        margin: 0 8px;
      }

      :global(.el-dropdown) {
        height: 100%;
      }

      img {
        width: 24px;
        height: 24px;
      }
    }
  }

  :global(.el-footer) {
    overflow: hidden;
    height: 38px;
    background: white;
    line-height: 38px;
    box-shadow: chartreuse;
    border-top: 1px solid #e1e3e9;

    .summary-container {
      span {
        margin-right: 16px;
      }
    }
  }
}

.el-alert {
  margin-top: 8px;

  :global(.el-alert__description) {
    max-height: 320px;
    overflow: auto;
  }
}

:global(.el-dropdown__list) {
  max-height: 400px;
}
