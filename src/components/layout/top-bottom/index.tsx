import logo from '@/assets/logo.png'
import useUserStore from '@/store/user'
import $http from '@/api'
import {
  El<PERSON>lert,
  ElButton,
  ElContainer,
  ElDialog,
  ElDropdown,
  ElDropdownItem,
  ElDropdownMenu,
  ElFooter,
  ElHeader,
  ElIcon,
  ElInput,
  ElMenu,
  ElMenuItem,
  ElMessage,
  ElSubMenu
} from 'element-plus'
import CreateBusiness from '../../create-business/index.vue'
import SecretDialog from '../../secret-dialog/index.vue'
import AppMain from '../app-main.vue'
import style from './index.module.less'
import zs from '@/plugin/zs'
import { BusinessUserStatus, RoleId } from '@/api/user/type'
import ConfigSchedule from '@/components/config-sched/index.vue'
interface Route {
  name: string
  path: string
  show?: boolean
  link?: string
  children?: Route[]
}
export default defineComponent({
  name: 'top-bottom',
  setup() {
    const userStore = useUserStore()
    const { isBusAdmin, isSuperAdmin, isBusSuperAdmin } = storeToRefs(userStore)
    const { path } = toRefs(useRoute())
    const isTaskRouter = computed(() => path.value.includes('/task'))
    const isChatRouter = computed(() => path.value.includes('/chat'))
    const isMarkRouter = computed(() => path.value.includes('/mark'))
    const showDocument = computed(
      () => isTaskRouter.value || isChatRouter.value || isMarkRouter.value
    )
    const go2document = () => {
      let url = ''
      if (isChatRouter.value) {
        url = 'https://docs.zuoyebang.cc/doc/1833461086213218315?ddtab=true'
      }
      if (isTaskRouter.value) {
        url = 'https://docs.zuoyebang.cc/doc/1833461086213218310?ddtab=true'
      }
      if (isMarkRouter.value) {
        url = 'https://docs.zuoyebang.cc/doc/1833461086213218312?ddtab=true'
      }
      window.open(url)
    }
    const activeMenu = ref('')
    watchImmediate(
      () => [path.value],
      ([path]) => {
        let res: any = path || ''
        if (res && typeof res === 'string') {
          res = res.split('/')
        }
        const module = `/${res[1]}`
        activeMenu.value = module
      }
    )
    const state = reactive({
      show: false,
      showGuestDialog: false,
      applyReason: '',
      businessId: userStore.current.businessId,
      showSecret: false,
      businessName: '',
      configScheduleShow: false
    })
    const [hasLogin] = useToggle(true)
    // 无权限
    const noPermission = computed(() => {
      const current = userStore.current
      return userStore.isGuest && current.status !== 1
    })
    // 判断是否是审核中
    const isChecking = (status: number) => {
      return status === 1
    }
    // 审核通过
    const isPassed = (status: number) => {
      return status === 2 || status === 5
    }
    // 是否是公开业务线
    const isGuest = computed(() => {
      const current = userStore.current
      return current && current.businessId === 1
    })
    // 是否展示自定义模型列表
    const showCustomModelBusinessList = ['nlp', 'SpeechTech', 'rec_strategy']
    const showCustomModel = computed(() => {
      const current = userStore.current
      return current && showCustomModelBusinessList.includes(current.businessCode)
    })

    const showName = computed(() => {
      const current = userStore.current
      if (noPermission.value) {
        return `${userStore.zhName || ''}(暂无权限)`
      }
      if (!userStore.zhName) {
        return
      }
      // const statusName = current.status === 1 ? '审核中' : ''
      let res = `${userStore.zhName}-${current.businessName}${current.roleName}`
      if (isChecking(current.status)) {
        res += '(审核中)'
      }
      return res
    })
    const options = computed(() => {
      const businessList = userStore.businessList || []
      const current = userStore.current
      const res: Array<{ name: string; onClick: () => void; disabled?: boolean }> = [
        {
          name: '建立业务线',
          onClick: () => {
            state.businessId = 0
            state.show = true
          }
        }
      ]
      // 无权限
      if (noPermission.value) {
        res.push({
          name: '申请试用权限',
          onClick: () => {
            state.showGuestDialog = true
          }
        })
        return res
      }
      businessList.forEach((bus) => {
        const checking = isChecking(bus.status)
        const map = {
          [BusinessUserStatus.InReview]: '审核中'
          // 5: '修改中'
        }
        const statusText = bus.status in map ? map[bus.status as keyof typeof map] : ''
        const statusName = statusText ? `（${statusText}）` : ''
        const { businessName, roleName, businessId, businessCode } = bus
        const item = {
          name: `${businessName}${businessCode ? `(${businessCode})` : ''}${roleName}${statusName}`,
          disabled: checking,
          onClick: async () => {
            if (item.disabled) {
              return
            }
            if (businessId === current.businessId) {
              return
            }
            await router.push({
              path: '/home'
            })
            userStore.updateCurrent(bus)
          }
        }
        res.push(item)
      })
      return res
    })

    const modules = computed(() => {
      const current = userStore.current
      const passed = isPassed(current.status)
      const { labPermission = [] }: any = current
      const hasDefaultPermission = labPermission.includes('default')
      const hasChatPermission = hasDefaultPermission || labPermission.includes('chat')
      const hasMarkPermission = hasDefaultPermission || labPermission.includes('mark')
      const hasTaskPermission = hasDefaultPermission || labPermission.includes('task')
      const hasImagePermission = hasDefaultPermission || labPermission.includes('image')
      const res: Route[] = [
        {
          name: '系统管理',
          path: '/system',
          children: [
            {
              name: 'IP管理',
              path: '/system/ip'
            },
            {
              name: '业务线管理',
              path: '/system/business'
            },
            {
              name: 'IP厂商管理',
              path: '/system/factory'
            },
            {
              name: '用户查询',
              path: '/system/user'
            },
            {
              name: 'SK管理',
              path: '/system/sk'
            },
            {
              name: '模型管理',
              path: '/system/model'
            },
            {
              name: '调度套餐管理',
              path: '/system/scheduling-group-list'
            },
            {
              name: '账单管理',
              path: '/system/bill'
            },
            {
              name: 'ApiBatch任务管理',
              path: '/system/batch',
              show: isSuperAdmin.value
            }
          ],
          show: isSuperAdmin.value
        },
        {
          name: '业务管理',
          path: '/business',
          children: [
            {
              name: '用户管理',
              path: '/business/user'
            },
            {
              name: 'SK管理',
              path: '/business/sk'
            },
            {
              name: '修改业务线数据',
              path: '/edit',
              show: isBusAdmin.value
            },
            {
              name: '调度配置',
              path: '/schedule',
              show: isBusSuperAdmin.value
            },
            {
              name: '报警管理',
              path: '/business/alarm'
            },
            {
              name: '自建模型列表',
              path: '/business/modellist',
              show: showCustomModel.value
            },
            {
              name: '标注标准管理',
              path: '/business/markStandard',
              show: isBusAdmin.value
            },
            {
              name: '标注标签管理',
              path: '/business/markTag',
              show: isBusAdmin.value
            },
            {
              name: '标注任务类别标签管理',
              path: '/business/markCategory',
              show: isBusAdmin.value
            },
            {
              name: '标注任务模板管理',
              path: '/business/markTaskTemplate',
              show: isBusAdmin.value
            }
          ],
          show: isBusAdmin.value && passed
        },
        {
          name: '对话实验室',
          path: '/chat',
          children: [],
          show: passed && !!current.businessId && hasChatPermission
        },
        {
          name: '任务实验室',
          path: '/task',
          children: [],
          show: passed && !!current.businessId && hasTaskPermission
        },
        {
          name: '图像实验室',
          path: '/picture',
          children: [],
          show: passed && !!current.businessId && hasImagePermission
        },
        {
          name: '标注实验室',
          path: '/mark',
          children: [],
          show: passed && !!current.businessId && hasMarkPermission
        },
        {
          name: '文档',
          path: '/document',
          children: [
            {
              name: 'API文档',
              path: '/api',
              link: 'https://docs.zuoyebang.cc/doc/1833461086209024003?ddtab=true'
            },
            {
              name: 'FAQ',
              path: '/faq',
              link: 'https://docs.zuoyebang.cc/doc/1833461086209024002?ddtab=true'
            },
            {
              name: '说明文档',
              path: '/guide',
              link: 'https://docs.zuoyebang.cc/doc/1833461086213218314?ddtab=true'
            }
          ],
          show: true
        },
        {
          path: '/price',
          name: '模型价格',
          show: true
        },
        {
          name: 'Tiktokenizer工具',
          path: '/tiktokenizer',
          children: [],
          show: true,
          link: 'https://tiktokenizer.vercel.app/'
        }
      ]
      const showModules = res.filter((item) => item.show)
      showModules.forEach((module) => {
        const { children = [] } = module
        module.children = children.filter((sub) => sub.show !== false)
      })
      return showModules
    })

    const logout = async () => {
      const data = await $http.logout()
      window.location = data.logoutUrl
    }
    const router = useRouter()
    const jump = (item: any) => {
      const { path = '', link } = item
      // 外部链接跳转
      if (link) {
        if (link === 'https://tiktokenizer.vercel.app/') {
          zs.track('HO1_001')
        }
        window.open(link)
        return
      }
      // 编辑业务线弹窗
      if (path === '/edit') {
        state.businessId = userStore.current.businessId
        state.show = true
        return
      }
      if (path === '/schedule') {
        state.businessId = userStore.current.businessId
        state.businessName = userStore.current.businessName
        state.configScheduleShow = true
        return
      }
      // 路由跳转
      router.push({
        path
      })
    }
    const applyGuest = async () => {
      await $http.applySettle({
        roleId: RoleId.User,
        applyReason: state.applyReason
      })
      state.applyReason = ''
      state.showGuestDialog = false
      ElMessage.success({
        message: '请等待审核,刷新页面可查看审核进度',
        duration: 3000
      })
    }
    const showUserSk = () => {
      state.showSecret = true
    }
    const [fullScreen, setFullScreen] = useToggle(false)
    watchImmediate(path, (val) => {
      const reg = new RegExp('/mark/rate/')
      if (val.match(reg)) return setFullScreen(true)
      setFullScreen(false)
    })
    return () => (
      <>
        {fullScreen.value && <AppMain />}
        <ElContainer class={style['app-layout']} v-show={!fullScreen.value}>
          <ElHeader class={cx(style['app-header'], 'v-center', 'space-b')}>
            <section class={cx(style.left, 'v-center')} onClick={() => jump({ path: '/home' })}>
              <img src={logo} />
              <span>LLM应用平台</span>
            </section>
            <section class={style.center}>
              <ElMenu class={style['app-menu']} default-active={activeMenu.value} mode="horizontal">
                {...modules.value.map((item) =>
                  !item.children?.length ? (
                    <ElMenuItem
                      index={item.path}
                      key={item.path}
                      onClick={() => {
                        jump(item)
                      }}>
                      {item.name}
                    </ElMenuItem>
                  ) : (
                    <ElSubMenu index={item.path} key={item.path}>
                      {{
                        title: () => item.name,
                        default: () =>
                          item.children!.map((subMenu) => (
                            <ElMenuItem
                              index={subMenu.path}
                              key={subMenu.path}
                              onClick={() => jump(subMenu)}>
                              {subMenu.name}
                            </ElMenuItem>
                          ))
                      }}
                    </ElSubMenu>
                  )
                )}
              </ElMenu>
            </section>
            <section class={style.right}>
              {hasLogin.value && (
                <ElDropdown trigger="click">
                  {{
                    dropdown: () => (
                      <ElDropdownMenu>
                        {options.value.map((item) => (
                          <ElDropdownItem
                            disabled={item.disabled}
                            key={item.name}
                            onClick={item.onClick}>
                            {item.name}
                          </ElDropdownItem>
                        ))}
                      </ElDropdownMenu>
                    ),
                    default: () => (
                      <section class="v-center">
                        <span class={style.username}>{showName.value}</span>
                        <ElIcon class="el-icon--right">
                          <arrow-down />
                        </ElIcon>
                      </section>
                    )
                  }}
                </ElDropdown>
              )}
            </section>
          </ElHeader>
          <AppMain />
          <ElFooter class={'space-b'}>
            <section>
              {userStore.summary?.businessId && (
                <section class={style['summary-container']}>
                  {isGuest.value && (
                    <span>
                      <span>当前用户今日已用次数: {userStore.summary.userApiCntNow}</span>
                      <span>
                        当前用户每日最大调用次数:
                        {userStore.summary.userApiCntLimit > 0
                          ? userStore.summary.userApiCntLimit
                          : '无限制'}
                      </span>
                    </span>
                  )}
                </section>
              )}
              {isSuperAdmin.value && <section>平台总用户数: {userStore.userCount}</section>}
            </section>
            <section>
              <ElButton link onClick={showUserSk} type="primary">
                查看api key
              </ElButton>
              <ElButton link onClick={logout} type="primary">
                退出
              </ElButton>
              {showDocument.value && (
                <ElButton link onClick={go2document} type="primary">
                  说明文档
                </ElButton>
              )}
            </section>
          </ElFooter>
        </ElContainer>
        {state.show && (
          <CreateBusiness id={state.businessId} onClose={() => (state.show = false)} />
        )}
        {state.configScheduleShow && (
          <ConfigSchedule
            businessId={state.businessId}
            businessName={state.businessName}
            condfigType={2}
            {...{ onClose: () => (state.configScheduleShow = false) }}
          />
        )}
        <ElDialog
          close-on-click-modal={false}
          title="申请试用权限"
          v-model={state.showGuestDialog}
          width="720px">
          {{
            default: () => (
              <>
                <ElInput
                  placeholder="请输入申请原因"
                  type="textarea"
                  v-model={[state.applyReason, ['trim']]}
                />
                {userStore.adminList.length && (
                  <ElAlert
                    closable={false}
                    title="当前业务线管理员列表，如果您属于上面这些管理员的业务线，请联系管理员把您的账号添加进去即可"
                    type="success">
                    {userStore.adminList.join('、')}
                  </ElAlert>
                )}
              </>
            ),
            footer: () => (
              <span class="dialog-footer">
                <ElButton onClick={() => (state.showGuestDialog = false)}>取消</ElButton>
                <ElButton disabled={!state.applyReason} onClick={applyGuest} type="primary">
                  确定
                </ElButton>
              </span>
            )
          }}
        </ElDialog>
        {state.showSecret && <SecretDialog onClose={() => (state.showSecret = false)} />}
      </>
    )
  }
})
