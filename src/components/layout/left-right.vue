<template>
  <el-container class="app-layout">
    <el-aside width="250px">
      <section class="logo-container v-center" @click="jump({ path: '/home' })">
        <img :src="logo" />
        <span>LLM应用平台</span>
      </section>
      <el-menu class="app-menu" :default-active="activeMenu">
        <el-menu-item
          v-for="item in modules"
          :key="item.name"
          :index="item.path"
          @click="jump(item)"
          >{{ item.name }}</el-menu-item
        >
      </el-menu>
    </el-aside>
    <el-main>
      <el-container>
        <el-header class="app-header">
          <section class="right">
            <el-dropdown v-if="hasLogin" trigger="click">
              <section class="v-center">
                <img :src="state.iconUrl" width="24px" height="24px" class="user-avatar" />
                <span class="username">{{ state.username }}</span>
                <el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </section>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>
                    <span class="block" @click="logout">退出</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <div v-else class="login-tip" @click="login">登录</div>
          </section>
        </el-header>
        <app-main></app-main>
      </el-container>
    </el-main>
  </el-container>
</template>

<script lang="ts" setup>
import logo from '@/assets/logo.png'
import { login, logout } from '@/utils/sso'
import avatar from '@/assets/avatar.png'
import AppMain from './app-main.vue'
const activeMenu = computed(() => {
  const route = useRoute()
  const { meta, path } = route
  if (meta?.activeMenu) {
    return meta.activeMenu
  }
  return path
})
const hasLogin = ref(true)
const state = reactive({
  username: 'test-user',
  iconUrl: avatar
})
const modules = [
  {
    name: '首页',
    path: '/home'
  },
  {
    name: '列表',
    path: '/list'
  }
]
const router = useRouter()
const jump = (item: any) => {
  router.push({
    path: item.path
  })
}
</script>
<style scoped lang="less">
.app-layout {
  height: 100%;

  & > .el-main {
    padding: 0;
  }

  .el-aside {
    background-color: white;

    .el-menu {
      border-right: none;
    }

    .logo-container {
      height: 60px;
      cursor: pointer;

      img {
        width: 36px;
        height: 36px;
        margin: 0 8px;
      }
    }
  }

  .app-header {
    background-color: white;

    .left {
      min-width: 280px;

      span {
        margin-left: 6px;
        color: rgb(51 51 51);
        font-weight: 600;
        line-height: 54px;
        font-size: 18px;
      }
    }

    .right {
      height: 100%;
      float: right;

      .username {
        margin: 0 8px;
      }

      .el-dropdown {
        height: 100%;
      }

      img {
        width: 24px;
        height: 24px;
      }
    }
  }
}
</style>
