import { ElButton } from 'element-plus'
import style from './index.module.less'
export default defineComponent({
  emits: ['confirm', 'reset'],
  setup(_props, { emit }) {
    return () => (
      <div class={style.handleBtn}>
        <ElButton onClick={() => emit('reset')}>重置</ElButton>
        <ElButton onClick={() => emit('confirm')} type="primary">
          提交
        </ElButton>
      </div>
    )
  }
})
