import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import '@vueup/vue-quill/dist/vue-quill.bubble.css'
export default defineComponent({
  props: {
    modelValue: String,
    edit: Boolean,
    height: {
      type: String,
      default: 'unset'
    }
  },
  emits: ['update:modelValue', 'blur'],
  setup(props, { emit }) {
    const config = computed<typeof QuillEditor.props>(() => {
      return props.edit
        ? {
            theme: 'snow',
            readOnly: false
          }
        : {
            theme: 'bubble',
            readOnly: true
          }
    })
    const content = useModel(props, 'modelValue')
    const editor = ref<typeof QuillEditor>()
    whenever(
      () => !content.value,
      () => {
        editor.value?.setHTML('')
      }
    )
    return () => (
      <div
        class="w-full"
        style={{ border: !props.edit ? '1px solid #d1d5db' : 'unset', height: props.height }}>
        <QuillEditor
          contentType="html"
          onBlur={() => emit('blur')}
          ref={editor}
          toolbar={[
            ['bold', 'italic', 'underline', 'strike'], // toggled buttons
            ['blockquote', 'code-block'],
            [{ header: 1 }, { header: 2 }], // custom button values
            [{ list: 'ordered' }, { list: 'bullet' }],
            [{ script: 'sub' }, { script: 'super' }], // superscript/subscript
            [{ indent: '-1' }, { indent: '+1' }], // outdent/indent
            [{ direction: 'rtl' }], // text direction
            [{ size: ['small', false, 'large', 'huge'] }], // custom dropdown
            [{ header: [1, 2, 3, 4, 5, 6, false] }],
            [{ color: [] }, { background: [] }], // dropdown with defaults from theme
            [{ font: [] }],
            [{ align: [] }],
            ['clean']
          ]}
          v-model:content={content.value}
          {...config.value}
        />
      </div>
    )
  }
})
