import style from './index.module.less'

export type SortValue = '' | 'up' | 'down'
export default defineComponent({
  props: {
    modelValue: String as PropType<SortValue>
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { slots, emit }) {
    const active = useModel(props, 'modelValue')
    return () => (
      <div class="flex items-center">
        <div>{slots.default?.()}</div>
        <div class="relative h-[40px] ml-2">
          <div
            class={cx(style['arrowUp'], active.value === 'up' && style['arrowUpActive'])}
            onClick={() => {
              active.value === 'up' ? (active.value = '') : (active.value = 'up')
              emit('change', active.value)
            }}
          />
          <div
            class={cx(style['arrowDown'], active.value === 'down' && style['arrowDownActive'])}
            onClick={() => {
              active.value === 'down' ? (active.value = '') : (active.value = 'down')
              emit('change', active.value)
            }}
          />
        </div>
      </div>
    )
  }
})
