<template>
  <el-select placeholder="请选择调度套餐" v-model="selectedId" filterable class="max-w-56">
    <el-option v-for="item in list" :value="item.id" :label="item.name" :key="item.id" />
  </el-select>
</template>
<script lang="ts" setup>
import { useGetModelSchedList } from '@/api/modelSched'
import { GetModelSchedListParams } from '@/api/modelSched/type'

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0
  },
  preOptionsList: {
    type: Array<{ id: number; name: string }>,
    default: () => [
      {
        id: 0,
        name: '默认'
      }
    ]
  }
})
const emit = defineEmits(['update:modelValue', 'update:realDefaultId'])
const selectedId = useVModel(props, 'modelValue', emit)
const list = ref([...props.preOptionsList])
const { mutate: getListMuate } = useGetModelSchedList({
  onSuccess: (data) => {
    const pkList = data.list || []
    list.value = [...list.value, ...pkList]
    const target = list.value.find((item: any) => item.isDefault)
    const defaultSchedId = target ? target.id : 0
    emit('update:realDefaultId', defaultSchedId)
  }
})
const getList = async () => {
  const params: GetModelSchedListParams = {
    type: 2
  }
  await getListMuate(params)
}
getList()
</script>
