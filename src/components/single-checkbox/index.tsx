import { ElCheckbox, ElCheckboxGroup } from 'element-plus'
import { WritableComputedRef } from 'vue'

export default defineComponent({
  props: {
    modelValue: {
      type: [String, Boolean],
      default: ''
    },
    options: {
      type: Array as PropType<
        { id?: string | boolean; value?: string | boolean; name?: string; label?: string }[]
      >,
      default: () => []
    },
    default: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    const modelValue = useModel(props, 'modelValue')
    const checkIdList = computed({
      get: () => [modelValue.value],
      set: (val) => {
        if (val.length) {
          modelValue.value = val[val.length - 1]
          return
        }
        if (props.default) {
          modelValue.value = (props.options[0].value ?? props.options[0].id)!
          return
        }
        modelValue.value = ''
      }
    }) as WritableComputedRef<string[]>
    return () => {
      return (
        <ElCheckboxGroup
          disabled={props.disabled}
          onChange={() => emit('change')}
          v-model={checkIdList.value}>
          {props.options?.map((item) => (
            <ElCheckbox
              key={(item.id ?? item.value) + ''}
              size="small"
              value={item.id ?? item.value}>
              {item.label || item.name}
            </ElCheckbox>
          ))}
        </ElCheckboxGroup>
      )
    }
  }
})
