import useCommonStore from '@/store/common'
import { useRule } from '@znzt-fe/hooks'
import { ElForm, ElFormItem, ElInputNumber, ElTree } from 'element-plus'
import style from './index.module.less'
import { ModelItem, UpdateBusinessParams } from '@/api/business/type'
import { cloneDeep, isEmpty } from 'lodash-es'
import { AdminModelStatus } from '@/api/adminModel/type'
import { Tooltip } from '@znzt-fe/components'
export default defineComponent({
  props: {
    modelValue: {
      type: Object as PropType<UpdateBusinessParams>,
      default: () => ({})
    },
    view: {
      type: Boolean,
      default: false
    },
    diff: {
      type: Object || null,
      default: () => ({})
    }
  },
  emits: ['update:modelValue'],
  exposed: ['validate'],
  setup(props, { expose }) {
    const diff = toRef(props, 'diff')
    expose({
      validate: async () => {
        const result = await Promise.allSettled(
          [...formRef.value.values()].filter((item) => item).map((item) => item.validate())
        )
        return result.every((item) => item.status === 'fulfilled')
      }
    })
    const state = useModel(props, 'modelValue')
    const commonStore = useCommonStore()
    const modelOptions = computed(() =>
      (commonStore.config?.companyList || []).map((item) => {
        const disabled = item.modelList?.length
          ? item.modelList.every((model) => !!model.disabled)
          : true
        const modelList = item.modelList?.map(handleTreeModel)
        return {
          ...item,
          modelList,
          disabled
        }
      })
    )
    const handleTreeModel = (item: ModelItem): ModelItem => {
      return {
        ...item,
        disabled: item.status !== AdminModelStatus['Effective'],
        modelList: item.modelList?.map(handleTreeModel)
      }
    }
    const getModel = (model: string): any => {
      const res = state.value.config.find((item) => item.modelName === model)
      return res
    }
    const childModelList = computed(
      () =>
        modelOptions.value
          .map((item) => item.modelList)
          .flat(1)
          .map((item) => item.modelList)
          .flat(1)
          .map((item) => item?.name || '')
          .filter(Boolean) || []
    )
    const selectModels = computed(() => {
      const { config } = state.value
      const res = config
        .map((item) => item.modelName)
        .filter((item) => childModelList.value.includes(item))
      return res
    })
    const dealChange = ref(false)
    setTimeout(() => {
      dealChange.value = true
    }, 1000)
    const modelsChange = () => {
      if (!dealChange.value) {
        return
      }
      const checkedNodes = tree.value.getCheckedNodes(true)
      state.value.config = checkedNodes.map((item: any) => {
        const { name: model, limitConfig = {} } = item
        const models = state.value.config || []
        const data = models.find((item) => item.modelName === model)
        if (data) {
          const cloneData = cloneDeep(data)
          return cloneData
        }
        const obj = {
          modelName: model,
          ...limitConfig
        }
        const cloneObj = cloneDeep(obj)
        // 编辑和新建都需要设置为0
        if (cloneObj.ipm) cloneObj.ipm = 0
        if (cloneObj.qps) cloneObj.qps = 0
        if (cloneObj.rpm) cloneObj.rpm = 0
        if (cloneObj.tpm) cloneObj.tpm = 0
        return cloneObj
      })
    }
    const gptRules = useRule(
      {
        rpm: '请输入RPM',
        tpm: '请输入TPM',
        ipm: '请输入IPM'
      },
      { trigger: 'blur' }
    )
    const tree = ref()
    const formRef = ref(new Map<string, any>())
    const getDiffClass = (name: string, data: any, subName: string = '') => {
      // 一级节点判断
      if (data.modelList) {
        const hasItem = data.modelList.some((item: any) => props.diff[item.name])
        return hasItem ? style['diff-ui'] : ''
      }
      if (!subName) {
        const className = props.diff[name] ? style['diff-ui'] : ''
        return className
      }
      return props.diff[name] && props.diff[name].diff[subName] ? style['diff-ui'] : ''
    }

    const showTree = ref(false)
    watchDeep(
      diff,
      async () => {
        showTree.value = false
        await nextTick()
        showTree.value = true
      },
      { immediate: true }
    )
    const nodeCheck = (data: any, treeNode: any) => {
      const { disabled } = data
      const { isLeaf } = treeNode
      if (isLeaf && disabled) {
        tree.value.setChecked(data, false)
      }
    }
    const defaultExpandedKeys = computed(() => Object.keys(props.diff))
    return () =>
      showTree.value && (
        <ElTree
          class={style['tree']}
          data={modelOptions.value}
          defaultCheckedKeys={selectModels.value}
          defaultExpandedKeys={defaultExpandedKeys.value}
          nodeKey="name"
          onCheck-change={modelsChange}
          onNode-click={nodeCheck}
          props={{
            label: 'name',
            children: 'modelList',
            disabled: 'disabled'
          }}
          ref={tree}
          renderAfterExpand={false}
          renderContent={(
            _h: any,
            {
              data
            }: {
              data: { disabled: boolean; id: number; name: string; sort: number; limitConfig?: any }
            }
          ) => (
            <div>
              <div class={getDiffClass(data.name, data)}> {data.name}</div>
              {getModel(data.name) && !isEmpty(data.limitConfig) && (
                <ElForm
                  disabled={props.view}
                  model={getModel(data.name)}
                  ref={(ref) => formRef.value.set(data.name, ref)}
                  rules={gptRules}>
                  {Object.keys(data.limitConfig || {}).map((key: string) => (
                    <ElFormItem
                      class={(style['form-item'], getDiffClass(data.name, data, key))}
                      key={key}
                      prop={key}>
                      {{
                        label: () => (
                          <div class="flex items-center">
                            {key.toUpperCase()}
                            <Tooltip content="“0”表示不限制" icon="info" />
                          </div>
                        ),
                        default: () => (
                          <ElInputNumber
                            min={0}
                            precision={0}
                            v-model={getModel(data.name)![key]}
                          />
                        )
                      }}
                    </ElFormItem>
                  ))}
                </ElForm>
              )}
            </div>
          )}
          showCheckbox={true}
          style={{ width: '100%' }}
        />
      )
  }
})
