import { pick, isEmpty } from 'lodash-es'
const keys: Array<string> = [
  'name',
  'code',
  'budgetName',
  'devUname',
  'useScenario',
  'config',
  'retryInterval',
  'memberLimit',
  'modelLimitStatus',
  'dailyTokenLimit',
  'desc',
  'needAlarm'
]
const configKeys = ['modelName', 'ipm', 'rpm', 'tpm', 'qps']
const formatConfig = (config: any[]) => {
  return config.map((item: any) => {
    return pick(item, configKeys)
  })
}
export const useHook = (props: any, state: any) => {
  const diffItems: any = computed(() => {
    const pre = pick(state.preDetail, keys)
    const current = pick(state.detail, keys)
    pre.config = formatConfig(pre.config || [])
    current.config = formatConfig(current.config || [])
    let res: any[] = []
    if (pre && current) {
      res = jsonDiff(pre, current)
    }
    return res
  })
  const isDiffItem = (key: string) => {
    return props.showDiff && !isEmpty(diffItems.value[key])
  }
  const getItemDiff = (key: string) => {
    return diffItems.value[key]
  }
  return {
    diffItems,
    isDiffItem,
    getItemDiff
  }
}

const jsonDiff = (pre: any, current: any) => {
  const res: any = {}
  Object.keys(pre).forEach((key) => {
    const preValue = pre[key]
    const currentValue = current[key]
    if (key === 'config') {
      const diff = arrayDiff(preValue || [], currentValue || [], 'modelName')
      res.config = diff
      return
    }
    if (preValue !== currentValue) {
      res[key] = {
        old: preValue,
        new: currentValue
      }
    }
  })
  return res
}

const arrayDiff = (oldArray: any, newArray: any, key: any) => {
  const res: any = {}
  const oldMap = new Map()
  const newMap = new Map()

  // 将旧数组中的对象按照指定键值映射到 Map 中
  oldArray.forEach((item: any) => oldMap.set(item[key], item))

  // 将新数组中的对象按照指定键值映射到 Map 中
  newArray.forEach((item: any) => newMap.set(item[key], item))

  // 检查新数组中是否有旧数组中没有的项，即新增项
  newArray.forEach((item: any) => {
    const idKey = item[key]
    const oldItem = oldMap.get(idKey) || {}
    const diffProps = ['ipm', 'rpm', 'tpm', key]
    // 对比新旧项的属性
    Object.keys(item).forEach((prop: string) => {
      if (diffProps.includes(prop)) {
        if (!isEqual(oldItem[prop], item[prop])) {
          if (!res[idKey]) {
            res[idKey] = {
              type: 'update',
              diff: {}
            }
          }
          res[idKey].diff[prop] = {
            new: item[prop],
            old: oldItem[prop]
          }
        }
      }
    })
  })

  // 检查旧数组中是否有新数组中没有的项，即删除项
  oldArray.forEach((item: any) => {
    if (!newMap.has(item[key])) {
      res[item[key]] = {
        type: 'delete'
      }
    }
  })
  return res
}

// 一个简单的比较函数，根据对象的键值是否相同来判断对象是否相等
const isEqual = (val1: string, val2: string) => {
  return JSON.stringify(val1) === JSON.stringify(val2)
}
