<template>
  <el-dialog
    v-model="state.show"
    :title="props.view ? '查看业务线' : state.isEdit ? '编辑业务线' : '新建业务线'"
    width="720px"
    :close-on-click-modal="false"
    @close="close()">
    <el-form :disabled="props.view" :model="state.detail" :rules label-width="145px" ref="formRef">
      <el-form-item label="业务方" prop="name" :class="{ diff: isDiffItem('name') }">
        <el-input
          v-model.trim="state.detail.name"
          placeholder="业务方名称"
          clearable
          show-word-limit
          :maxlength="15"></el-input>
      </el-form-item>
      <el-form-item label="业务标识" prop="code" :class="{ diff: isDiffItem('code') }">
        <el-input
          v-model.trim="state.detail.code"
          placeholder="英文字母或者下划线"
          clearable
          show-word-limit
          :disabled="state.isEdit"
          :maxlength="15"></el-input>
      </el-form-item>
      <el-form-item label="预算单元" prop="budgetName" :class="{ diff: isDiffItem('budgetName') }">
        <el-select
          placeholder="请选择预算单元"
          v-loading="businessBudgetIsLoading"
          v-model="state.detail.budgetName"
          filterable>
          <el-option
            v-for="item in businessBudgetData?.list"
            :key="item.budgetName"
            :label="item.budgetName"
            :value="item.budgetName" />
        </el-select>
        <span v-if="isDiffItem('budgetName')" :style="{ 'margin-left': '10px' }">
          {{ state.preDetail.budgetName }}
        </span>
      </el-form-item>
      <el-form-item
        label="业务线负责人"
        prop="applyUname"
        :class="{ diff: isDiffItem('applyUname') }">
        <SearchUser v-model="state.detail.applyUname" />
      </el-form-item>
      <el-form-item label="研发对接人" prop="devUname" :class="{ diff: isDiffItem('devUname') }">
        <SearchUser v-model="state.detail.devUname" />
      </el-form-item>
      <el-form-item
        label="使用场景"
        prop="useScenario"
        :class="{ diff: isDiffItem('useScenario') }">
        <el-radio-group v-model="state.detail.useScenario" :disabled="canNotEditModels">
          <el-radio :value="Scenario.Online">在线</el-radio>
          <el-radio :value="Scenario.Offline">离线</el-radio>
          <el-radio :value="Scenario.Inside">内部</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="使用模型"
        prop="config"
        class="model-select"
        :class="{ diff: isDiffItem('config') }">
        <ModelSelect
          ref="modelRef"
          :view="props.view"
          v-model="state.detail"
          :diff="props.showDiff ? getItemDiff('config') : {}" />
        <el-button class="mt-2" @click="innerVisible = true">模型统一设置</el-button>
      </el-form-item>
      <el-form-item
        label="流式重试间隔(秒)"
        prop="retryInterval"
        :rules="retryIntervalRules"
        v-if="state.detail.useScenario === Scenario.Online"
        :class="{ diff: isDiffItem('retryInterval') }">
        <el-input-number
          v-model="state.detail.retryInterval"
          :precision="1"
          :step="0.1"
          :min="0"
          placeholder="流式重试间隔" />
      </el-form-item>
      <el-form-item
        label="组内人数上限"
        prop="memberLimit"
        :class="{ diff: isDiffItem('memberLimit') }">
        <el-input-number
          v-model="state.detail.memberLimit"
          :precision="0"
          :step="1"
          :min="1"
          placeholder="组内人数上限" />
      </el-form-item>
      <el-form-item
        label="是否限流"
        prop="modelLimitStatus"
        :class="{ diff: isDiffItem('modelLimitStatus') }">
        <el-switch v-model="state.detail.modelLimitStatus" :active-value="1" :inactive-value="2" />
      </el-form-item>
      <el-form-item label="是否报警" prop="needAlarm" :class="{ diff: isDiffItem('needAlarm') }">
        <el-switch v-model="state.detail.needAlarm" :active-value="1" :inactive-value="2" />
      </el-form-item>
      <el-form-item
        label="每日token上限"
        prop="dailyTokenLimit"
        :class="{ diff: isDiffItem('dailyTokenLimit') }">
        <el-input-number
          v-model="state.detail.dailyTokenLimit"
          :precision="0"
          :step="1"
          :min="0"
          placeholder="每日token上限" />
      </el-form-item>
      <el-form-item label="业务线描述" prop="desc" :class="{ diff: isDiffItem('desc') }">
        <el-input v-model="state.detail.desc" placeholder="业务线描述" type="textarea"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <template v-if="!props.view">
        <span class="dialog-footer">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="confirm" :disabled="state.disabled">确定</el-button>
        </span>
      </template>

      <template v-else>
        <el-button @click="close()">关闭</el-button>
      </template>
    </template>
    <el-dialog
      @open="initInnerForm"
      v-model="innerVisible"
      width="500"
      title="模型统一设置"
      append-to-body>
      <el-form>
        <el-form-item label="tpm">
          <el-input-number :precision="0" :min="0" v-model="innerForm.tpm"></el-input-number>
        </el-form-item>
        <el-form-item label="rpm">
          <el-input-number :precision="0" :min="0" v-model="innerForm.rpm"></el-input-number>
        </el-form-item>
        <el-form-item label="qps">
          <el-input-number :precision="0" :min="0" v-model="innerForm.qps"></el-input-number>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="innerVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmTpm">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script lang="ts" setup>
import $http from '@/api'
import { getBusinessBudget } from '@/api/business'
import { Scenario, UpdateBusinessParams } from '@/api/business/type'
import { RoleId } from '@/api/user/type'
import SearchUser from '@/components/search-user'
import useUserStore from '@/store/user'
import { useQuery } from '@znzt-fe/axios'
import { useRule } from '@znzt-fe/hooks'
import { resetObj } from '@znzt-fe/utils'
import { ElMessage } from 'element-plus'
import { isEmpty, isNumber, omit } from 'lodash-es'
import { useHook } from './hook'
import ModelSelect from './model-select'
const initInnerForm = () => {
  innerForm.tpm = undefined
  innerForm.rpm = undefined
  innerForm.qps = undefined
}
const innerForm = reactive({
  tpm: undefined,
  rpm: undefined,
  qps: undefined
})
const confirmTpm = () => {
  const { tpm, rpm, qps } = innerForm
  typeof tpm === 'number' && state.detail.config.forEach((item: any) => (item.tpm = tpm))
  typeof rpm === 'number' && state.detail.config.forEach((item: any) => (item.rpm = rpm))
  typeof qps === 'number' && state.detail.config.forEach((item: any) => (item.qps = qps))
  innerVisible.value = false
}
const innerVisible = ref(false)
const retryIntervalRules = [
  {
    required: true,
    message: '请输入流式重试间隔',
    trigger: 'blur'
  }
]
const { data: businessBudgetData, isLoading: businessBudgetIsLoading } = useQuery(
  ['BusinessBudget'],
  getBusinessBudget
)
const userStore = storeToRefs(useUserStore())
const props = defineProps({
  value: {
    type: Object,
    default: () => ({})
  },
  id: {
    type: Number
  },
  view: {
    type: Boolean,
    default: false
  },
  showDiff: {
    type: Boolean,
    default: false
  },
  onClose: {
    type: Function
  }
})
const isSuperAdmin = computed(() => {
  const current = userStore.current.value
  return (
    current && (current.roleId === RoleId.SuperAdmin || current.roleId === RoleId.BusSuperAdmin)
  )
})
// 编辑状态下非超管不可编辑模型数据
const canNotEditModels = computed(() => {
  return state.isEdit && !isSuperAdmin.value
})

const state: any = reactive({
  show: true,
  detail: {
    code: '',
    needAlarm: 1,
    businessId: -1,
    name: '',
    useScenario: Scenario.Unset,
    dailyTokenLimit: 0,
    memberLimit: 1,
    desc: '',
    config: [],
    retryInterval: 0,
    budgetName: '',
    devUname: '',
    applyUname: '',
    modelLimitStatus: 2
  } as UpdateBusinessParams,
  disabled: false,
  isEdit: false,
  preDetail: {} as { budgetName: string }
})

const { isDiffItem, getItemDiff } = useHook(props, state)

const rules = useRule(
  {
    name: '请输入函数名称',
    code: {
      validator: (rule: any, value: any, callback: any) => {
        const reg = /^[A-Za-z_]+$/
        if (!value) {
          callback(new Error('请输入业务标识'))
          return
        }
        const pass = reg.test(value)
        if (!pass) {
          callback(new Error('业务标识需要英文字母或者下划线！'))
          return
        }
        callback()
      }
    },
    memberLimit: '请输入组内人数上限',
    useScenario: {
      validator: (rule: any, value: any, callback: any) => {
        if (!value) {
          callback(new Error('请选择使用场景'))
          return
        }
        callback()
      },
      message: '请选择使用场景'
    },
    config: [
      { message: '请选择使用模型', required: true },
      {
        asyncValidator() {
          return new Promise((resolve, reject) => {
            modelRef.value.validate().then((res: boolean) => {
              res ? resolve() : reject('填写数据不完整')
            })
          })
        }
      }
    ],
    budgetName: { message: '请选择预算单元', trigger: 'change' },
    devUname: '请选择研发对接人',
    applyUname: '请选择业务线负责人'
  },
  {
    trigger: 'blur'
  }
)
watch(
  () => state.detail.useScenario,
  (val: Scenario) => {
    // 在线场景默认重试间隔为3s
    if (val === Scenario.Online) {
      const { retryInterval } = state.detail
      state.detail.retryInterval = isNumber(retryInterval) ? retryInterval : 3.0
    }
  }
)
const emits = defineEmits(['close'])
const formRef = ref<any>()
const modelRef = ref<any>()
const confirm = async () => {
  const form = formRef.value
  const valid = await form.validate().catch(() => false)
  const modelTarget = await modelRef.value.validate().catch(() => false)
  if (!modelTarget) {
    return
  }

  if (valid) {
    let params: any = {
      ...state.detail
    }
    let action: typeof $http.createBusiness | typeof $http.updateBusiness = $http.createBusiness
    if (state.isEdit) {
      params.businessId = props.id || props.value.businessId
      action = $http.updateBusiness
    }
    state.disabled = true
    if (params.useScenario !== Scenario.Online) {
      params = omit(params, ['retryInterval'])
    }
    await action(params).finally(() => {
      state.disabled = false
    })
    ElMessage.success({
      message: '请等待审核。刷新网页可查看审核进度',
      duration: 3500
    })
    close(true)
  }
}
const close = (refresh = false) => {
  state.show = false
  emits('close', refresh)
}
const getDetail = async () => {
  if (props.id) {
    state.isEdit = true
    const data = await $http.getBusinessDetail({ businessId: props.id })
    const omitData = omit(data, ['jsonRetryInterval'])
    resetObj(state.detail, omitData)
  } else if (!isEmpty(props.value)) {
    state.isEdit = true
    const omitData = omit(
      {
        ...props.value
      },
      ['jsonRetryInterval']
    )
    Object.assign(state.detail, omitData)
  }
  if (props.showDiff) {
    const data = await $http.getBusinessDetail({ businessId: props.value.businessId })
    state.preDetail = {
      ...data
    }
  }
}
getDetail()
invoke(async () => {
  await until(userStore.name).toBeTruthy()
  if (!state.isEdit) {
    state.detail.applyUname = userStore.name.value
  }
})
</script>

<style lang="less">
.el-dialog__body {
  max-height: 1280px;
  overflow: auto;

  // .el-input-number {
  //   width: 240px;
  // }

  .el-form-item {
    margin-bottom: 4px;

    &.diff {
      border: 1px solid rgba(233, 43, 23, 0.5);
      // background-color: rgba(233, 43, 23, 0.5);
    }

    padding: 3px 12px;

    .el-form-item__content {
      .el-form-item__error {
        position: inherit;
        top: auto;
        left: 6px;
      }
    }
  }
}

.model-select {
  .el-checkbox-group {
    display: flex;
    flex-direction: column;
  }

  .el-row {
    z-index: 1000;
    font-size: 16px;

    .extend-params {
      padding: 0 0 8px 16px;

      .el-form-item {
        margin-bottom: 4px;

        .el-form-item__content {
          .el-form-item__error {
            position: inherit;
            top: auto;
            left: 6px;
          }
        }
      }
    }
  }
}

.disabled-status {
  .el-checkbox__label {
    opacity: 0.5;
  }
}
</style>
