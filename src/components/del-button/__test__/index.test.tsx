import { describe, it, expect, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import DelButton from '@/components/del-button'
import { ElButton } from 'element-plus'
import { rAF } from '@/test'

const selector = '.el-overlay'
describe('confirm-button', () => {
  it('create', () => {
    const wrapper = mount({
      setup: () => () => <DelButton />
    })
    const ElButtonWrap = wrapper.getComponent(ElButton)
    const DelButtonWrap = wrapper.getComponent(DelButton)
    expect(wrapper.findComponent(ElButton).exists()).toBeTruthy()
    expect(DelButtonWrap.props('size')).toBe('default')
    expect(ElButtonWrap.props('size')).toBe('default')
  })
  describe('click', () => {
    afterEach(async () => {
      document.body.innerHTML = ''
      await rAF()
    })
    it('show MessageBox', async () => {
      const wrapper = mount({
        setup: () => () => <DelButton />
      })
      const ElButtonWrap = wrapper.getComponent(ElButton)
      await ElButtonWrap.trigger('click')
      await rAF()
      expect(document.querySelector(selector)).toBeDefined()
    })
    it('confirm', async () => {
      const wrapper = mount({
        setup: () => () => <DelButton />
      })
      const ElButtonWrap = wrapper.getComponent(ElButton)
      const DelButtonWrap = wrapper.getComponent(DelButton)
      await ElButtonWrap.trigger('click')
      await rAF()
      const btn = document
        .querySelector(selector)!
        .querySelector('.el-button--primary') as HTMLButtonElement
      btn.click()
      await rAF()
      expect(DelButtonWrap.emitted().click).toBeDefined()
    })
    it('cancel', async () => {
      const wrapper = mount({
        setup: () => () => <DelButton />
      })
      const ElButtonWrap = wrapper.getComponent(ElButton)
      const DelButtonWrap = wrapper.getComponent(DelButton)
      await ElButtonWrap.trigger('click')
      await rAF()
      const btn = document.querySelector('.el-message-box__close') as HTMLButtonElement
      btn.click()
      await rAF()
      expect(DelButtonWrap.emitted().click).toBeFalsy()
    })
  })
})
