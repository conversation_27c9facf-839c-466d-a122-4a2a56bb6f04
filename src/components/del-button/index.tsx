import ConfirmButton from '@/components/confirm-button'
import { ButtonInstance } from 'element-plus'
export default defineComponent({
  emits: ['click'],
  props: {
    confirmText: {
      type: String,
      default: '确认删除?'
    },
    size: {
      type: String as PropType<ButtonInstance['size']>,
      default: 'default'
    }
  },
  setup(props, { emit, slots }) {
    const { confirmText, size } = toRefs(props)
    return () => (
      <ConfirmButton
        confirmText={confirmText.value}
        onClick={() => emit('click')}
        size={size.value}>
        {slots.default ? slots.default() : '删除'}
      </ConfirmButton>
    )
  }
})
