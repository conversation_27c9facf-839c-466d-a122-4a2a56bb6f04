import { ElPopover } from 'element-plus'
import { TableDataProps } from '../table'
const tableColumnClass = 'shrink-0 px-1 h-full'
const textEllipsisClass = 'overflow-hidden text-ellipsis text-nowrap h-full'
export default defineComponent({
  props: {
    prop: {
      type: String,
      required: true
    },
    data: {
      type: [Object, String, Number]
    },
    toolTip: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const { data, toolTip: propToolTip, prop } = toRefs(props)
    const tableData = inject('tableData') as Ref<TableDataProps[]>
    const tooltipInject = inject('tooltip') as Ref<TableDataProps[]>
    const toolTip = computed(() => propToolTip.value || tooltipInject.value)
    const tableContent = computed(() => tableData.value.find((item) => item.prop === prop.value))
    const tableWidth = computed(() => tableContent.value!.width)
    const tableSlots = tableContent.value!.slots!
    const elRef = ref()
    watch(data, () => calculateEl())
    const popoverDisabled = ref(false)
    const calculateEl = () => {
      const element = elRef.value
      popoverDisabled.value = element?.scrollWidth > element?.offsetWidth ? false : true
    }
    onMounted(() => calculateEl())

    return () => (
      <div class={cx(tableColumnClass, 'box-border')} style={{ width: tableWidth.value + 'px' }}>
        {!toolTip.value ? (
          <div class={textEllipsisClass}>{tableSlots(data.value)}</div>
        ) : (
          <ElPopover
            disabled={popoverDisabled.value}
            effect="dark"
            persistent={false}
            placement="top"
            popperStyle={{ width: 'auto' }}>
            {{
              reference: () => (
                <div class={textEllipsisClass} ref={elRef}>
                  {tableSlots(data.value)}
                </div>
              ),
              default: () => <>{tableSlots(data.value)}</>
            }}
          </ElPopover>
        )}
      </div>
    )
  }
})
