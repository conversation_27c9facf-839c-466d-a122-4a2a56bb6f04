import style from './index.module.less'
import { <PERSON><PERSON>utton, ElIcon } from 'element-plus'
import { ArrowDownBold, ArrowLeftBold } from '@element-plus/icons-vue'
import CollapseTransition from '@/components/collapse-transition'
export default defineComponent({
  emits: ['reset'],
  setup(_props, { slots, emit }) {
    const [showExtra, setShowExtra] = useToggle(false)
    const reset = () => emit('reset')
    return () =>
      !slots.default ? (
        ''
      ) : (
        <section class={style['search-form']}>
          <div class="w-full">
            <div class={style['main']}>
              <div class="grow">{slots.default()}</div>
              {slots.extra && (
                <div>
                  <ElButton link onClick={reset} type="primary">
                    重置
                  </ElButton>
                  <ElButton link onClick={() => setShowExtra()} type="primary">
                    展开
                    <ElIcon class="el-icon--right">
                      {showExtra.value ? <ArrowDownBold /> : <ArrowLeftBold />}
                    </ElIcon>
                  </ElButton>
                </div>
              )}
            </div>
            <CollapseTransition style={{ marginTop: '8px' }}>
              {showExtra.value && <div>{slots.extra?.()}</div>}
            </CollapseTransition>
          </div>
        </section>
      )
  }
})
