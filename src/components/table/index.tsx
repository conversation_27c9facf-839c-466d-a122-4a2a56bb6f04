import { PropType } from 'vue'
import { JSX } from 'vue/jsx-runtime'

export interface TableDataProps {
  prop: string
  label: string
  class: string
  width: number
  rate?: number
  slots: (data: any) => JSX.Element | string | number
  header?: () => JSX.Element
}

export default defineComponent({
  props: {
    tableData: {
      type: Array as PropType<TableDataProps[]>,
      default: () => []
    },
    width: {
      type: Number
    },
    tooltip: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { slots }) {
    const { tableData, width, tooltip } = toRefs(props)
    provide('tableData', tableData)
    provide('tooltip', tooltip)
    const TableHeader = () => (
      <div
        class="flex ml-4 h-[40px] leading-[40px] items-center relative"
        style={{ width: width.value + 'px' }}>
        {tableData.value.map((item) => (
          <div
            class={cx('shrink-0', item.class)}
            key={item.label}
            style={{ width: item.width + 'px' }}>
            {item.header?.() || item.label}
          </div>
        ))}
      </div>
    )
    return () => (
      <div class="w-full flex flex-col overflow-auto h-full">
        <TableHeader />
        <div class="grow overflow-auto" style={{ width: width.value! + 16 + 'px' }}>
          {slots.default?.()}
        </div>
      </div>
    )
  }
})
