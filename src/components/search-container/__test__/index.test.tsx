import { describe, expect, it } from 'vitest'
import SearchContainer from '@/components/search-container'
import { mount } from '@vue/test-utils'
import { ElButton } from 'element-plus'
describe('SearchContainer', () => {
  describe('create', () => {
    it('create with slots', () => {
      const wrapper = mount({
        setup: () => () =>
          (
            <SearchContainer>
              <ElButton>测试</ElButton>
            </SearchContainer>
          )
      })
      expect(wrapper.findComponent(ElButton).exists()).toBeDefined()
      expect(wrapper.find('section').exists()).toBeTruthy()
    })
    it('create without slots', () => {
      const wrapper = mount({
        setup: () => () => <SearchContainer />
      })
      expect(wrapper.find('section').exists()).toBeFalsy()
    })
  })
})
