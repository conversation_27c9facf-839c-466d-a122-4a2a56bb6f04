<template>
  <el-dialog v-model="state.show" title="用户信息" width="800px" @close="close()">
    <div>
      这是您的个人API Key，线上服务建议使用<el-button type="primary" link @click="openServiceDocumentLink">服务账号</el-button>
    </div>
    <div v-if="userStore.current?.businessCode">api key：{{ apiKey }}</div>
    <div>API接入说明：<el-button type="primary" link @click="docLick">文档地址</el-button></div>
    <div :style="{ marginTop: '10px' }" class="markdown-container">
      <SuspenseTag>
        <AssistantMessage :line-height="1.0" :text="mdText" />
      </SuspenseTag>
    </div>
    <template #footer>
      <el-button type="primary" @click="copy(1)">复制key</el-button>
      <el-button v-if="userStore.current?.businessCode" type="primary" @click="copy(2)">复制代码</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import useUserStore from '@/store/user'
import { copyText } from '@znzt-fe/utils'
import { Suspense as SuspenseTag } from '@znzt-fe/components'
import { getUserSecret } from '@/api/user'
import copyAction from 'copy-to-clipboard'
const AssistantMessage = defineAsyncComponent(() => import('@/views/mark/rate/assistantMessage'))
const userStore = useUserStore()
const state: any = reactive({
  show: true
})

defineProps({
  onClose: Function
})
const bashCodeArray = computed(() => {
  const bashCode = [
    `API_HOST="https://openproxy-cn.zuoyebang.cc"\nAPI_KEY="${apiKey.value}"` +
    '\ncurl "${API_HOST}/openproxy/rp/v1/chat/completions"',
    // 'curl "${API_HOST}/openproxy/rp/v1/chat/completions"',
    '-H "Content-Type: application/json"',
    '-H "Authorization: Bearer ${API_KEY}"',
    `-d '{
      "model": "ERNIE-Speed-128K",
      "messages":[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Say Hi"}
      ],
      "stream": false
    }'`
  ]
  debugger
  return bashCode
})

const secret = ref('')
const { mutate: getUserSecretMutation } = getUserSecret({
  onSuccess: (res: any) => {
    secret.value = (Array.isArray(res) && res.length) ? res[0].secretKey : ''
  }
})
const mdText = computed(
  () => `\`\`\`bash
${bashCodeArray.value.join(' \\\n')}
\`\`\``
)
if (userStore.current?.businessCode) {
  getUserSecretMutation({
    businessCode: userStore.current.businessCode,
    uname: userStore.name
  })
}
const apiKey = computed(() =>{
  const res = userStore.current?.businessCode ? secret.value : ''
  return res
})
  
const emits = defineEmits(['close'])
const docLick = () => window.open('https://docs.zuoyebang.cc/doc/1833461086209024003?ddtab=true')
const openServiceDocumentLink = () =>
  window.open(
    'https://docs.zuoyebang.cc/doc/1833461086209024003?smParams=2A1umCcVDcaG5bQjFPkd43e1VbvZ5vvP9vDTiyq18FuVFsujzPVkTq0GaXTDGjGQk2vrTLTfgMAnqaubCicAdn5HWSmz'
  )
const copy = (type = 1) => {
  if (type === 1) {
    copyText(apiKey.value)
  }
  if (type === 2) {
    const code = bashCodeArray.value.join(' \\\n')
    copyAction(code)
  }
  ElMessage.success('复制成功')
}
const close = (refresh = false) => {
  state.show = false
  emits('close', refresh)
}
</script>

<style scoped lang="less">
.markdown-container {
  :deep(.code-block-header__copy) {
    display: none;
  }

  :deep(.code-block-body) {
    margin: 8px 12px;
  }
}
</style>
