<template>
  <section class="flex flex-col">
    <section v-for="(srv, index) in handsrvList" :key="srv.handSrv + index + srv.id">
      <config-item
        v-model="handsrvList[index]"
        :modelId="props.value.modelId"
        :disabled="true"></config-item>
    </section>
  </section>
</template>
<script setup lang="ts">
import { useGetModelSchedDetail } from '@/api/modelSched'
import { GetModelSchedHandsrvsParams } from '@/api/modelSched/type'
import ConfigItem from './config-item.vue'

const props = defineProps({
  value: {
    type: Object,
    default: () => ({})
  },
  schedId: {
    type: Number,
    defualt: 0
  }
})

const configs: Ref<Array<GetModelSchedHandsrvsParams>> = ref([])
const handsrvList = computed(() => {
  const target: any =
    configs.value.find(
      (item: GetModelSchedHandsrvsParams) => item.modelId === props.value.modelId
    ) || {}
  return target.handSrvs || []
})
const { mutate: getScheduleDetail } = useGetModelSchedDetail({
  onSuccess: (data) => {
    configs.value = data.configs || []
  }
})
watch(
  () => props.schedId,
  (id: any) => {
    if (id) {
      getScheduleDetail({ id })
    } else {
      configs.value = []
    }
  },
  {
    immediate: true
  }
)
</script>
