<template>
  <section class="custom-config">
    <section class="clone-container" :gutter="12" ref="cloneRef">
      <section v-for="option in handsrvList" :key="option.value" class="cursor-move handle">
        {{ getHandsrvName<PERSON>y<PERSON>ey(option.key) }}
      </section>
    </section>
    <ModelChannel :current="props.value.modelId"></ModelChannel>
    <section ref="targetRef" class="target-container">
      <section v-for="(srv, index) in list" :key="srv.handSrv + index + srv.id">
        <config-item
          v-model="list[index]"
          :modelId="props.value.modelId"
          @remove="
            () => {
              remove(index)
            }
          "></config-item>
      </section>
    </section>
  </section>
</template>

<script lang="ts" setup>
import { v4 as uuidv4 } from 'uuid'
import { useDraggable } from 'vue-draggable-plus'
import { getHandsrvName<PERSON>y<PERSON><PERSON>, getModelInfoById, isGroupTab } from '../store'
import ConfigItem from './config-item.vue'
import ModelChannel from './model-channel.vue'

/** props 定义 */
const props = defineProps({
  value: {
    type: Object,
    default: () => ({})
  },
  modelId: {
    type: Number,
    default: 0
  }
})
const list: any = computed({
  get() {
    return props.value.handSrvs || []
  },
  set(val: any) {
    props.value.handSrvs = [...val]
  }
})
const remove = (index: number) => {
  list.value.splice(index, 1)
}

/** 拖拽相关 */
const cloneRef = ref()
const targetRef = ref()
const handsrvList: any = computed(() => {
  const modelId = props.value.modelId
  const isGroupType = isGroupTab(modelId)
  const target: any = getModelInfoById(modelId) || {
    handSrvs: [],
    parent: {}
  }
  if (isGroupType) {
    return target.handSrvs || []
  } else {
    const { parent = {} }: any = target
    return parent.handSrvs || []
  }
})
let timer: any = null
const isCustom = computed(() => props.value.useModelSchedPackageId === -1)
watch(
  () => handsrvList.value,
  (handsrvList) => {
    if (handsrvList.length) {
      timer = setTimeout(() => {
        if (isCustom.value) {
          initDrag()
        }
      }, 16 * 10)
    }
  },
  {
    deep: true,
    immediate: true
  }
)
onUnmounted(() => {
  clearTimeout(timer)
})
const initDrag = () => {
  useDraggable(cloneRef, handsrvList, {
    animation: 150,
    group: {
      name: 'people',
      pull: 'clone',
      put: false
    },
    handle: '.handle',
    sort: false,
    clone(element: any) {
      const cloneData = {
        handSrv: element.key,
        rate: 100,
        id: uuidv4(),
        privatePoolCode: ''
      }
      return cloneData
    }
  })
  useDraggable(targetRef, list, {
    animation: 150,
    group: 'people',
    handle: '.handle'
  })
}
</script>
<style lang="less">
.custom-config {
  display: flex;
  flex-direction: column;

  .clone-container {
    display: flex;

    section {
      margin: 0 6px;
      border: 1px solid #e1e3e9;
      flex: 1;
      padding: 8px 12px;
      text-align: center;
      cursor: move;
      font-size: 15px;
      font-weight: 500;
    }
  }

  .target-container {
    min-height: 350px;
    // overflow: auto;
    margin-top: 16px;

    &:empty {
      display: flex;
      justify-content: center;
      align-items: center;

      &::before {
        content: '\53ef\62d6\62fd\533a\57df';
        color: #a1a5af;
        font-size: 18px;
        font-weight: 500;
      }
    }
  }
}
</style>
