<template>
  <div class="collapse-container">
    <el-drawer
      v-model="drawer"
      title="跨模型调度"
      :modal="false"
      class="drawer-container"
      width="100%">
      <section v-for="group in groups" :key="group" class="group-container">
        <p>{{ group.name }}</p>
        <section ref="cloneRef">
          <VueDraggable
            v-model="group.handSrvs"
            :animation="150"
            ghostClass="ghost"
            :group="{ name: 'people', pull: 'clone', put: false }"
            :clone="
              (el) => {
                return clone(el, group)
              }
            "
            :sort="false">
            <span
              v-for="option in group.handSrvs"
              :value="option.key"
              :key="option.key"
              class="cursor-move handle">
              {{ option.name }}
            </span>
          </VueDraggable>
        </section>
      </section>
    </el-drawer>
    <section class="cross-model">
      <el-button @click="drawer = true" type="primary" text>
        跨模型调度
        <el-icon class="el-icon--right">
          <ArrowRight />
        </el-icon>
      </el-button>
    </section>
  </div>
</template>

<script lang="ts" setup>
import { ModelGroupItem } from '@/api/modelSched/type'
import { ArrowRight } from '@element-plus/icons-vue'
import { v4 as uuidv4 } from 'uuid'
import { VueDraggable } from 'vue-draggable-plus'
import { getModelSceneById, useStore } from '../store'
const cloneRef = ref()
const drawer = ref(false)
const props: any = defineProps({
  current: {
    type: Number,
    default: 0
  }
})
const { modelList } = useStore()

const clone = (element: any, group: ModelGroupItem) => {
  const res = {
    handSrv: element.key,
    rate: 100,
    id: uuidv4(),
    privatePoolCode: '',
    changeModelId: group.id,
    changeModelConfig: [
      {
        pre: '',
        after: ''
      }
    ]
  }
  return res
}

const groups = computed(() => {
  const models: any = modelList.value || []
  const scene = getModelSceneById(props.current)
  const res = models.filter((item: ModelGroupItem) => item.scene === scene)
  return res || []
})
</script>

<style lang="less" scoped>
.collapse-container {
  & > :deep(div) {
    top: 0 !important;
    bottom: 0;
    width: 300px;
    right: 0 !important;
    left: initial !important;
  }

  :deep(.drawer-container) {
    width: 100% !important;

    .el-drawer__header {
      margin-bottom: 0;
    }

    .group-container {
      padding-bottom: 12px;

      p {
        font-weight: bold;
        font-size: 14px;
      }

      span {
        margin-right: 8px;
        margin-top: 8px;
        border: 1px solid #e1e3e9;
        padding: 4px 8px;
        text-align: center;
        cursor: move;
        font-size: 13px;
        font-weight: 400;
        min-width: 72px;
        display: inline-block;
        box-sizing: border-box;
      }
    }
  }

  .cross-model {
    text-align: right;

    .el-button {
      margin-right: 0px;
      margin-top: 5px;
      padding: 0 5px;
    }
  }
}
</style>
