<template>
  <el-card shadow="hover">
    <el-tag
      disable-transitions
      v-if="srv.changeModelId"
      :type="getModelNameById(srv.changeModelId) ? 'success' : 'danger'">
      {{
        getModelNameById(srv.changeModelId)
          ? getModelNameById(srv.changeModelId)
          : `无权限(模型id:${srv.changeModelId})`
      }}
    </el-tag>
    <section class="content">
      <section class="main">
        <span class="name">
          {{ getOptionByKey(srv.handSrv) }}
        </span>
        <span class="rate">流量占比（{{ srv.rate }}%）</span>
        <el-slider v-model="srv.rate" :min="0" :disabled="props.disabled" />
      </section>
      <el-select
        placeholder="请选择私有池"
        v-model="srv.privatePoolCode"
        :disabled="props.disabled">
        <el-option
          v-for="item in privatePools"
          :value="item.code"
          :label="item.name"
          :key="item.name" />
      </el-select>
      <div :style="{ marginLeft: '6px' }" v-if="!props.disabled">
        <el-button link type="primary" text class="handle">
          <el-icon>
            <Rank />
          </el-icon>
        </el-button>
        <el-button link type="danger" text @click="emit('remove')">
          <el-icon>
            <Delete />
          </el-icon>
        </el-button>
      </div>
    </section>
    <ModelMap
      v-if="srv.changeModelConfig && srv.changeModelConfig.length"
      v-model="srv.changeModelConfig"
      :disabled="props.disabled"
      :preList="getModelVersions(props.modelId)"
      :afterList="getModelVersions(srv.changeModelId)"></ModelMap>
  </el-card>
</template>
<script lang="ts" setup>
import ModelMap from "./model-map.vue";
import { ModelGroupItem } from "@/api/modelSched/type";
import { get } from "lodash-es";
import { useGetModelSchedPools } from "@/api/modelSched";
import { useStore, getModelVersions, getModelNameById } from "../store";
import { PrivatePoolItemBase } from "@/api/modelSched/type";

const props = defineProps({
	modelValue: {
		type: Object,
		default: () => ({
			handSrv: "",
			privatePoolCode: "",
			rate: "",
		}),
	},
	modelList: {
		type: Array<ModelGroupItem>,
		default: () => [],
	},
	modelId: {
		type: Number,
		default: 0,
	},
	disabled: {
		type: Boolean,
		default: false,
	},
});
const businessId: number | undefined = inject("businessId");
const { privatePoolMap, updatePrivatePoolMap, handsrvMap } = useStore();
const emit = defineEmits(["update:modelValue", "remove"]);
const srv = useVModel(props, "modelValue", emit);
const { mutate: getPrivatePoolMutate } = useGetModelSchedPools({
	onSuccess: (res, params) => {
		const { pools = {} } = res;
		const key: any = `${businessId}-${params.modelId}`;
		updatePrivatePoolMap(key, pools);
		setDefaultPrivatePoolCode();
	},
});

// 获取model组下的pools枚举值
const getPrivatePool = (modelId: number, key: number) => {
	const id = `${businessId}-${modelId}`;
	const cache = privatePoolMap.value.has(id);
	if (cache) {
		const config = privatePoolMap.value.get(id);
		return get(config, key) || [];
	}
	getPrivatePoolMutate({
		modelId,
		businessId,
	});
	return [];
};
const privatePools = computed(() => {
	const modelId = srv.value.changeModelId || props.modelId;
	const privatePools = getPrivatePool(modelId, srv.value.handSrv);
	return privatePools;
});
const setDefaultPrivatePoolCode = () => {
	if (!srv.value.privatePoolCode) {
		srv.value.privatePoolCode =
			privatePools.value?.find((item: PrivatePoolItemBase) => item.isDefault)
				?.code || "";
	}
};
setDefaultPrivatePoolCode();
const getOptionByKey = (key: string) => {
	const targetMap = handsrvMap.value;
	return targetMap.get(key);
};
</script>
<style lang="less" scoped>
.el-card {
  margin: 8px 0;
  position: relative;

  .el-card__body {
    position: relative;
    padding: 0;

    .el-tag {
      position: absolute;
      left: 0;
      top: 0;
    }
  }

  .content {
    display: flex;
    align-items: center;
    position: relative;

    section.main {
      flex: 1;
      display: flex;
      justify-content: center;
      line-height: 32px;
      margin-right: 32px;

      .name {
        display: inline-block;
        font-size: 14px;
        min-width: 120px;
        font-weight: 500;
      }

      .rate {
        flex-grow: 1;
        margin-left: 16px;
        margin-right: 8px;
        flex-shrink: 0;
      }
    }

    .el-select {
      width: 180px;
    }
  }
}
</style>
