<template>
  <el-collapse class="model-map">
    <el-collapse-item title="模型映射">
      <section v-for="(item, index) in props.modelValue" :key="index" class="model-map-item">
        <section>
          <span>From</span>
          <el-select v-model="item.pre" placeholder="请选择" clearable :disabled="props.disabled">
            <el-option
              v-for="option in props.preList"
              :key="option.id"
              :label="option.name"
              :value="option.id"
              :disabled="getDisabled(option, item.pre)" />
          </el-select>
        </section>
        <section>
          <span>To</span>
          <el-select v-model="item.after" placeholder="请选择" clearable :disabled="props.disabled">
            <el-option
              :disabled="item.status !== AdminModelStatus['Effective']"
              v-for="item in props.afterList"
              :key="item.id"
              :label="item.name"
              :value="item.id" />
          </el-select>
        </section>
        <template v-if="!props.disabled">
          <el-button type="primary" :icon="CirclePlus" text @click="addItem"></el-button>
          <el-button
            type="danger"
            :icon="Delete"
            text
            @click="removeItem(index)"
            v-if="props.modelValue.length > 1"></el-button>
        </template>
      </section>
    </el-collapse-item>
  </el-collapse>
</template>
<script lang="ts" setup>
import { AdminModelStatus } from '@/api/adminModel/type'
import { CirclePlus, Delete } from '@element-plus/icons-vue'
const props: any = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  preList: {
    type: Array,
    default: () => []
  },
  afterList: {
    type: Array,
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  }
})
const getDisabled = (option: any, model: string) => {
  if (option.name === model) {
    return false
  }
  return props.modelValue.some((item: any) => item.pre === option)
}
const addItem = () => {
  // eslint-disable-next-line vue/no-mutating-props
  props.modelValue.push({
    pre: '',
    after: ''
  })
}
const removeItem = (index: number) => {
  // eslint-disable-next-line vue/no-mutating-props
  props.modelValue.splice(index, index + 1)
}
</script>
<style lang="less" scoped>
.model-map {
  margin-top: 12px;

  .model-map-item {
    display: flex;
    padding: 6px 24px;
    display: flex;
    align-items: center;

    .el-select {
      width: 230px;
    }

    .el-button {
      margin-left: 0;
      padding: 8px;
    }

    & > section {
      margin-right: 16px;

      span {
        margin-right: 8px;
      }
    }
  }

  :deep(.el-collapse-item__header) {
    height: 36px;
  }

  :deep(.el-collapse-item__content) {
    padding-bottom: 8px;
  }
}
</style>
