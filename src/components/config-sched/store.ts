import { GetPrivatePoolRet } from '@/api/model/type'
import { ModelGroupItem } from '@/api/modelSched/type'

import { omit } from 'lodash-es'
const privatePoolMap: any = ref(new Map<string, GetPrivatePoolRet>())
const updatePrivatePoolMap = (key: number, value: GetPrivatePoolRet) => {
  if (!key) {
    return
  }
  const preMap = privatePoolMap.value
  preMap.set(key, value)
  privatePoolMap.value = preMap
}
const modelList: Ref<Array<ModelGroupItem>> = ref([])
const updateModelList = (list = []) => {
  modelList.value = list
  updateHandsrvMap()
}
const handsrvMap: any = ref(new Map())
const updateHandsrvMap = () => {
  const map = new Map()
  modelList.value.forEach((model) => {
    const handsrvs = model.handSrvs || []
    handsrvs.forEach((srv: any) => {
      const { key, name } = srv
      map.set(key, name)
    })
  })
  handsrvMap.value = map
}
export const getModelInfoById = (
  modelId: number,
  list: any = undefined,
  parent: any = undefined
) => {
  list = list || modelList.value
  let res: any = null
  list.forEach((group: any) => {
    if (res) {
      return
    }
    if (group.id === modelId) {
      res = group
      return
    }
    const models = group.models || []
    if (models && models.length) {
      const parent: any = omit(group, ['models'])
      res = getModelInfoById(modelId, models, parent)
    }
  })
  if (res && parent) {
    res.parent = parent
  }
  return res
}
export const getModelNameById = (modelId: number) => {
  const res = getModelInfoById(modelId) || {}
  return res.name
}
export const getModelSceneById = (modelId: number) => {
  const res = getModelInfoById(modelId) || {}
  return res.scene
}
export const isGroupTab = (modelId: number) => {
  return modelList.value.some((item) => item.id === modelId)
}
export const getChildrenModel = (modelId: number) => {
  const target = getModelInfoById(modelId) || {}
  return target.models || []
}
export const getModelVersions = (modelId: number) => {
  const target = getModelInfoById(modelId)
  if (!target) {
    return []
  }
  const isModelGroup = isGroupTab(modelId)
  if (isModelGroup) {
    return target.models || []
  } else {
    return [
      {
        id: target.id,
        name: target.name
      }
    ]
  }
}
export const getHandsrvNameByKey = (key: string) => {
  const targetMap = handsrvMap.value
  return targetMap.get(key)
}
export const useStore = () => {
  return {
    privatePoolMap,
    updatePrivatePoolMap,
    updateModelList,
    modelList,
    handsrvMap,
    updateHandsrvMap
  }
}
