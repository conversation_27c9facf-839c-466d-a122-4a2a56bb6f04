<template>
  <el-dialog
    fullscreen
    v-model="state.show"
    width="880px"
    :close-on-click-modal="false"
    :show-close="false"
    @close="close()"
    class="config-group-dialog">
    <template #header>
      <section class="flex justify-between items-center">
        <p class="text-lg font-bold">调度配置</p>
        <section>
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="confirm">保存</el-button>
        </section>
      </section>
    </template>
    <section class="flex flex-col">
      <section class="flex items-center p-3">
        <template v-if="isConfigSystemSched">
          <span class="text-sm text-gray-700 mr-2">套餐名称</span>
          <el-input v-model="state.detail.name" class="!w-[200px]"></el-input>
        </template>
        <template v-else>
          <span class="text-sm text-gray-700 mr-2">{{ props.businessName }}业务线默认套餐</span>
          <select-system-sched-package
            v-model="state.detail.useModelSchedPackageId"
            class="!w-[200px]">
          </select-system-sched-package>
        </template>
      </section>
      <section class="flex flex-row !p-0 flex-1 overflow-hidden">
        <section class="min-w-52 flex-shrink-0 flex flex-col py-3 items-center overflow-hidden">
          <el-tabs
            v-if="state.detail.configs && state.detail.configs.length"
            v-model="state.tab"
            tab-position="left"
            class="flex-1">
            <el-tab-pane v-for="tab in state.detail.configs" :key="tab.modelId" :name="tab.modelId">
              <template #label>
                <section class="group flex items-center w-full overflow-hidden px-2">
                  <section class="flex-1 items-center flex justify-end">
                    <span
                      v-show="tab.useModelSchedPackageId < 0"
                      class="text-red-500 inline-block h-6 leading-8 mr-2"
                      >*</span
                    >
                    <span>{{ getModelNameById(tab.modelId) }}</span>
                  </section>
                  <el-dropdown v-if="isGroupTab(tab.modelId)">
                    <el-icon class="el-icon--right relative top-px ml-2">
                      <arrow-down />
                    </el-icon>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item
                          @click="addModel(item)"
                          :key="item.id"
                          v-for="item in getChildrenModel(tab.modelId)"
                          :disabled="state.detail.configs.some((items: any) => items.modelId === item.id)"
                          >{{ item.name }}</el-dropdown-item
                        >
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                  <el-button
                    v-if="isConfigSystemSched || !isGroupTab(tab.modelId)"
                    @click.stop="delModel(tab.modelId)"
                    icon="Delete"
                    type="danger"
                    link
                    class="invisible group-hover:visible flex-shrink-0 ml-1"></el-button>
                </section>
              </template>
            </el-tab-pane>
          </el-tabs>
          <template v-if="isConfigSystemSched">
            <p
              v-if="!state.detail.configs.length"
              class="flex-1 flex justify-center items-center text-gray-500 font-bold text-base">
              点击底部按钮添加模型组
            </p>
            <el-button type="primary" @click="addModelGroup">添加模型组</el-button>
          </template>
        </section>
        <section class="flex flex-col flex-1 overflow-auto p-3" v-if="state.tab">
          <section class="flex items-center p-3" v-if="!isConfigSystemSched">
            <span class="mr-2 flex-shrink-0">选择套餐</span>
            <SelectSystemSchedPackage
              :preOptionsList="preOptionsList"
              v-model="detail.useModelSchedPackageId"
              @update:realDefaultId="updateRealDefaultId">
            </SelectSystemSchedPackage>
          </section>
          <sched-config
            v-if="!isCustom"
            :value="detail"
            :schedId="
              detail.useModelSchedPackageId || state.detail.useModelSchedPackageId || realDefaultId
            "></sched-config>
          <custom-config v-if="isCustom" :value="detail" :modelId="state.tab"></custom-config>
        </section>
      </section>
    </section>
  </el-dialog>
  <el-dialog
    v-model="state.selectModelGroup"
    title="添加模型组"
    width="380px"
    :close-on-click-modal="false">
    <el-select
      placeholder="请选择模型组"
      v-model="selectModelGroupList"
      multiple
      clearable
      filterable>
      <el-option
        v-for="item in modelList"
        :value="item.id"
        :label="item.name"
        :key="item.id"
        :disabled="hasAddedModel(item.id)" />
    </el-select>
    <template #footer>
      <el-button type="primary" @click="confirmSelectModelGroup">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  useAddModelSched,
  useGetModelSchedDetail,
  useGetModelSchedModels,
  useUpdateModelSched
} from '@/api/modelSched'
import { ModelItem, UpdateModelSchedParams } from '@/api/modelSched/type'
import SelectSystemSchedPackage from '@/components/select-system-sched-package/index.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import CustomConfig from './components/custom-config.vue'
import SchedConfig from './components/sched-config.vue'
import { getChildrenModel, getModelNameById, isGroupTab, useStore } from './store'
/** props 定义 */
const props = defineProps({
  id: {
    type: Number,
    default: 0
  },
  businessId: {
    type: Number,
    default: 0
  },
  businessName: {
    type: String,
    default: ''
  },
  condfigType: {
    type: Number,
    default: 1
  }
})
provide('businessId', props.businessId)
/**
 * type = 1 系统调度套餐配置
 * type = 2 业务线调度配置
 */
const isConfigSystemSched = props.condfigType === 1
const preOptionsList = [
  {
    id: 0,
    name: '使用业务线默认套餐'
  },
  {
    id: -1,
    name: '自定义'
  }
]
const { mutate: getScheduleDetail } = useGetModelSchedDetail({
  onSuccess: (data) => {
    const { configs = [] } = data
    state.detail = {
      ...data,
      configs
    }
    state.tab = configs.length ? configs[0].modelId : ''
  }
})
const { modelList, updateModelList } = useStore()
const { mutate: getScheduleModels } = useGetModelSchedModels({
  onSuccess: (data: any) => {
    updateModelList(data.list || [])
  }
})
const state: any = reactive({
  detail: {
    configs: []
  },
  show: true,
  tab: '',
  selectModelGroup: false
})
const realDefaultId = ref(0)
const updateRealDefaultId = (id: number) => {
  realDefaultId.value = id
}

const hasAddedModel = (id: number) => {
  const { configs = [] } = state.detail
  return configs.some((item: any) => item.modelId === id)
}
invoke(() => {
  const params = {
    id: props.id || undefined,
    businessId: props.businessId || undefined
  }
  if (props.id || props.businessId) {
    getScheduleDetail(params)
  }
  getScheduleModels({
    businessId: props.businessId || undefined
  })
})

const selectModelGroupList = ref([])
const addModelGroup = () => {
  selectModelGroupList.value = []
  state.selectModelGroup = true
}
const confirmSelectModelGroup = () => {
  const groups = selectModelGroupList.value || []
  const tabs = state.detail.configs
  groups.forEach((id) => {
    tabs.push({
      modelId: id,
      useModelSchedPackageId: 0,
      handSrvs: []
    })
  })
  state.selectModelGroup = false
}
const addModel = (item: ModelItem) => {
  const { id } = item
  const tabs = state.detail.configs
  tabs.push({
    modelId: id,
    handSrvs: [],
    useModelSchedPackageId: 0
  })
  state.tab = id
}
const delModel = async (modelId: number) => {
  const tabs = state.detail.configs
  const result = await ElMessageBox.confirm('确认删除？', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).catch(() => false)
  if (!result) return
  const index = tabs.findIndex((tab: any) => tab.modelId === modelId)
  tabs.splice(index, 1)
}

const detail: any = computed(() => {
  const target = state.detail.configs.find((tab: any) => tab.modelId === state.tab) || {}
  if (isConfigSystemSched) {
    target.useModelSchedPackageId = -1
  }
  return target
})
const isCustom = computed(() => {
  const res = detail.value.useModelSchedPackageId === -1
  return res
})

const emits = defineEmits(['close'])

/** 确定以及取消按钮 */
const checkData = (data: UpdateModelSchedParams) => {
  const { configs = [], name = '' } = data
  if (isConfigSystemSched && !name) {
    ElMessage.warning({
      message: '请输入调度套餐名称',
      grouping: true
    })
    return false
  }
  const notCoverAll: string[] = []
  const modelMapConfigError: string[] = []
  configs.forEach((tab) => {
    const { handSrvs = [], modelName }: any = tab
    handSrvs.forEach((srv: any) => {
      const { changeModelId, changeModelConfig } = srv
      if (changeModelId && changeModelConfig.length) {
        const passed = changeModelConfig.every((item: any) => item.pre && item.after)
        if (!passed) {
          modelMapConfigError.push(`${modelName}模型映射配置错误`)
        }
      }
    })
    const length = handSrvs.length
    if (length && handSrvs[length - 1].rate !== 100) {
      notCoverAll.push(modelName)
    }
  })
  if (notCoverAll.length) {
    const message = `${notCoverAll.join('、')}配置错误：最后一个调度方的流量占比应为100%`
    ElMessage.warning({
      message,
      grouping: true
    })
    return false
  }
  if (modelMapConfigError.length) {
    const message = modelMapConfigError[0]
    ElMessage.warning({
      message,
      grouping: true
    })
    return false
  }
  return true
}
const { mutate: updateSchedMutate } = useUpdateModelSched({
  onSuccess: () => {
    ElMessage.success({
      message: '调度配置成功',
      duration: 1500
    })
    close(true)
  }
})
const { mutate: addSchedMutate } = useAddModelSched({
  onSuccess: () => {
    ElMessage.success({
      message: '调度配置成功',
      duration: 1500
    })
    close(true)
  }
})
const confirm = async () => {
  const data = {
    type: 2,
    ...state.detail
  }
  const isPassed = checkData(data)
  if (!isPassed) {
    return
  }
  const action = props.id || props.businessId ? updateSchedMutate : addSchedMutate
  await action(data)
}
const close = (refresh = false) => {
  state.show = false
  emits('close', refresh)
}
</script>
<style lang="less">
.config-group-dialog {
  display: flex;
  flex-direction: column;

  .el-dialog__body {
    flex: 1;
    border-top: 1px solid #e7e8e9;
    padding: 0;

    & > section {
      height: 100%;
      overflow: hidden;

      .el-tabs {
        width: 100%;
        margin: 0;

        .el-tabs__content {
          display: none;
        }

        .el-tabs__header {
          margin-right: 0;

          .el-tabs__nav-wrap.is-left {
            padding: 0 !important;

            .el-tabs__nav-prev,
            .el-tabs__nav-next {
              display: none;
            }

            .el-tabs__nav-scroll {
              overflow: auto;
            }

            &::after {
              // display: none;
            }
          }
        }
      }
    }

    .el-tabs__item {
      padding: 0 4px !important;
    }
  }

  .clone-container {
    display: flex;

    section {
      margin: 0 6px;
      border: 1px solid #e1e3e9;
      flex: 1;
      padding: 8px 12px;
      text-align: center;
      cursor: move;
      font-size: 15px;
      font-weight: 500;
    }
  }

  .target-container {
    min-height: 350px;
    // overflow: auto;
    margin-top: 16px;

    &:empty {
      display: flex;
      justify-content: center;
      align-items: center;

      &::before {
        content: '\53ef\62d6\62fd\533a\57df';
        color: #a1a5af;
        font-size: 18px;
        font-weight: 500;
      }
    }
  }
}
</style>
