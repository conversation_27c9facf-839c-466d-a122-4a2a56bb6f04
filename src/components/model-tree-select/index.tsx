import { CompanyList } from '@/hooks/useModelTree'
import { ElTreeSelect } from 'element-plus'
import { StyleValue } from 'vue'
export default defineComponent({
  props: {
    data: Array as PropType<CompanyList>,
    modelValue: Array as PropType<string[]>,
    disabled: Boolean,
    multiple: <PERSON>olean,
    style: Object as PropType<StyleValue>
  },
  emits: ['change'],
  setup(props, { emit }) {
    return () => (
      <ElTreeSelect
        clearable
        data={props.data}
        disabled={props.disabled}
        filterable
        modelValue={props.modelValue}
        multiple={props.multiple}
        onChange={(e: string[]) => emit('change', e)}
        props={{
          label: 'name',
          children: 'modelList',
          disabled: 'disabled'
        }}
        renderAfterExpand={false}
        style={props.style}
      />
    )
  }
})
