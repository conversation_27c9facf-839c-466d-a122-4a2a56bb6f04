import { ElButton, ElMessageBox, type MessageBoxState, type ButtonInstance } from 'element-plus'
export default defineComponent({
  props: {
    confirmText: {
      type: String,
      default: '确认删除?'
    },
    title: {
      type: String,
      default: '警告'
    },
    type: {
      type: String as PropType<MessageBoxState['type']>,
      default: 'warning'
    },
    size: {
      type: String as PropType<ButtonInstance['size']>,
      default: 'default'
    }
  },
  emits: ['click'],
  setup(props, { emit, slots }) {
    const { confirmText, title, type, size } = toRefs(props)
    const click = async () => {
      const result = await ElMessageBox.confirm(confirmText.value, title.value, {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: type.value
      }).catch(() => false)
      if (!result) return
      emit('click')
    }
    return () => (
      <ElButton link onClick={click} size={size.value} type="danger">
        {slots.default ? slots.default() : '删除'}
      </ElButton>
    )
  }
})
