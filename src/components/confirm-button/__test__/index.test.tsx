import { describe, it, expect, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import ConfirmButton from '@/components/confirm-button'
import { ElButton } from 'element-plus'
import { rAF } from '@/test'

const selector = '.el-overlay'
describe('confirm-button', () => {
  it('create', () => {
    const wrapper = mount({
      setup: () => () => <ConfirmButton />
    })
    const ElButtonWrap = wrapper.getComponent(ElButton)
    const ConfirmButtonWrap = wrapper.getComponent(ConfirmButton)
    expect(wrapper.findComponent(ElButton).exists()).toBeTruthy()
    expect(ConfirmButtonWrap.props('size')).toBe('default')
    expect(ElButtonWrap.props('size')).toBe('default')
  })
  describe('click', () => {
    afterEach(async () => {
      document.body.innerHTML = ''
      await rAF()
    })
    it('show MessageBox', async () => {
      const wrapper = mount({
        setup: () => () => <ConfirmButton />
      })
      const ElButtonWrap = wrapper.getComponent(ElButton)
      await ElButtonWrap.trigger('click')
      await rAF()
      expect(document.querySelector(selector)).toBeDefined()
    })
    it('confirm', async () => {
      const wrapper = mount({
        setup: () => () => <ConfirmButton />
      })
      const ElButtonWrap = wrapper.getComponent(ElButton)
      const ConfirmButtonWrap = wrapper.getComponent(ConfirmButton)
      await ElButtonWrap.trigger('click')
      await rAF()
      const btn = document
        .querySelector(selector)!
        .querySelector('.el-button--primary') as HTMLButtonElement
      btn.click()
      await rAF()
      expect(ConfirmButtonWrap.emitted().click).toBeDefined()
    })
    it('cancel', async () => {
      const wrapper = mount({
        setup: () => () => <ConfirmButton />
      })
      const ElButtonWrap = wrapper.getComponent(ElButton)
      const ConfirmButtonWrap = wrapper.getComponent(ConfirmButton)
      await ElButtonWrap.trigger('click')
      await rAF()
      const btn = document.querySelector('.el-message-box__close') as HTMLButtonElement
      btn.click()
      await rAF()
      expect(ConfirmButtonWrap.emitted().click).toBeFalsy()
    })
  })
})
