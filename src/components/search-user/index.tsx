import { useSearchUser } from '@/api/user'
import { SearchUserRet } from '@/api/user/type'
import { ElOption, ElSelect } from 'element-plus'
export default defineComponent({
  props: {
    modelValue: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue'],
  setup(props) {
    const uname = useModel(props, 'modelValue')
    const options = ref<SearchUserRet['list']>([])
    const { mutate } = useSearchUser({ onSuccess: (res) => (options.value = res.list) })
    const remoteMethod = async (keyword: string) => {
      if (!keyword) {
        return
      }
      mutate({
        pinyin: keyword,
        pageSize: 100,
        pageNum: 1
      })
    }
    return () => (
      <ElSelect clearable filterable remote remoteMethod={remoteMethod} v-model={uname.value}>
        {options.value.map((item) => (
          <ElOption key={item.uname} label={`${item.uname}(${item.zhName})`} value={item.uname} />
        ))}
      </ElSelect>
    )
  }
})
