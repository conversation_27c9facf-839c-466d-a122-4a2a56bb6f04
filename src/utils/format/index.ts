import { v4 as uuidv4 } from 'uuid'

/**
 * 转义 HTML 字符
 * @param source
 */
export function encodeHTML(source: string) {
  return source
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
}

/**
 * 判断是否为代码块
 * @param text
 */
export function includeCode(text: string | null | undefined) {
  const regexp = /^(?:\s{4}|\t).+/gm
  return !!(text?.includes(' = ') || text?.match(regexp))
}

/**
 * 复制文本
 * @param options
 */
export function copyText(options: { text: string; origin?: boolean }) {
  const props = { origin: true, ...options }

  let input: HTMLInputElement | HTMLTextAreaElement

  if (props.origin) input = document.createElement('textarea')
  else input = document.createElement('input')

  input.setAttribute('readonly', 'readonly')
  input.value = props.text
  document.body.appendChild(input)
  input.select()
  if (document.execCommand('copy')) document.execCommand('copy')
  document.body.removeChild(input)
}

export const replaceVar = (str: string, map: any) => {
  const result = str.replace(/\$\{([^}]+)\}/g, (match, variable) => {
    variable = variable.trim()
    return map[variable] || match
  })
  return result
}
export const configString2Preset = (inputStr: string) => {
  let system: string[] = []
  let user: string[] = []
  let assistant: string[] = []
  const preset: any = {
    systemMessage: '',
    conversations: []
  }
  const dealMessage = () => {
    // 处理当前所有的存储数据
    if (system.length) preset.systemMessage = system.join('\n')

    if (user.length) {
      preset.conversations.push({
        role: 'user',
        text: user.join('\n'),
        id: `preset-${uuidv4()}`
      })
    }
    if (assistant.length) {
      preset.conversations.push({
        role: 'assistant',
        text: assistant.join('\n'),
        id: `preset-${uuidv4()}`
      })
    }
    system = []
    assistant = []
    user = []
  }
  const inputArr = inputStr.split('\n') // 将输入的字符串按行分割成数组
  let preFlag
  for (let i = 0; i < inputArr.length; i++) {
    // 遍历每一行
    const currentLine = inputArr[i].trim() // 去掉前后空格
    if (currentLine.startsWith('SYSTEM: ')) {
      // 判断是否以'SYSTEM: '开头
      dealMessage()
      system.push(currentLine.split('SYSTEM: ')[1])
      preFlag = system
      continue
    }
    if (currentLine.startsWith('USER: ')) {
      // 判断是否以'USER: '开头
      dealMessage()
      user.push(currentLine.split('USER: ')[1])
      preFlag = user
      continue
    }
    if (currentLine.startsWith('ASSISTANT: ')) {
      // 判断是否以'ASSISTANT: '开头
      dealMessage()
      assistant.push(currentLine.split('ASSISTANT: ')[1])
      preFlag = assistant
      continue
    }
    if (preFlag) {
      // 三个都不是
      preFlag.push(currentLine)
    }
  }
  dealMessage()
  return preset
}

export const formatNumber = (num: number) => {
  if (num < 1000) {
    return num.toString()
  } else if (num < 1000000) {
    return (num / 1000).toFixed(1) + 'k'
  } else if (num < 1000000000) {
    return (num / 1000000).toFixed(1) + 'm'
  } else {
    return (num / 1000000000).toFixed(1) + 'b'
  }
}
