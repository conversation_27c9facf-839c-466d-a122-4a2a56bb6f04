import { isArray } from 'lodash-es'
import COS from 'cos-js-sdk-v5'
export enum STATUS {
  UPLOADING = 1,
  SUCCESS = 2,
  ERROR = 3
}
import { ElMessage } from 'element-plus'

export const downloadFile = (content: string | string[], name: string) => {
  // 创建Blob对象
  const data = isArray(content) ? content : [content]
  const blob = new Blob(data, { type: 'text/plain' })
  // 创建URL并触发下载
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = name
  document.body.appendChild(link)
  link.click()
  // 释放URL对象
  URL.revokeObjectURL(url)
}

const cos = new COS({
  SecretId: 'AKIDMXcUH0UntoX5QZWHqw11gCcu22oKceR0',
  SecretKey: 'RceSzCDA3lP6jBQQCk4BHBsVwA8rMcjc'
})
type UploadFileParams = {
  Body: File
  Key: string
}
const baseConfig = {
  Bucket: 'zyb-llm-1253445850',
  // Bucket: 'zyb-ship-1253445850',
  Region: 'ap-beijing'
}
const folder = 'model'
export const getFullName = (name: string) => {
  return `/${folder}/${name}`
}
export const uploadFile = async (params: UploadFileParams) => {
  await cos.uploadFile(
    {
      ...baseConfig,
      SliceSize: 1024 * 1024 * 1,
      ...params,
      Key: getFullName(params.Key)
    },
    function (err, data) {
      if (err) {
        console.log('上传失败', err)
      } else {
        console.log('上传成功', data)
      }
    }
  )
}

export const uploadFiles = async (files: File[]) => {
  const fileList: any = files.map((file) => {
    return {
      ...baseConfig,
      FilePath: `/${file.name}`,
      Key: file.name,
      Body: file
    }
  })
  await cos.uploadFiles({
    files: fileList,
    SliceSize: 1024 * 1024 * 3 /* 设置大于10MB采用分块上传 */
  })
}

interface UploadActionRet {
  fileName: string
  url: string
}

export const uploadAction = async (data: any) => {
  const formData = new FormData()
  const keys = Object.keys(data)
  keys.forEach((key) => formData.append(key, data[key]))
  const res = await fetch('/openmis/file/upload', {
    method: 'POST',
    body: formData
  })
  if (res.ok) {
    const { errNo = 0, data = {}, errMsg } = await res.json()
    if (errNo !== 0) {
      ElMessage.warning(errMsg)
      throw new Error(errMsg)
    }
    return data as UploadActionRet
  }
}
