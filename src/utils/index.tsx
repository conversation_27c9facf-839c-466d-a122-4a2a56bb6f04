const { VITE_SHIP_APPID, VITE_PROD_APPID } = import.meta.env
// ship环境app id
const shipAppId = VITE_SHIP_APPID || VITE_PROD_APPID
// 线上环境app id
const prodAppId = VITE_PROD_APPID
export const getAppId = () => {
  const appId = location.host.search('yunsizhixue') === -1 ? prodAppId : shipAppId
  return appId
}

export const containsChinese = (text: string) => {
  // 使用正则表达式检查是否包含中文字符
  const pattern = /[\u4e00-\u9fa5]/
  return pattern.test(text)
}

export const getStringLength = (string: string) => {
  const res = string.replace(/[\u0391-\uFFE5]/g, 'aa')
  return res.length
}

/** 获取反值 */
export const getInverseValue = (params: boolean) => !params

export const numberToString = (param: number | undefined) => {
  if (typeof param === 'number') {
    return param.toString()
  }
  return ''
}

export const stringToNumber = (param: string) => {
  if (!param) {
    return undefined
  }
  return +param
}

export const arrayToNumber = (param?: number[]) => param && param[0]

export const numberToArray = (param: number) => [param]

export const suffixWebp = '&imageMogr2/format/webp'
export const videoExtensions = /^(https|http).*\.(mp4|mov|wmv|avi|flv|mkv|webm)/i
export const audioExtensions = /^(https|http).*\.(mp3|wav|ogg|m4a|flac|aac)/i
// 视频文件扩展名列表
export const isVideo = (url: string) => videoExtensions.test(url)

// 音频文件扩展名列表
export const isAudio = (url: string) => audioExtensions.test(url)

export const timeFormat = <T, K>(_row: T, _column: K, time: number) =>
  dayjs.unix(time).format('YYYY-MM-DD HH:mm:ss')
