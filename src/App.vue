<script setup lang="ts">
import useUserStore from '@/store/user'
import useCommonStore from '@/store/common'
import { useLocal } from '@/plugin/locale'
import { useCopyCode } from '@/views/mark/rate/assistantMessage/hooks'
import { KeepAliveRouterView } from '@znzt-fe/components'
const store = useUserStore()
store.getUserInfo()
const commonStore = useCommonStore()
commonStore.getConfig()
const zhCn = useLocal()
useCopyCode()
</script>
<template>
  <div id="app-root">
    <el-config-provider :locale="zhCn">
      <KeepAliveRouterView :include="['top-bottom']"></KeepAliveRouterView>
    </el-config-provider>
  </div>
</template>

<style scoped>
#app-root {
  height: 100%;
  min-width: 1180px;
}

.example-showcase .el-dropdown + .el-dropdown {
  margin-left: 15px;
}

.example-showcase .el-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
}

.add-text {
  background-color: green;
}

.remove-text {
  background-color: red;
}
</style>
