// @import 'vue3-cron/dist/css/index.9dc7bb47.css';

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  background: rgb(238 241 245);
}

p {
  margin: 0;
  padding: 0;
}

#app {
  height: 100%;
}

.flex {
  display: flex;
}

.h-center {
  .flex();
  justify-content: center;
}

.v-center {
  .flex();
  align-items: center;
}

.both-center {
  .h-center();
  .v-center();
}

.space-a {
  .flex();
  justify-content: space-around;
}

.space-b {
  .flex();
  justify-content: space-between;
}

.el-pagination {
  padding: 8px 0;
  justify-content: flex-end;
  align-items: center;
  display: flex;
}

.el-table__cell {
  .el-button {
    margin-left: 0;
    padding: 8px;
  }
}

.icon-button {
  cursor: pointer;
}

.float-l {
  float: left;
}

.el-dialog {
  .el-dialog__body {
    padding-top: 8px;
    max-height: unset;
  }
}

.text-top {
  vertical-align: text-top !important;
}

.flex-wrap {
  display: flex;
  flex-wrap: wrap;
}

.flex-just-end {
  .flex(flex-end);
}
.flex-just-start {
  .flex(flex-start);
}
.flex-just-space {
  .flex(space-between);
}
.flex-just-center {
  .flex(center);
}
.flex-center {
  .flex(center,center);
}

.el-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
}

.cell {
  height: 100% !important;
}
.el-month-table td .cell {
  height: 36px !important;
}

.input-number {
  width: 100% !important;
}

.flex-direction {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
}

.flex-g {
  flex-grow: 1;
}

.abs {
  position: absolute;
}

.el-rate__decimal {
  color: var(--el-rate-fill-color) !important;
}

.el-form-item__content {
  .el-input,
  .el-select,
  .el-input-number {
    @apply w-full;
  }
}

.hidden-upload-trigger {
  .el-upload {
    display: none;
  }
}

.el-message-box__message {
  overflow: auto;
}

.dialog-column {
  display: flex;
  flex-direction: column;
  .el-dialog__body {
    overflow: auto;
    flex-grow: 1;
  }
}

.card-column {
  display: flex;
  flex-direction: column;
  .el-card__body {
    overflow: auto;
    flex-grow: 1;
    display: flex;
  }
}

.bg-search {
  background-color: rgb(254 240 138);
}

.current-search {
  border: 1px solid rgb(248 113 113);
}

.el-form-item {
  &.hidden-label {
    & > label {
      overflow: hidden;
      display: none;
    }
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}

.katex-html {
  display: none;
}

.el-progress-bar__inner {
  transition: none !important;
}

.popoverMinWidth {
  min-width: auto !important;
  width: auto !important;
}
