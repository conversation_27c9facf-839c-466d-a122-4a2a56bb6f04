import { VueQueryPlugin, type VueQueryPluginOptions } from '@znzt-fe/axios'
import { App } from 'vue'
const vueQueryPluginOptions: VueQueryPluginOptions = {
  queryClientConfig: {
    defaultOptions: {
      queries: {
        networkMode: 'always',
        retry: false,
        staleTime: Infinity
      },
      mutations: {
        networkMode: 'always',
        retry: false
      }
    }
  }
}

export default {
  install(app: App) {
    app.use(VueQueryPlugin, vueQueryPluginOptions)
  }
}
