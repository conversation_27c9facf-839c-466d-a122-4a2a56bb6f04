import elementZhCn from 'element-plus/es/locale/lang/zh-cn'
import 'element-plus/es/components/loading/style/css'
export const useLocal = () => {
  const zhCn = ref(elementZhCn)
  const route = useRoute()
  watch(route, (val) => {
    if (val.path.match(/mark\/rate/)) {
      zhCn.value.el.pagination.pagesize = '组/页'
      zhCn.value.el.pagination.total = '共 {total} 组'
    } else {
      zhCn.value.el.pagination.pagesize = '条/页'
      zhCn.value.el.pagination.total = '共 {total} 条'
    }
  })
  return zhCn
}
