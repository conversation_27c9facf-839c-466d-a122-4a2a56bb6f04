import { useBusinessCode } from '@/hooks/useBusinessCode'
import type { AxiosInstance, AxiosResponse } from '@znzt-fe/axios'
import { ElMessage } from 'element-plus'
export const enum ErrNo {
  Success,
  LoginFailed = 1003,
  NoAuth,
  NoDalleAuth = 10000801
}
export const serviceInterceptors = (service: AxiosInstance) => {
  service.interceptors.request.use((config) => {
    const businessCode = useBusinessCode()
    switch (config.method) {
      case 'get':
        config.params = {
          businessCode,
          ...config.params
        }
        break
      case 'post':
        config.data = { businessCode, ...config.data }
    }
    return config
  })
  service.interceptors.response.use(errorHandle, (error) => {
    const message =
      error?.response?.statusText ?? ~error?.message?.search('timeout')
        ? '请求超时，请检查网络状况或重试'
        : '请求失败，请检查网络状况或重试'
    if (error.code !== 'ERR_CANCELED') {
      ElMessage.error({ message, grouping: true })
    }
    throw new Error('ignoreErrors' + message)
  })
}

function errorHandle(response: AxiosResponse) {
  const data = response?.data
  const message = data?.errMsg ?? '请求错误！'
  const status = response.status.toString()
  switch (data.errNo) {
    case ErrNo.Success:
      return data.data
    case ErrNo.LoginFailed:
      window.location.href = data.data.loginUrl
      return new Promise(() => {})
    case ErrNo.NoAuth:
      return response
    case ErrNo.NoDalleAuth:
      ElMessage.error({ message, grouping: true })
      throw new Error('ignoreErrors' + message)
    default:
      ElMessage.error({ message, grouping: true })
      throw new Error(
        `status:${status},msg:${message},requestId:${response.headers['request-id']},url:${response.request.responseURL}`
      )
  }
}

export interface ResponseData<T = any> {
  data: T
  errNo: string
  errMsg: string
}
