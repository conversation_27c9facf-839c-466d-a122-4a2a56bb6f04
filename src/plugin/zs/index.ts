import { zs as pcZs } from '@zyb-data/stats-pc'
import type { NavigationGuardNext, RouteLocationNormalized, RouterHistory } from 'vue-router'
class ZS {
  private debug = import.meta.env.PROD ? false : false // 本地需要验证设置为true
  private isProd = import.meta.env.PROD
  zs: typeof pcZs
  constructor() {
    this.zs = pcZs
    this.setConfig()
  }
  track(key: string, value: Record<string, any> = {}) {
    this.zs.track(key, value)
  }
  login = (uname: string) => this.zs.login(uname)
  init = () => this.zs.init()
  setConfig = () =>
    this.zs.config({
      plat: 'llmPC',
      zpID: 'PNB-LLM-PCW',
      logUrl: this.isProd
        ? 'https://nlogtj.zuoyebang.cc/nlogtj/h5'
        : 'https://nlogtj.zuoyebang.cc/nlogtj/h5_test',
      debug: import.meta.env.DEV && this.debug,
      env: this.isProd ? 'production' : 'development'
    })

  handle(
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext,
    mode: string,
    base: RouterHistory['base']
  ) {
    try {
      let fromRule, toRule
      // hash模式下base需要为空
      base = mode === 'hash' ? '' : base.replace(/\/$/, '')
      if (from.matched.length != 0) {
        fromRule = base + from.matched[from.matched.length - 1].path
      }
      if (to.matched.length != 0) {
        toRule = base + to.matched[to.matched.length - 1].path
      }
      fromRule = fromRule === undefined || fromRule === '' ? '/' : fromRule
      toRule = toRule === undefined || toRule === '' ? '/' : toRule
      this.zs.pageHide(fromRule)
      //展示点位缓存清空
      try {
        this.zs.clearCache()
      } catch (err) {
        console.log(err)
      }
      this.zs.pageShow(toRule)
    } catch (error) {
      console.log(error)
    }
    next()
  }

  /**
   *
   * @param {string} mode vue router的mode
   * @param {string} [base=''] vue router的base
   */
  routeMixin(mode: string, base = '') {
    return {
      beforeEnter: (
        to: RouteLocationNormalized,
        from: RouteLocationNormalized,
        next: NavigationGuardNext
      ) => {
        this.handle(to, from, next, mode, base)
      },
      beforeUpdate: (
        to: RouteLocationNormalized,
        from: RouteLocationNormalized,
        next: NavigationGuardNext
      ) => {
        this.handle(to, from, next, mode, base)
      }
    }
  }
}

const zs = new ZS()
export default zs
