# chat-mis 开发说明

- Vue 3 + TypeScript + Vite + pnpm

## 项目介绍

- LLM 项目

## 运行

### 启动

- pnpm install
- pnpm dev

### 提交

- pnpm commit（已集成 commitlint，cz，husky 等插件），使用此命令后选择本次的 type，scope，并填写本次开发内容说明

### 打包

- pnpm build

## 网络请求部分

### 基本使用

本项目采用 axios 作为基本请求（流式请求可使用 fetch）

1. 位置：项目中需要的请求，在`src/api`文件夹下找到对应的模块进行添加
2. 类型：需要把本次请求的 parmas 和 result 的 type 填写到对应模块的 type.ts 文件中
3. axios 引入：引入`src/plugin/axios`，调用传入本模块的通用 prefix 即可
4. 请求详情：请求方式，url 等放在对应模块的 index.ts 中

开发示例

```typescript
// src/api/marktask/index.ts
import axios from '@/plugin/axios'
import type {
  GetMarkTaskListParams,
  GetMarkTaskListRet,
  MarkTaskOptionsParams,
  MarkTaskOptionsRet
} from './type'
// MutationFn为处理 mutationPost 和 mutationGet的通用类型，泛型传递params和result即可
import type { MutationFn } from '@znzt-fe/declare'
// marktask为本模块通用prefix，比如下方的标注任务列表的url实际为 marktask/list
const { post, mutationPost } = axios('marktask')

/** 标注任务列表 */
export const useGetMarkTaskList = () =>
  mutationPost<GetMarkTaskListParams, GetMarkTaskListRet>('list')

/** 新建标注任务选项 */
export const getMarkTaskOptions = (data: MarkTaskOptionsParams) =>
  post<MarkTaskOptionsRet>('option', data)
```

> tips: mutationPost 和 mutationGet 是通过 `@tanstack/vue-query` 对 axios 请求进行封装，使用方式参考 [vue-query](https://tanstack.com/query/latest/docs/vue/overview)

### 如何使用 mock

仅在 import.meta.env.Prod 为 false 才会生效，生产环境自动忽略 mock
项目中的 mock 采用访问 yapi 的方式，在`@znzt-fe/utils/axios`已经进行了封装，使用方式如下

```typescript
// get
function get<T>(url: string, data?: IParams, mock?: boolean): Promise<T>
function get<T>(url: string, mock: boolean): Promise<T>

// post同get
function post<T>(url: string, data?: IParams, mock?: boolean): Promise<T>
function post<T>(url: string, mock: boolean): Promise<T>
// mutationGet 和 mutationPost options之后的下一个参数传递boolean即可
```

## 开发流程

### 需求评审阶段

阅读当前需求文档初步了解需求 => 产品需求评审会提出问题和建议，明确需求内容 => 排期 => 开始研发

### 需求开发阶段

当前分支为线上分支（master），以及对应需求创立的个人分支。创建格式 (type)-(version||description)-(author)，比如新需求分支，feat-v2.1-likefan，feat-markresult-likefan，hotfix 分支 hotfix-markresult-likefan 公共能力分支 chore-testpublish-likefan

1. 拉取 master 最新的代码

阅读之前的 commit，了解整体更新，一方面了解大致其他需求点，另一方面是公共封装部分，方便之后使用上的统一，避免无效的重复封装

2. 建立个人需求开发分支（禁止在 master 直接开发）

3. 开发完毕后，首先进行自测，自测没有问题，push 代码（在 gitlab 会自动打镜像），在 ship 环境中进行全面自测

commit 提交需要使用 pnpm commit 命令，选择对应的 type（比如 feat），scope（比如标注实验室），以及填写对应的 comment 简单描述本次提交内容

在合并之前

- 需求通过 git rebase -i ^commit-id 合并无用信息，防止 git log 过于混乱
- 再次 rebase master，提前处理开发过程中由于 master 的上线所出现的冲突

4. ship 环境测试出现问题

在自己开发分支 git rebase commit-id^ -i => 找到需要修改的 commit id 把 pick 修改为 edit => 改动完毕之后运行 git add => git commit -amend => git rebase --continue
防止 commit log 有很多 fix 提交（master 的 hotfix 例外）

5. 如果需要和其他前端共同开发一个需求，且需求有较多需要联动测试模块，则在 ship 环境测试时，共同创建一个新的分支，最终上线此分支

> 注意：所有的合并操作，除了合并到 master 需要发送 merge request，其他均使用 rebase 操作，防止出现过多无用的 merge request log，且 git graph 也会更加清晰

### 上线阶段

1. 在 gitlab 上新建 merge request，合并到 master 分支
2. 至少需要一个其他前端进行 code review，并在 merge request 页面点击 approve
3. 点击 merge 按钮，删除原分支
4. cicd 构建成功之后，在 pms 创建对应的需求，选择对应的构建版本号，选择之后查看 diff，是否是自己需要上线的内容。确认无误完成最终上线

## webpro

### 相关资料

[使用能力](https://www.volcengine.com/docs/6431/104862)

> 对接人找 **房敬超** 同学

### 接入配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import { ApmVitePlugin } from '@zyb/apm-plugin'
export default () => {
  const config = {
    plugins: [
      ApmVitePlugin({
        env:'debug' // env分为 debug，prod，test，本地验证设置为debug，此外无需设置
        aid: '', // 根据申请的aid填写
        plugins: {
          pageview: {
            sendInit: true, // true;
            routeMode: 'history',
            // 指定 history 或 hash 变化时如何从 URL 中提取 pid
            extractPid: (url: string) => new URL(url).pathname.replaceAll(/\/\d+/g, '')
          },
          jsError: {
            // 忽略error message匹配得上的错误
            ignoreErrors: [/ignoreErrors/], // [];
            // 是否添加全局 onerror 监听器
            onerror: true, // true;
            // 是否添加全局 onunhandledrejection 监听器
            onunhandledrejection: true, // true,
            // 对前后发生的相同错误，是否去重
            dedupe: true, // true;
            // 将 Native Object 和事件 API（setTimeout、setInterval、requestAnimationFrame、addEventListener/removeEventListener）包装在 try/catch 块中来处理异步异常
            captureGlobalAsync: false // false;
          }
        }
      }),
    ],
    build: {
      // sourcemap必须开启，否则线上报错无法定位回代码
      sourcemap: true
    }
  }
}

```

apm 如何处理 sourcemap？

在项目打镜像过程中，会把 build 出来的.map 文件上传到 apm 服务器，并删除 build 之后的.map 文件防止用户通过 map 反向映射源码（@zyb/apm-plugin@0.0.17 开始 test 环境不会删除.map 文件，方便调试），线上报错则会打到 apm 服务，apm 根据上传好的.map 文件在平台进行源码报错位置展示

## sso

- 需要在 env.production 中配置 VITE_PROD_APPID 和 VITE_SHIP_APPID（如果线上线下环境的 appid 一致 则只需要配置 VITE_PROD_APPID）

## TS 相关使用规定

### 必须使用 ts

- 业务中的 form 表单

good

```typescript
const initData: CreateMarkTaskParams = {
  name: '',
  type: '',
  scoreType: '',
  languageType: '',
  desc: '',
  unames: [],
  models: [],
  deadLine: '',
  businessCode: '',
  uploadId: '',
  standardId: '',
  repetitionRatio: 0,
  questionSetting: [],
  isMarkTag: false
}
const { formRef, validate, clearValidate, validateField, form, resetForm } = useElForm(initData)
```

bad

```typescript
const initData = reactive({
  name: '',
  type: '',
  scoreType: '',
  languageType: '',
  desc: '',
  unames: [], // never[]
  models: [], // // never[]
  deadLine: '',
  businessCode: '',
  uploadId: '',
  standardId: '',
  repetitionRatio: 0,
  questionSetting: [], // never[]
  isMarkTag: false
})
```

- api 接口的 params 和 result

参考`网络请求`部分

- 封装的公共方法，公共 hooks，pinia 等

good

```typescript
/** 装饰器模式 */
export type Fn<K = any> = (...rest: any[]) => Promise<K | void> | K | void
export function decorator<T extends Fn, K extends Fn, P extends Fn, This = any>(
  fn: T,
  options: { beforeFn?: K; afterFn?: P }
) {
  return async function (this: This, ...rest: Parameters<T>) {
    try {
      const bindFn = fn.bind(this)
      if (options?.beforeFn) {
        await Promise.resolve(options.beforeFn())
      }
      const data: ReturnType<T> = await Promise.resolve(bindFn(...rest))
      if (options?.afterFn) {
        await Promise.resolve(options.afterFn(data))
      }
      return data
    } catch {}
  }
}
```

bad

```typescript
const store: any = useStore()
store.xxx()
```

### 推荐使用 ts

- props 的引用数据类型

good

```typescript
const props = defineProps({
  value: {
    type: Object as PropType<{ value: string }>,
    default: () => ({})
  },
  id: {
    type: Number
  }
})
```

bad

```typescript
const props = defineProps({
  value: {
    type: Object,
    default: () => ({})
  },
  id: {
    type: Number
  }
})
```

- 业务中的 hooks
  参考`封装的公共方法，公共 hooks，pinia 等`

## 公共部分发包 npm

比较常用的函数，hooks，declare，lint 配置等，
封装到了 `growthdata-npm-fe` 包 中，对应两个包为 `@znzt-fe/utils`，`@znzt-fe/lint-config`
如有需要封装，请移步[growthdata-npm-fe](https://git.zuoyebang.cc/znzt/growthdata/growthdata-npm-fe)
