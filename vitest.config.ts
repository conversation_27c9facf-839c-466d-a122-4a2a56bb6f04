import { defineConfig } from 'vitest/config'
import config from './vite.config'
export default defineConfig({
  ...config({ mode: 'development' }),
  test: {
    environment: 'jsdom',
    coverage: {
      provider: 'istanbul', // or 'v8'
      include: ['src/components/**']
    },
    css: false,
    server: {
      deps: {
        // https://github.com/vitest-dev/vitest/issues/1388
        inline: ['element-plus', '@znzt-fe/utils']
      }
    }
  }
})
