import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { viteImgToWebp } from '@znzt-fe/webp/vite-plugin-webp'
import { ApmVitePlugin } from '@zyb/apm-plugin'
import autoprefixer from 'autoprefixer'
import path from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import tailwindcss from 'tailwindcss'
import AutoImport from 'unplugin-auto-import/vite'
import ElementPlus from 'unplugin-element-plus/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { UserConfigExport, defineConfig, loadEnv } from 'vite'
import mkcert from 'vite-plugin-mkcert'
export default ({ mode }) => {
  const envMode = mode === 'report' ? 'production' : mode
  const env = loadEnv(envMode, process.cwd())
  const BaseProxy = {
    target: env.VITE_PROXY,
    headers: {},
    changeOrigin: true
  }
  const MockProxy = {
    target: env.VITE_MOCK,
    headers: {},
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/mock/, 'openmis')
  }
  const config: UserConfigExport = {
    css: {
      preprocessorOptions: {
        less: {
          additionalData: `@import "${path.resolve(__dirname, 'src/style/common.less')}";`
        }
      },
      postcss: {
        plugins: [tailwindcss, autoprefixer]
      }
    },
    base: env.VITE_BASE_URL,
    plugins: [
      vue(),
      env.VITE_HOST === 'fe.zuoyebang.cc' && mkcert(),
      ApmVitePlugin({
        id: env.VITE_APP_ID, // 示例：'4eodGC5DZW2rJ2Olbm'
        reportApiSpeed: true, // 接口测速
        reportAssetSpeed: true, // 静态资源测速
        reportImmediately: false, // 不立即上报
        // env: 'debug', // 本地测试需设置为'debug'
        // sdkUrl: 'https://tam.cdn-go.cn/aegis-sdk/latest/aegis.min.js', // 指定sdk的cdn链接

        // 上传sourcemap需要以下三个字段，任一字段为空都不会收集map文件上传
        // 修改为自己的项目id
        projectId: env.VITE_PROJECT_ID,
        // 专用于sourcemap上传的子账号密钥id
        secretId: env.VITE_SOURCEMAP_SECRET_ID,
        // 专用于sourcemap上传的子账号密钥key
        secretKey: env.VITE_SOURCEMAP_SECRET_KEY,
        spa: true
      }),
      vueJsx(),
      AutoImport({
        resolvers: [ElementPlusResolver()],
        include: [/\.[tj]sx?$/, /\.vue$/],
        imports: [
          'vue',
          {
            vue: ['useModel', 'withModifiers']
          },
          'vue-router',
          'pinia',
          '@vueuse/core',
          {
            '@vueuse/core': ['invoke']
          },
          {
            classnames: [['default', 'cx']]
          },
          {
            dayjs: [['default', 'dayjs']]
          },
          {
            qs: [['default', 'qs']]
          },
          {
            'decimal.js': [['default', 'Decimal']]
          }
        ],
        dts: './auto-imports.d.ts',
        eslintrc: { enabled: true }
      }),
      Components({
        resolvers: [ElementPlusResolver()]
      }),
      ElementPlus({
        ignoreComponents: ['LoadingDirective']
      }),
      viteImgToWebp()
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    },
    build: {
      sourcemap: true
    },
    server: {
      open: true,
      host: env.VITE_HOST,
      // https: true,
      proxy: {
        '/openmis': BaseProxy,
        '/mock': MockProxy
      }
    }
  }
  if (mode === 'report') {
    config.plugins.push(
      visualizer({
        gzipSize: true,
        brotliSize: true,
        emitFile: false,
        filename: 'report.html', //分析图生成的文件名
        open: true //如果存在本地服务端口，将在打包后自动展示
      })
    )
  }
  if (mode === 'production') {
    config.esbuild = {
      drop: ['console', 'debugger']
    }
  }
  return defineConfig(config)
}
