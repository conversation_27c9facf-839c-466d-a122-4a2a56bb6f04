FROM image-docker.zuoyebang.cc/base/node:18-slim as builder
WORKDIR /output
WORKDIR /home/<USER>/
# ------- 增加下边这两句，如果没有包的修改，install可以使用缓存（需要每个项目Owner在项目中增加）
COPY package.json /home/<USER>/package.json
COPY pnpm-lock.yaml /home/<USER>/pnpm-lock.yaml
# RUN corepack enable pnpm
RUN npm install -g pnpm
RUN pnpm config set registry https://ued.zuoyebang.cc/npm/
RUN pnpm install
COPY . /home/<USER>/
ARG CI_FE_DEBUG
ARG REPO_GIT_REMOTE_ADDRESS
ARG CI_COMMIT_SHA
RUN if [ "$CI_FE_DEBUG" = "true" ] ; then npm run build:test ; else npm run build:online ; fi

# 运行
FROM image-docker.zuoyebang.cc/privbase/fe-nginx:1.2.9
ARG APP_NAME
ENV APP_NAME $APP_NAME
ARG REPO_NAME
ENV REPO_NAME $REPO_NAME
# 仅用于通用的前端，部分前端视情况来组织目录结构
COPY --from=builder /home/<USER>/dist/ /home/<USER>/www/chat-mis/
# 用于前端内部rewrite规则配置
COPY ./location.d/ /etc/nginx/location.d/
